import knex from "../../../knex/knex";
import { GraphQLError } from "graphql";
import { QueryInvoiceItemsArgs } from "../../generated/graphql";
import { populateInvoiceItemsUOMs } from "../../services/itemService/uomService";

const invoiceItems = async (_, args: QueryInvoiceItemsArgs) => {
  const { orderId, invoiceId } = args;
  if (orderId && invoiceId) {
    throw new GraphQLError("Only one of orderId or invoiceId is required", {
      extensions: { code: "BAD_USER_INPUT" },
    });
  }

  let items;
  if (invoiceId) {
    items = await knex("invoice_item")
      .select("invoice_item.*")
      .where("invoice_id", invoiceId)
      .orderBy("id", "asc");
  } else if (orderId) {
    items = await knex
      .queryBuilder()
      .select("invoice_item.*")
      .from("invoice_item")
      .join("invoice", "invoice.id", "=", "invoice_item.invoice_id")
      .where("invoice.order_id", orderId)
      .orderBy("id", "asc");
  } else {
    throw new GraphQLError("One of orderId or invoiceId is required", {
      extensions: { code: "BAD_USER_INPUT" },
    });
  }

  // Populate UOM data for invoice items
  await populateInvoiceItemsUOMs(items);
  return items;
};

export default invoiceItems;
