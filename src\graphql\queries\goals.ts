import { QueryGoalsArgs } from "../../generated/graphql";
import goalService from "../../services/goalService";

export default async (_: any, { goalsInput }: QueryGoalsArgs) => {
  try {
    const result = await goalService.getGoals(goalsInput);

    const goalsWithStatus = result.goals.map((goal) => {
      const status = goalService.getGoalStatus(goal, goal.assignments);
      const availablePeriods = goalService.getAvailablePeriods(goal);

      return {
        ...goal,
        status: status.status,
        available_periods: availablePeriods,
      };
    });

    return {
      goals: goalsWithStatus,
      totalCount: result.totalCount,
    };
  } catch (error) {
    console.error("Error fetching goals:", error);
    throw new Error("Failed to fetch goals");
  }
};
