import { GraphQLError } from "graphql";
import { MutationAddInvoiceItemsArgs } from "../../generated/graphql";
import addInvoiceItemsHelper from "../../services/invoiceService/addInvoiceItems";

const addInvoiceItems = async (_, args: MutationAddInvoiceItemsArgs) => {
  const { invoice_id: invoiceId, invoice_items: items } =
    args.addInvoiceItemsInput;
  if (!items) {
    throw new GraphQLError("Invoice items are required", {
      extensions: { code: "BAD_USER_INPUT" },
    });
  }
  if (!invoiceId) {
    for (const item of items) {
      if (!item.invoice_id) {
        throw new GraphQLError("Invoice ID is required", {
          extensions: { code: "BAD_USER_INPUT" },
        });
      }
    }
  }

  return await addInvoiceItemsHelper(invoiceId, items, true);
};

export default addInvoiceItems;
