import { getCustomUOMPrices } from "../../services/itemService/uomService";

export const customUOMPrices = async (
  _: any,
  {
    itemId,
    userId,
    supplierId,
  }: { itemId: string; userId: string; supplierId: string }
) => {
  try {
    return await getCustomUOMPrices(itemId, userId, supplierId);
  } catch (error) {
    console.error("Error fetching custom UOM prices:", error);
    throw error;
  }
};
