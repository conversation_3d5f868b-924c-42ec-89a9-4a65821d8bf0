import knex from "../../../knex/knex";
import addLastOrderedDate from "./addLastOrderedDate";
import { getRecommendedItemsForStore } from "./getRecommendedItemsForStore";
import { getTrendingItems } from "./getTrendingItems";
import { getItemsDetails } from "./getItemsDetails";
import { getSuppliers } from "../supplierService/getSuppliers";
import modifyToCustomPricing from "./modifyToCustomPricing";
import { getHiddenProductIds } from "./customerHiddenProductsService";
import { addFavoriteStatusToItems, getFavoriteItems } from "../favoriteService";
import { getItems } from "./itemService";
import { getSupplierFromUser } from "../supplierService/supplierService";

// TODO: make item blacklists first class
const newItemsBlocklist = [393486];

const getItemsBySection = async (userId, value, offset = 0, limit = 20) => {
  let sortedItems = [];
  let supplierNames = [];

  const supplier = await getSupplierFromUser(userId);

  // Get hidden product IDs if userId is provided
  let hiddenProductIds: string[] = [];
  if (userId) {
    const supplierInfo = await knex("supplier_times")
      .select("supplier_id")
      .where("business_id", userId)
      .first();

    if (supplierInfo) {
      hiddenProductIds = await getHiddenProductIds(
        supplierInfo.supplier_id.toString(),
        userId
      );
    }
  }

  if (value === "favorites") {
    sortedItems = await getFavoriteItems(userId, null, offset, limit);

    sortedItems = sortedItems.map((item) => ({ ...item, isFavorited: true }));

    await modifyToCustomPricing(sortedItems, userId);
    sortedItems = await addLastOrderedDate(sortedItems, userId);

    return sortedItems;
  } else if (value === "order_again") {
    let query = knex
      .select("order_item.item_id", "item.*")
      .sum({ sum: "order_item.quantity" })
      .from("order_detail")
      .join("order_item", "order_item.order_id", "order_detail.id")
      .join("item", "item.id", "order_item.item_id")
      .where("user_id", userId)
      .where("item.archived", false);

    if (hiddenProductIds.length > 0) {
      query = query.whereNotIn("item.id", hiddenProductIds);
    }

    sortedItems = await query
      .groupBy("order_item.item_id", "item.id")
      .orderBy("sum", "desc")
      .limit(limit)
      .offset(offset);

    sortedItems = await addFavoriteStatusToItems(sortedItems, userId);
    await modifyToCustomPricing(sortedItems, userId);
    sortedItems = await addLastOrderedDate(sortedItems, userId);
    return sortedItems;
  } else if (
    value === "top_sellers" ||
    value === "recommended" ||
    value === "trending" ||
    value === "new_items" ||
    value === "on_sale" ||
    value === "back_in_stock"
  ) {
    supplierNames = await getSuppliers(userId);
    if (value === "new_items") {
      const fourteenDaysAgo = new Date(Date.now() - 14 * 24 * 60 * 60 * 1000);

      if (supplierNames.length > 0) {
        let query = knex("item")
          .select("*")
          .where("local_item", "=", false)
          .where("outdated", "=", false)
          .where("archived", "=", false)
          .where("supplier", supplierNames[0])
          .where("created_at", ">=", fourteenDaysAgo)
          .whereNotNull("image");

        if (hiddenProductIds.length > 0) {
          query = query.whereNotIn("id", hiddenProductIds);
        }

        sortedItems = await query.limit(limit).offset(0);

        sortedItems = await addFavoriteStatusToItems(sortedItems, userId);
        await modifyToCustomPricing(sortedItems, userId);
        sortedItems = await addLastOrderedDate(sortedItems, userId);

        return sortedItems.filter(
          (item) => !newItemsBlocklist.includes(item.id)
        );
      }
    }

    if (value === "on_sale") {
      if (supplierNames.length > 0) {
        let query = knex("item")
          .select("*")
          .where("local_item", "=", false)
          .where("outdated", "=", false)
          .where("archived", "=", false)
          .where("supplier", supplierNames[0])
          .whereNotNull("discounted_price");

        if (hiddenProductIds.length > 0) {
          query = query.whereNotIn("id", hiddenProductIds);
        }

        sortedItems = await query.limit(limit).offset(0);

        sortedItems = await addFavoriteStatusToItems(sortedItems, userId);
        await modifyToCustomPricing(sortedItems, userId);
        sortedItems = await addLastOrderedDate(sortedItems, userId);
        return sortedItems;
      }
    }

    if (value === "back_in_stock") {
      console.log("supplierNames", supplierNames);

      if (supplierNames.length > 0) {
        sortedItems = await knex("item")
          .select("*")
          .where("local_item", "=", false)
          .where("outdated", "=", false)
          .where("archived", "=", false)
          .where("supplier", supplierNames[0])
          .whereRaw("back_in_stock_date >= NOW() - INTERVAL '7 days'")
          .limit(limit)
          .offset(0);

        sortedItems = await addFavoriteStatusToItems(sortedItems, userId);
        await modifyToCustomPricing(sortedItems, userId);
        sortedItems = await addLastOrderedDate(sortedItems, userId);

        return sortedItems;
      }
    }

    if (value === "top_sellers") {
      let query = knex
        .select("order_item.item_id", "item.*")
        .count({ count: "order_item.item_id" })
        .sum({ sum: "order_item.quantity" })
        .from("order_detail")
        .join("order_item", "order_item.order_id", "order_detail.id")
        .join("item", "item.id", "order_item.item_id")
        .where("single_supplier", supplierNames[0])
        .where("item.archived", false);

      // Filter out hidden products if any exist
      if (hiddenProductIds.length > 0) {
        query = query.whereNotIn("item.id", hiddenProductIds);
      }

      sortedItems = await query
        .groupBy("order_item.item_id", "item.id")
        .orderBy("count", "desc")
        .orderBy("sum", "desc")
        .limit(limit)
        .offset(offset);

      sortedItems = await addFavoriteStatusToItems(sortedItems, userId);
      await modifyToCustomPricing(sortedItems, userId);
      sortedItems = await addLastOrderedDate(sortedItems, userId);

      return sortedItems;

    } else if (value === "recommended") {
      const recs = await getRecommendedItemsForStore(
        userId,
        null,
        hiddenProductIds
      );

      sortedItems = await getItemsDetails(recs, supplierNames);

      if (sortedItems.length < 10) {
        let query = knex("item")
          .select("*")
          .where("supplier", supplierNames[0])
          .where("local_item", "=", false)
          .where("outdated", "=", false)
          .where("archived", "=", false)
          .whereNotNull("image");

        // Filter out hidden products if any exist
        if (hiddenProductIds.length > 0) {
          query = query.whereNotIn("id", hiddenProductIds);
        }

        const additionalItems = await query.limit(limit).offset(20);

        sortedItems = [...sortedItems, ...additionalItems];
      }

      sortedItems = await addFavoriteStatusToItems(sortedItems, userId);
      sortedItems = await addLastOrderedDate(sortedItems, userId);
    } else {
      const trendingItems = await getTrendingItems(hiddenProductIds);
      sortedItems = await getItemsDetails(trendingItems, supplierNames);

      sortedItems = await addFavoriteStatusToItems(sortedItems, userId);
    }
  }

  const { items } = await getItems({
    supplierId: supplier.supplier_id,
    userId,
    filters: {
      ids: sortedItems.map((item) => item.id),
    },
  });

  return items;
};

export default getItemsBySection;
