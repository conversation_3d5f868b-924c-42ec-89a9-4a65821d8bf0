"use strict";

var dbm;
var type;
var seed;

/**
 * We receive the dbmigrate dependency from dbmigrate initially.
 * This enables us to not have to rely on NODE_PATH.
 */
exports.setup = function (options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

exports.up = function (db) {
  return db
    .createTable("credit", {
      // Create "credits" table
      id: { type: "int", primaryKey: true, autoIncrement: true },
      total: { type: "decimal", precision: 10, scale: 2 },
      cash_amount: {
        type: "decimal",
        precision: 10,
        scale: 2,
        defaultValue: 0,
      },
      invoice_id: {
        type: "int",
        notNull: false,
        foreignKey: {
          name: "fk_credits_to_invoice",
          table: "invoice",
          mapping: "id",
          rules: {
            onDelete: "SET NULL",
          },
        },
      },
      order_id: {
        type: "int",
        notNull: false,
        foreignKey: {
          name: "fk_credits_to_order",
          table: "order_detail",
          mapping: "id",
          rules: {
            onDelete: "SET NULL",
          },
        },
      },
      order_number: { type: "int", notNull: false },
      credit_number: { type: "int", notNull: true },
      user_id: {
        type: "int",
        foreignKey: {
          name: "fk_credits_to_user",
          table: "attain_user",
          mapping: "id",
          rules: {
            onDelete: "CASCADE",
          },
        },
      },
      supplier_id: {
        type: "int",
        foreignKey: {
          name: "fk_credits_to_supplier",
          table: "supplier",
          mapping: "id",
          rules: {
            onDelete: "CASCADE",
          },
        },
      },
      created_at: {
        type: "timestamp",
        defaultValue: new String("CURRENT_TIMESTAMP"),
      },
      updated_at: {
        type: "timestamp",
        defaultValue: new String("CURRENT_TIMESTAMP"),
      },
      status: { type: "string", length: 32, defaultValue: "pending" }, // pending, approved, rejected
      archived: { type: "boolean", defaultValue: false },
    })
    .then(() =>
      Promise.all([
        // Add indices for credit table
        db.addIndex(
          "credit",
          "idx_credit_user_supplier",
          ["user_id", "supplier_id"],
          false
        ),
        db.addIndex("credit", "idx_credit_status", ["status"], false),
        db.addIndex("credit", "idx_credit_created_at", ["created_at"], false),
        db.addIndex(
          "credit",
          "uq_supplier_credit_number",
          ["supplier_id", "credit_number"],
          true
        ),
        db.addIndex("credit", "idx_credit_order", ["order_id"], false),
        db.addIndex(
          "credit",
          "idx_credit_order_number",
          ["order_number"],
          false
        ),
      ])
    )
    .then(() =>
      db.createTable("credit_item", {
        // Create "credit_items" table, depends on "credits" table FK
        id: { type: "int", primaryKey: true, autoIncrement: true },
        credit_id: {
          type: "int",
          foreignKey: {
            name: "fk_credit_items_to_credits",
            table: "credit",
            mapping: "id",
            rules: {
              onDelete: "CASCADE",
            },
          },
        },
        item_id: {
          type: "int",
          foreignKey: {
            name: "fk_credit_items_to_items",
            table: "item",
            mapping: "id",
            rules: {
              onDelete: "SET NULL",
            },
          },
        },
        quantity: { type: "int" },
        reason: { type: "string", length: 32 }, // damaged, expired, missing, overage, other
        note: { type: "text" },
        unit_price: { type: "decimal", precision: 10, scale: 2 },
        total_price: { type: "decimal", precision: 10, scale: 2 },
        invoice_item_id: {
          type: "int",
          foreignKey: {
            name: "fk_credit_items_to_invoice_items",
            table: "invoice_item",
            mapping: "id",
            rules: {
              onDelete: "SET NULL",
            },
          },
        },
        item_snapshot: { type: "jsonb" },
      })
    )
    .then(() =>
      Promise.all([
        // Add indices for credit_item table
        db.addIndex(
          "credit_item",
          "idx_credit_item_credit",
          ["credit_id"],
          false
        ),
        db.addIndex("credit_item", "idx_credit_item_item", ["item_id"], false),
        db.addIndex("credit_item", "idx_credit_item_reason", ["reason"], false),
      ])
    )
    .then(() =>
      db.createTable("credit_image", {
        id: { type: "int", primaryKey: true, autoIncrement: true },
        credit_id: {
          type: "int",
          foreignKey: {
            name: "fk_credit_images_to_credits",
            table: "credit",
            mapping: "id",
            rules: {
              onDelete: "CASCADE",
            },
          },
        },
        url: { type: "string", length: 512, notNull: true },
        created_at: {
          type: "timestamp",
          defaultValue: new String("CURRENT_TIMESTAMP"),
        },
      })
    )
    .then(() =>
      Promise.all([
        // Add indices for credit_image table
        db.addIndex(
          "credit_image",
          "idx_credit_image_credit",
          ["credit_id"],
          false
        ),
        db.addIndex(
          "credit_image",
          "idx_credit_image_created",
          ["created_at"],
          false
        ),
      ])
    );
};

exports.down = function (db) {
  return db
    .dropTable("credit_item")
    .then(() => db.dropTable("credit_image"))
    .then(() => db.dropTable("credit")); // Drop "credits" table last due to dependencies
};

exports._meta = {
  version: 2,
};
