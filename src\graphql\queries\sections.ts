import { QuerySectionsArgs, Section } from "../../generated/graphql";
import getItemsBySection from "../../services/itemService/getItemsBySection";
import { populateItemsUOMs } from "../../services/itemService/uomService";
import knex from "../../../knex/knex";

const sectionMap = {
  favorites: "Favorites",
  new_items: "New Items",
  on_sale: "On Sale",
  order_again: "Order Again",
  top_sellers: "Top Sellers",
  recommended: "Recommended For You",
  back_in_stock: "Back In Stock",
};

const Sections = async (_, args: QuerySectionsArgs): Promise<Section[]> => {
  try {
    const { userId, sections, pagination } = args.getSectionsInput;
    const { offset = 0, limit = 10 } = pagination || {};

    // Get supplier ID for UOM population
    let supplierId: string | null = null;
    if (userId) {
      const supplierInfo = await knex("supplier_times")
        .select("supplier_id")
        .where("business_id", userId)
        .first();
      if (supplierInfo) {
        supplierId = supplierInfo.supplier_id.toString();
      }
    }

    // Process each section
    const sectionResults: Section[] = await Promise.all(
      (sections ? sections : Object.keys(sectionMap)).map(async (section) => {
        try {
          const items = await getItemsBySection(userId, section, offset, limit);

          // Populate UOM data for items
          if (supplierId && items.length > 0) {
            await populateItemsUOMs(items, supplierId);
          }

          return {
            name: sectionMap[section],
            value: section,
            items: items,
          };
        } catch (error) {
          console.error(`Error processing section ${section}:`, error);
          // Return empty items for this section rather than failing the whole query
          return {
            name: sectionMap[section],
            value: section,
            items: [],
          };
        }
      })
    );

    return sectionResults.filter((section) => section.items.length > 0);
  } catch (error) {
    console.error("Error in Sections resolver:", error);
    throw error; // Re-throw to let Apollo handle the error response
  }
};

export default Sections;
