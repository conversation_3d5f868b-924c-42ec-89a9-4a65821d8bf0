import axios from "axios";
import { QueryUploadedOrderCsvArgs } from "../../generated/graphql";

const UploadedOrderCsv = async (_, args: QueryUploadedOrderCsvArgs) => {
  const { embedId, sheetId } = args;
  const headers = {
    "X-API-KEY": "VxEo6UPx+60gsmMkp/4xXYZD+Eug+51ZobpLk7MzUTeDgGYyveZBR4NOnXGx",
    accept: "application/json",
    "content-type": "application/json",
  };

  const params = {
    embed_id: embedId,
  };

  try {
    const result = await axios.post(
      `https://api.oneschema.co/v1/sheets/${sheetId}/export-file/csv`,
      params,
      { headers: headers }
    );
    return result.data;
  } catch (error) {
    console.log(error);
  }

  // setEmbed(result.data)
  // setIsOpen(true)
};

export default UploadedOrderCsv;
// export {Orders, UserOrders}
