import knex from "../../../knex/knex";

// TODO: We should createa actual service functions that transform from
// row data into the graphql types.

export const getInvoiceByOrderId = async (orderId: string) => {
  const invoice = await knex("invoice").where("order_id", orderId).first();
  return invoice;
};

export const getInvoice = async (invoiceId: string) => {
  const invoice = await knex("invoice").where("id", invoiceId).first();
  return invoice;
};
