import algoliasearch from "algoliasearch";
import { algoliaConfig } from "../../config/environment";
import { Item } from "../../generated/graphql";

const client = algoliasearch(algoliaConfig.appId, algoliaConfig.apiKey);
const index = client.initIndex("item-index");

const updateOrAddItems = async (items: Item[]) => {
  const itemsToIndex = items.map((item) => ({
    ...item,
    objectID: item.id.toString(),
  }));
  return await index.partialUpdateObjects(itemsToIndex, {
    createIfNotExists: true,
  });
};

const deleteItems = async (itemIds: string[]) => {
  return await index.deleteObjects(itemIds);
};

export { updateOrAddItems, deleteItems };
