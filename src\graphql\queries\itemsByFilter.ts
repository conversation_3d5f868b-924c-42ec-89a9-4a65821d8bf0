import knex from "../../../knex/knex";
import { Item, QueryItemsByFilterArgs } from "../../generated/graphql";
import addLastOrderedDate from "../../services/itemService/addLastOrderedDate";
import modifyToCustomPricing from "../../services/itemService/modifyToCustomPricing";
import { getHiddenProductIds } from "../../services/itemService";

const ItemsByFilter = async (_, args: QueryItemsByFilterArgs) => {
  const { userId, category, tag, pagination } = args.getItemsByFilterInput;

  const { offset, limit } = pagination;
  const suppliers = await knex("supplier_times")
    .select("*")
    .where("business_id", userId)
    .join("supplier", "supplier_times.supplier_id", "supplier.id");

  // Get hidden product IDs if userId is provided
  let hiddenProductIds: string[] = [];
  if (userId && suppliers.length > 0) {
    hiddenProductIds = await getHiddenProductIds(
      suppliers[0].supplier_id.toString(),
      userId
    );
  }

  const tagMap = {
    marlboro: ["%marlboro%"],
    american_spirit: ["%amer%"],
    camel: ["%camel%"],
    newport: ["%newport%"],
    lucky_strike: ["%lucky_strike%"],
    pall_mall: ["%pall_mall%"],
    swisher_sweet: ["%swisher%"],
    dutch: ["%dutch%"],
    fortuna: ["%fortuna%"],
    bon_appetite: ["%B/A%", "Bon Appetite"],
    lays: ["%lays%"],
    taki: ["%taki%"],
    trident: ["%trident%", "wrigleys"],
    doublemint: ["%doublemint%"],
    orbit: ["%orbit%"],
    mentos: ["%mentos%"],
    stride: ["%stride%"],
    sprite: ["%sprite%"],
    gatorade: ["%gatorade%"],
    powerade: ["%powerade%"],
    arizona: ["%arizona%"],
    snapple: ["%snapple%"],
    bandaid: ["%bandaid%"],
    allegra: ["%allegra%"],
    tylenol: ["%tylenol%"],
    nyquil: ["%nyquil%"],
    advil: ["%advil%"],
    detergent: ["%detergent%"],
    dish_soap: ["%dish_soap%"],
    bleach: ["%bleach%"],
    ariel: ["%ariel%"],
    lirio: ["%lirio%"],
    tide: ["%tide%"],
    clorox: ["%clorox%"],
    mr_clean: ["%mr_clean%"],
    lysol: ["%lysol%"],
    airwick: ["%airwick%"],
    glad: ["%glad%"],
    dawn_ultra: ["%dawn_ultra%"],
    downy: ["%downy%"],
    cloralen: ["%cloralen%"],
    lucky: ["%lucky%"],
  };

  let result = knex
    .select()
    .table("item")
    .where("supplier", suppliers[0].name)
    .whereNot("archived", true);

  // Filter out hidden products if any exist
  if (hiddenProductIds.length > 0) {
    result = result.whereNotIn("id", hiddenProductIds);
  }

  if (category) {
    result = result.where((whereBuilder) =>
      whereBuilder
        .whereRaw("nacs_category ilike ?", category)
        .orWhereRaw("nacs_subcategory ilike ?", category)
    );
  }

  if (tag) {
    result = result.whereRaw("name ilike ANY(?)", [tagMap[tag]]);
  }

  const items: Item[] = await result
    .orderBy("name", "asc")
    .limit(limit)
    .offset(offset);

  if (userId) {
    await modifyToCustomPricing(items, userId);
    return addLastOrderedDate(items, userId);
  }

  return items;
};

export default ItemsByFilter;
