import { Knex } from "knex";

export async function seed(knex: Knex): Promise<void> {
  // Deletes ALL existing entries
  await knex("attain_user").del();

  // Inserts seed entries
  await knex("attain_user").insert([
    {
      name: "test1",
      email: "<EMAIL>",
      store_id: 1,
      buyer_id: "test1_buyer_id",
      user_name: "test1_user_name",
      address: "test1_address",
    },
    {
      name: "test2",
      email: "<EMAIL>",
      store_id: 2,
      buyer_id: "test2_buyer_id",
      user_name: "test2_user_name",
      address: "test2_address",
    },
  ]);
}
