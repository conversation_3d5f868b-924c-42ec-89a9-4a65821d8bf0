import axios from "axios";
import knex from "../../../knex/knex";
import { SyncStatus, SyncStatusItem } from "../../generated/graphql";
import {
  markInProgress,
  markCompleted,
  markFailed,
  listStatusesBySupplier,
} from "./syncStatusService";

const SYNC_ENDPOINT = "https://attain-server.onrender.com/api/sync";

const getSupplierName = async (supplierId: string): Promise<string> => {
  const supplier = await knex("supplier")
    .select("name")
    .where("id", supplierId)
    .first();
  return supplier?.name;
};

const executeSyncRequest = async (
  type: string,
  supplierName: string,
  supplierId: string
): Promise<void> => {
  // Set IN_PROGRESS first so other requests back off
  const startedAt = new Date();
  const recordId = await markInProgress(supplierId, type);

  try {
    await axios.post(
      `${SYNC_ENDPOINT}/${type.toLowerCase()}/${encodeURIComponent(
        supplierName
      )}`
    );

    console.log("sync completed", type, supplierName, supplierId);
    await markCompleted(supplierId, type, startedAt, recordId);
  } catch (error: any) {
    console.log("sync failed", type, supplierName, supplierId);
    await markFailed(supplierId, type, startedAt, error, recordId);
  }
};

export const startSync = async (
  supplierId: string,
  types: string[]
): Promise<boolean> => {
  try {
    console.log("starting sync", supplierId, types);

    const supplierName = await getSupplierName(supplierId);
    if (!supplierName) {
      console.error("Supplier not found", supplierId);
      return false;
    }

    // normalize
    let normalizedSupplierName = supplierName;
    if (supplierName === "Whitestone Foods") {
      normalizedSupplierName = "whitestone";
    } else {
      throw new Error("Invalid supplier name");
    }

    const current = await listStatusesBySupplier(supplierId);
    const inProgressTypes = types.filter((t) =>
      current.some(
        (row) => row.type === t && row.status === SyncStatus.InProgress
      )
    );
    if (inProgressTypes.length) {
      console.log(
        "Sync already in progress for:",
        inProgressTypes,
        "supplier:",
        supplierId
      );
      return true;
    }

    await Promise.all(
      types.map((type) =>
        executeSyncRequest(type, normalizedSupplierName, supplierId)
      )
    );

    return true;
  } catch (err) {
    console.error("Error starting sync:", err);
    return false;
  }
};

export const getSyncStatus = async (
  supplierId: string
): Promise<SyncStatusItem[]> => {
  return listStatusesBySupplier(supplierId);
};
