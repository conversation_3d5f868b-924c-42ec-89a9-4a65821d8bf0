import { QueryTagsArgs } from "../../generated/graphql";

const Tags = async (_, args: QueryTagsArgs) => {
  const { category } = args.getTagsInput;

  const categoryMap = {
    tobacco: [
      {
        name: "Marlboro",
        value: "marlboro",
      },
      {
        name: "American Spirit",
        value: "american_spirit",
      },
      {
        name: "Camel",
        value: "camel",
      },
      {
        name: "Newport",
        value: "newport",
      },
      {
        name: "Lucky Strike",
        value: "lucky_strike",
      },
      {
        name: "Pall Mall",
        value: "pall_mall",
      },
      {
        name: "Swisher Sweet",
        value: "swisher_sweet",
      },
      {
        name: "Dutch",
        value: "dutch",
      },
      {
        name: "Fortuna",
        value: "fortuna",
      },
      {
        name: "Double Diamond",
        value: "double diamond",
      },
    ],
    snack: [
      {
        name: "Lays",
        value: "lays",
      },
      {
        name: "<PERSON><PERSON>",
        value: "taki",
      },
    ],
    pastry: [
      {
        name: "Bon Appetite",
        value: "bon_appetite",
      },
    ],
    gum: [
      {
        name: "Trident",
        value: "trident",
      },
      {
        name: "Doublemint",
        value: "doublemint",
      },
      {
        name: "Orbit",
        value: "orbit",
      },
      {
        name: "Mentos",
        value: "mentos",
      },
      {
        name: "Stride",
        value: "stride",
      },
    ],
    beverage: [
      {
        name: "Sprite",
        value: "sprite",
      },
      {
        name: "Gatorade",
        value: "gatorade",
      },
      {
        name: "Powerade",
        value: "powerade",
      },
      {
        name: "Arizona",
        value: "arizona",
      },
      {
        name: "Snapple",
        value: "snapple",
      },
    ],
    health: [
      {
        name: "Bandaid",
        value: "bandaid",
      },
      {
        name: "Allegra",
        value: "allegra",
      },
      {
        name: "Tylenol",
        value: "tylenol",
      },
      {
        name: "Nyquil",
        value: "nyquil",
      },
      {
        name: "Advil",
        value: "advil",
      },
    ],
    cleaning: [
      {
        name: "Detergent",
        value: "detergent",
      },
      {
        name: "Dish Soap",
        value: "dish_soap",
      },
      {
        name: "Bleach",
        value: "bleach",
      },
      {
        name: "Ariel",
        value: "ariel",
      },
      {
        name: "Lirio",
        value: "lirio",
      },
      {
        name: "Tide",
        value: "tide",
      },
      {
        name: "Clorox",
        value: "clorox",
      },
      {
        name: "Mr Clean",
        value: "mr_clean",
      },
      {
        name: "Lysol",
        value: "lysol",
      },
      {
        name: "Glade",
        value: "glade",
      },
      {
        name: "Airwick",
        value: "airwick",
      },
      {
        name: "Glad",
        value: "glad",
      },
      {
        name: "Dawn Ultra",
        value: "dawn_ultra",
      },
      {
        name: "Downy",
        value: "downy",
      },
      {
        name: "Cloralen",
        value: "cloralen",
      },
      {
        name: "Lucky",
        value: "lucky",
      },
    ],
    energy: [],
    alcohol: [],
  };

  return categoryMap[category];
};

export default Tags;
