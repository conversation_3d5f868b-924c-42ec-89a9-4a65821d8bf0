import { QueryPushNotificationTokensArgs } from "../../generated/graphql";
import getPushNotificationTokens from "../../services/notificationService/getPushNotificationTokens";

const PushNotificationTokens = async (
  _,
  args: QueryPushNotificationTokensArgs
) => {
  const { user_ids } = args.getPushNotificationTokensInput;
  return await getPushNotificationTokens(user_ids);
};

export default PushNotificationTokens;
