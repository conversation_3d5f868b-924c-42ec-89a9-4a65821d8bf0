import knex from "../../../knex/knex";
import { createCredit } from "../../services/creditService";
import { CreateCreditInput } from "../../generated/graphql";

export async function createCreditResolver(
  _: any,
  { createCreditInput }: { createCreditInput: CreateCreditInput }
) {
  const trx = await knex.transaction();
  try {
    const credit = await createCredit(trx, createCreditInput);
    await trx.commit();
    return credit;
  } catch (error) {
    await trx.rollback();
    throw error;
  }
}

export default createCreditResolver;
