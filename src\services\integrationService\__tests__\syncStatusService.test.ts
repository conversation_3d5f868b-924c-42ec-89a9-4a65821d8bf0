import knex from "../../../../knex/knex";
import {
  upsertStatus,
  listStatusesBySupplier,
  markInProgress,
  markCompleted,
  markFailed,
} from "../syncStatusService";

describe("syncStatusService", () => {
  beforeEach(async () => {
    await knex("sync_status").del();
    await knex("supplier").del();

    // Insert a test supplier
    await knex("supplier").insert({
      id: 1,
      name: "Test Supplier",
      email: "<EMAIL>",
    });
  });

  afterAll(async () => {
    await knex("sync_status").del();
    await knex("supplier").del();
    await knex.destroy();
  });

  describe("upsertStatus", () => {
    it("should insert a new status record", async () => {
      const supplierId = 1;
      const type = "items";
      const params = {
        status: "PENDING" as const,
        errorMessage: null,
        startedAt: null,
        completedAt: null,
      };

      await upsertStatus(supplierId.toString(), type, params);

      const records = await knex("sync_status")
        .where({ supplier_id: supplierId, type })
        .first();

      expect(records).toMatchObject({
        supplier_id: supplierId,
        type,
        status: "PENDING",
        error: null,
        start_time: null,
        end_time: null,
        duration_ms: null,
      });
      expect(records?.last_synced).toBeInstanceOf(Date);
    });

    it("should calculate duration when startedAt and completedAt are provided", async () => {
      const supplierId = 1;
      const type = "items";
      const startedAt = new Date("2024-01-01T10:00:00Z");
      const completedAt = new Date("2024-01-01T10:05:00Z");
      const params = {
        status: "COMPLETED" as const,
        errorMessage: null,
        startedAt,
        completedAt,
      };

      await upsertStatus(supplierId.toString(), type, params);

      const records = await knex("sync_status")
        .where({ supplier_id: supplierId, type })
        .first();

      expect(records).toMatchObject({
        supplier_id: supplierId,
        type,
        status: "COMPLETED",
        error: null,
        start_time: startedAt,
        end_time: completedAt,
        duration_ms: 300, // 5 minutes in seconds
      });
    });

    it("should update existing record when recordId is provided", async () => {
      const supplierId = 1;
      const type = "items";

      // First insert
      const recordId = await upsertStatus(supplierId.toString(), type, {
        status: "PENDING" as const,
        errorMessage: null,
        startedAt: null,
        completedAt: null,
      });

      // Second call should update the same record
      await upsertStatus(
        supplierId.toString(),
        type,
        {
          status: "COMPLETED" as const,
          errorMessage: null,
          startedAt: new Date("2024-01-01T10:00:00Z"),
          completedAt: new Date("2024-01-01T10:05:00Z"),
        },
        recordId
      );

      const records = await knex("sync_status").where({ supplier_id: 1, type });

      expect(records).toHaveLength(1);
      expect(records[0]).toMatchObject({
        supplier_id: 1,
        type,
        status: "COMPLETED",
        id: recordId,
      });
    });
  });

  describe("listStatusesBySupplier", () => {
    it("should return sync statuses for a supplier", async () => {
      const supplierId = 1;

      // Insert test data
      await knex("sync_status").insert([
        {
          supplier_id: supplierId,
          type: "items",
          status: "COMPLETED",
          error: null,
          start_time: new Date("2024-01-01T10:00:00Z"),
          end_time: new Date("2024-01-01T10:05:00Z"),
          duration_ms: 300000,
          last_synced: new Date("2024-01-01T10:05:00Z"),
        },
        {
          supplier_id: supplierId,
          type: "customers",
          status: "FAILED",
          error: "Network error",
          start_time: new Date("2024-01-01T10:02:00Z"),
          end_time: new Date("2024-01-01T10:03:00Z"),
          duration_ms: 60000,
          last_synced: new Date("2024-01-01T10:03:00Z"),
        },
      ]);

      const result = await listStatusesBySupplier(supplierId.toString());

      expect(result).toHaveLength(2);
      expect(result[0]).toMatchObject({
        type: "customers", // ordered by start_time desc (10:02 > 10:00)
        status: "FAILED",
        error: "Network error",
        durationMs: 60000,
      });
      expect(result[1]).toMatchObject({
        type: "items", // ordered by start_time desc
        status: "COMPLETED",
        error: undefined,
        durationMs: 300000,
      });
    });

    it("should handle null values in database response", async () => {
      const supplierId = 1;

      // Insert test data with null values
      await knex("sync_status").insert([
        {
          supplier_id: supplierId,
          type: "items",
          status: "PENDING",
          error: null,
          start_time: null,
          end_time: null,
          duration_ms: null,
          last_synced: new Date("2024-01-01T09:00:00Z"),
        },
      ]);

      const result = await listStatusesBySupplier(supplierId.toString());

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        type: "items",
        status: "PENDING",
        error: undefined,
        lastSynced: "2024-01-01T09:00:00.000Z",
        startedAt: undefined,
        completedAt: undefined,
        durationMs: undefined,
      });
    });

    it("should return empty array for supplier with no statuses", async () => {
      const result = await listStatusesBySupplier("999");
      expect(result).toEqual([]);
    });
  });

  describe("markInProgress", () => {
    it("should mark sync as in progress and return record ID", async () => {
      const supplierId = 1;
      const type = "items";

      const recordId = await markInProgress(supplierId.toString(), type);

      expect(recordId).toBeDefined();
      expect(typeof recordId).toBe("number");

      const record = await knex("sync_status")
        .where({ supplier_id: supplierId, type })
        .first();

      expect(record).toMatchObject({
        supplier_id: supplierId,
        type,
        status: "IN_PROGRESS",
        error: null,
        end_time: null,
        duration_ms: null,
        id: recordId,
      });
      expect(record?.start_time).toBeInstanceOf(Date);
      expect(record?.last_synced).toBeInstanceOf(Date);
    });
  });

  describe("markCompleted", () => {
    it("should mark sync as completed", async () => {
      const supplierId = 1;
      const type = "items";
      const startedAt = new Date("2024-01-01T10:00:00Z");

      // First create a record in progress
      const recordId = await markInProgress(supplierId.toString(), type);

      // Then mark it as completed
      await markCompleted(supplierId.toString(), type, startedAt, recordId);

      const record = await knex("sync_status").where({ id: recordId }).first();

      expect(record).toMatchObject({
        supplier_id: supplierId,
        type,
        status: "COMPLETED",
        error: null,
        start_time: startedAt,
        id: recordId,
      });
      expect(record?.end_time).toBeInstanceOf(Date);
      expect(record?.duration_ms).toBeGreaterThan(0);
      expect(record?.last_synced).toBeInstanceOf(Date);
    });
  });

  describe("markFailed", () => {
    it("should mark sync as failed with error message", async () => {
      const supplierId = 1;
      const type = "items";
      const startedAt = new Date("2025-01-01T10:00:00Z");
      const error = new Error("Network timeout");

      // First create a record in progress
      const recordId = await markInProgress(supplierId.toString(), type);

      // Then mark it as failed
      await markFailed(supplierId.toString(), type, startedAt, error, recordId);

      const record = await knex("sync_status").where({ id: recordId }).first();

      expect(record).toMatchObject({
        supplier_id: supplierId,
        type,
        status: "FAILED",
        error: "Network timeout",
        start_time: startedAt,
        id: recordId,
      });
      expect(record?.end_time).toBeInstanceOf(Date);
      expect(record?.duration_ms).toBeGreaterThan(0);
      expect(record?.last_synced).toBeInstanceOf(Date);
    });
  });
});
