import knex from "../../../knex/knex";
import { env } from "../../config/environment";
import { MutationDeleteItemsArgs } from "../../generated/graphql";
import { deleteItems as algoliaDeleteItems } from "../../services/integrationService/algoliaOperations";

const deleteItems = async (_, args: MutationDeleteItemsArgs) => {
  const { itemIds: itemsToDelete } = args;
  const timeNow = new Date();
  if (env.production) {
    // Update Items in Algolia Index
    await algoliaDeleteItems(itemsToDelete);
  }
  await knex("item")
    .update({ archived: true, updated_at: timeNow })
    .where("id", "in", itemsToDelete);

  // Uncomment the following lines when ready to push to QB.
  // await processDBItemUpdates(
  //   await knex("item")
  //     .select("id", "name", "qb_id", "qb_sync_token", "supplier", "archived")
  //     .whereIn("id", itemsToDelete)
  // );

  return itemsToDelete;
};

export default deleteItems;
