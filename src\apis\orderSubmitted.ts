import { Request, Response } from "express";
import RestEndpoint from "./_restEndpoint";
import { sendSubmittedNotification } from "../services/notificationService/sendNotification";
export default class OrderSubmitted extends RestEndpoint {
  public async handler(req: Request, res: Response) {
    const body = req.body;
    try {
      await this.worker(body.orderId);
      res.status(200).send("Server OK");
    } catch (e) {
      console.error(e);
      res.status(500).send("Server Error");
    }
  }

  protected async worker(orderId: string) {
    if (orderId) {
      await sendSubmittedNotification(orderId);
    } else {
      throw new Error("Missing orderId");
    }
  }
}
