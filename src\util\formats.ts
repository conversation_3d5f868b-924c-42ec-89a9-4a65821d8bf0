import { Maybe } from "../generated/graphql";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

export const DateStringFromDbNumber = (
  date_number: Maybe<number> | number | undefined
) => (date_number ? new Date(date_number).toLocaleDateString() : null);

const USDollar = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
});

export const USDollarFromNumber = (
  dollar_number: Maybe<number> | number | undefined
) =>
  dollar_number !== undefined && dollar_number !== null
    ? USDollar.format(dollar_number)
    : null;
