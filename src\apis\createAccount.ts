import { Request, Response } from "express";
import RestEndpoint from "./_restEndpoint";

export default class CreateAccount extends RestEndpoint {
  public async handler(req: Request, res: Response) {
    try {
      const result = await this.worker(req.body);
      res.send(result);
    } catch (err) {
      res
        .status(err.code || 500)
        .send(`Error creating account: ${err.message || err.toString()}`);
    }
  }

  protected async worker(payload) {
    const query = /* GraphQL */ `
      mutation CreateAccountFromBalance($payload: String!) {
        createAccountFromBalance(payload: $payload)
      }
    `;
    const variables = {
      payload: encodeURIComponent(JSON.stringify(payload)),
    };

    return this.apolloHttpPost(query, variables).then(
      ({ createAccountFromBalance: result }) => {
        console.log("data returned:", result);
        return result;
      }
    );
  }
}
