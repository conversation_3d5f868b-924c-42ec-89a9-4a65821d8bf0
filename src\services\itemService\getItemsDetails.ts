import knex from "../../../knex/knex";
import { Item } from "../../generated/graphql";
import { filterByUpc, modifyUpc } from "../../util/upcs";

export const getItemsDetails = async (
  itemIds,
  supplierNames
): Promise<Item[]> => {
  const queries = itemIds.map(async ({ item_id, upc1 }) => {
    const modifiedUpc = modifyUpc(upc1);
    let query = knex
      .select("*")
      .from("item")
      .where((whereBuilder) =>
        filterByUpc(whereBuilder, modifiedUpc).orWhere("id", item_id)
      )
      .where("local_item", false)
      .where("outdated", false);

    if (supplierNames && supplierNames.length > 0) {
      query = query.whereIn("supplier", supplierNames);
    }

    return query;
  });

  const items = await Promise.all(queries);
  return items.filter((item) => item.length > 0).map((item) => item[0]);
};

export default getItemsDetails;
