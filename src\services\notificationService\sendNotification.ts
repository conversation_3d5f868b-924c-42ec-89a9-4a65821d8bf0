import expoPushNotification from "./expoPushNotification";
import Expo from "expo-server-sdk";
import knex from "../../../knex/knex";

const expoClient = new Expo();
const expoPushNotificationService = expoPushNotification(expoClient);

export const sendNotification = async (userIds, notification) => {
  const tokens = await knex
    .select("token")
    .from("push_notification_tokens")
    .whereIn("user_id", userIds);

  const { title, subtitle, body, data } = notification;
  try {
    await expoPushNotificationService.sendCommonPushNotification(
      tokens.map((token) => token["token"]),
      title,
      subtitle,
      body,
      data
    );
  } catch (error) {
    console.log("Error sending notification", error);
  }
};

export const sendSubmittedNotification = async (orderId) => {
  const orderDetail = await knex
    .select("*")
    .from("order_detail")
    .where("id", orderId)
    .first();

  const notification = {
    title: `Order Submitted`,
    subtitle: ``,
    body: `Your order #${orderDetail.id} for ${orderDetail.single_supplier} has been submitted! Track your order status in the the orders tab.`,
    data: {},
  };
  await sendNotification([orderDetail.user_id], notification);
};

export const sendOrderReminder = async () => {
  const users = await knex("attain_user AS a")
    .select("a.id")
    .whereNotIn("a.id", function () {
      this.select("o.user_id")
        .from("order_detail AS o")
        .where(
          "o.date_submitted",
          ">",
          knex.raw("current_date - interval '14 days'")
        );
    });

  const notification = {
    title: `Ready to restock?`,
    subtitle: ``,
    body: `With Attain, you can consolidate orders from suppliers and browse new items. Let us save you time and boost your bottom line.`,
    data: {},
  };
  await sendNotification(
    users.map((user) => user.id),
    notification
  );

  // return users;
};
