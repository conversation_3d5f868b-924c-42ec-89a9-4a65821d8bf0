import knex from "../../../knex/knex";
import dayjs from "dayjs";
import { queryBuilder } from "../../../knex/mock";
import { getRoutes, getRoutesOnDay } from "./routeService";
import { Route } from "../../generated/graphql";

jest.mock("../../../knex/knex", () => {
  const { queryBuilder } = require("../../../knex/mock");
  return {
    __esModule: true,
    default: jest.fn().mockReturnValue(queryBuilder),
  };
});

// Mock the dayjs module.
jest.mock("dayjs", () => {
  const mockDayjs = jest.fn();
  return Object.assign(mockDayjs, {
    utc: jest.fn(),
  });
});

describe("Route Service", () => {
  const supplierId = "supplier-abc-123";

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("getRoutes", () => {
    it("should return all routes for a given supplier ID", async () => {
      const fakeRoutes: Route[] = [
        {
          id: "route-1",
          name: "Monday Route",
          supplier_id: supplierId,
          day_of_week: "Monday",
          color: "#ff0000",
        },
        {
          id: "route-2",
          name: "Tuesday Route",
          supplier_id: supplierId,
          day_of_week: "Tuesday",
          color: "#00ff00",
        },
      ];

      queryBuilder.where.mockResolvedValueOnce(fakeRoutes);
      const result = await getRoutes(supplierId);

      expect(knex).toHaveBeenCalledWith("route");
      expect(queryBuilder.select).toHaveBeenCalledWith("*");
      expect(queryBuilder.where).toHaveBeenCalledWith(
        "supplier_id",
        supplierId
      );
      expect(result).toEqual(fakeRoutes);
    });

    it("should return an empty array if no routes are found", async () => {
      queryBuilder.where.mockResolvedValueOnce([]);
      const result = await getRoutes(supplierId);
      expect(result).toEqual([]);
    });
  });

  describe("getRoutesOnDay", () => {
    it("should return routes for a specific day and filter out UNASSIGNED", async () => {
      const testDate = new Date();
      const formattedDay = "Wednesday";

      const fakeRoutes: Route[] = [
        {
          id: "route-1",
          name: "Wednesday AM",
          supplier_id: supplierId,
          day_of_week: formattedDay,
          color: "#0000ff",
        },
        {
          id: "route-2",
          name: "UNASSIGNED",
          supplier_id: supplierId,
          day_of_week: formattedDay,
          color: "#808080",
        },
        {
          id: "route-3",
          name: "Wednesday PM",
          supplier_id: supplierId,
          day_of_week: formattedDay,
          color: "#ffff00",
        },
      ];

      (dayjs.utc as jest.Mock).mockReturnValue({
        format: jest.fn().mockReturnValue(formattedDay),
      });

      (queryBuilder.where as jest.Mock)
        .mockImplementationOnce(() => queryBuilder)
        .mockResolvedValueOnce(fakeRoutes);

      const result = await getRoutesOnDay(supplierId, testDate);

      expect(dayjs.utc).toHaveBeenCalledWith(testDate);
      expect(knex).toHaveBeenCalledWith("route");
      expect(queryBuilder.where).toHaveBeenCalledWith(
        "supplier_id",
        supplierId
      );
      expect(queryBuilder.where).toHaveBeenCalledWith(
        "day_of_week",
        formattedDay
      );
      expect(result.length).toBe(2);
      expect(
        result.find((route) => route.name === "UNASSIGNED")
      ).toBeUndefined();
    });
  });
});
