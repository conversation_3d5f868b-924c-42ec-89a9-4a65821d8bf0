import knex from "../../../../knex/knex";
import { Credit, CreditReason } from "../../../generated/graphql";
import { createOrder } from "../../orderService/createOrder";
import { createInvoiceFromOrder } from "../../invoiceService";
import { createCredit, updateCredit, getCredits } from "../index";

describe("Credit Service Integration Tests", () => {
  it("should create, update, and retrieve a credit", async () => {
    // Create test order
    const order = await createOrder({
      supplierId: "31",
      userId: "1984",
      orderItems: [
        {
          id: "391058",
          quantity: 5,
          price_purchased_at: 100,
        },
      ],
      deliveryDate: "2025-01-01",
      notes: "Test Notes",
      config: {},
      order_name: "Test Order",
    });

    expect(order).toBeDefined();
    expect(order.id).toBeDefined();

    // Create invoice from order
    const invoice = await createInvoiceFromOrder("31", order.id);

    expect(invoice).toBeDefined();
    expect(invoice?.id).toBeDefined();

    // Create credit
    const trx = await knex.transaction();
    try {
      const credit = await createCredit(trx, {
        invoice_id: parseInt(invoice?.id || "0"),
        user_id: 1984,
        supplier_id: 31,
        credit_items: [
          {
            item_id: 391058,
            quantity: 1,
            reason: CreditReason.Damaged,
          },
        ],
      });

      expect(credit).toBeDefined();
      expect(credit.id).toBeDefined();
      expect(credit.creditItems).toBeDefined();
      expect(credit.creditItems?.length).toBe(1);
      expect(credit.creditItems?.[0]?.reason).toBe(CreditReason.Damaged);
      expect(credit.invoice_id).toBe(parseInt(invoice?.id || "0"));
      expect(credit.order_id).toBe(parseInt(order.id));
      expect(credit.order_number).toBe(order.order_number);

      // Update credit
      const updatedCredit = await updateCredit(trx, {
        id: credit.id || 0,
        supplier_id: 31,
        credit_items: [
          {
            item_id: 391058,
            reason: CreditReason.Missing,
            quantity: 1,
          },
        ],
      });

      expect(updatedCredit.creditItems).toBeDefined();
      expect(updatedCredit.creditItems?.[0]?.reason).toBe(CreditReason.Missing);

      // Retrieve credit within transaction
      const retrievedCreditInTx = await getCredits(
        {
          filters: {
            ids: [credit.id || 0],
          },
          supplier_id: 31,
        },
        trx
      );

      expect(retrievedCreditInTx.credits).toHaveLength(1);
      const firstCreditInTx = retrievedCreditInTx.credits[0];
      expect(firstCreditInTx.id).toBe(credit.id);
      expect(firstCreditInTx.creditItems).toBeDefined();
      expect(firstCreditInTx.creditItems?.length).toBe(1);
      expect(firstCreditInTx.creditItems?.[0]?.reason).toBe(
        CreditReason.Missing
      );

      await trx.commit();

      // Verify credit after transaction is committed
      const retrievedCredit = await getCredits({
        filters: {
          ids: [credit.id || 0],
        },
        supplier_id: 31,
      });

      expect(retrievedCredit.credits).toHaveLength(1);
      const firstCredit = retrievedCredit.credits[0];
      expect(firstCredit.id).toBe(credit.id);
      expect(firstCredit.creditItems).toBeDefined();
      expect(firstCredit.creditItems?.length).toBe(1);
      expect(firstCredit.creditItems?.[0]?.reason).toBe(CreditReason.Missing);
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  });

  it("should handle credit creation with invalid invoice ID", async () => {
    const trx = await knex.transaction();
    try {
      await expect(
        createCredit(trx, {
          invoice_id: 999999, // Non-existent invoice ID
          user_id: 1984,
          supplier_id: 31,
          credit_items: [
            {
              item_id: 391058,
              quantity: 1,
              reason: CreditReason.Damaged,
            },
          ],
        })
      ).rejects.toThrow();
      await trx.rollback();
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  });

  it("should handle credit creation with invalid item ID", async () => {
    const order = await createOrder({
      supplierId: "31",
      userId: "1984",
      orderItems: [
        {
          id: "391058",
          quantity: 5,
          price_purchased_at: 100,
        },
      ],
      deliveryDate: "2025-01-01",
      notes: "Test Notes",
      config: {},
      order_name: "Test Order",
    });

    const invoice = await createInvoiceFromOrder("31", order.id);

    const trx = await knex.transaction();
    try {
      await expect(
        createCredit(trx, {
          invoice_id: parseInt(invoice?.id || "0"),
          user_id: 1984,
          supplier_id: 31,
          credit_items: [
            {
              item_id: 999999, // Non-existent item ID
              quantity: 1,
              reason: CreditReason.Damaged,
            },
          ],
        })
      ).rejects.toThrow();
      await trx.rollback();
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  });
});
