import knex from "../../../knex/knex";
import { QueryResolvers } from "../../generated/graphql";
import { getFavoriteItems } from "../../services/favoriteService";
import addLastOrderedDate from "../../services/itemService/addLastOrderedDate";
import modifyToCustomPricing from "../../services/itemService/modifyToCustomPricing";

const getFavorites: QueryResolvers["getFavorites"] = async (_, { input }) => {
  const { userId, employeeId, pagination } = input;
  const { offset = 0, limit = 20 } = pagination || {};

  try {
    const user = await knex("attain_user").where({ id: userId }).first();

    if (!user) {
      throw new Error("User not found");
    }

    const items = await getFavoriteItems(userId, employeeId, offset, limit);

    const itemsWithFavoriteFlag = items.map((item) => ({
      ...item,
      isFavorited: true,
    }));

    await modifyToCustomPricing(itemsWithFavoriteFlag, userId.toString());

    return addLastOrderedDate(itemsWithFavoriteFlag, userId.toString());
  } catch (error) {
    console.error("Error in getFavorites resolver:", error);
    throw error;
  }
};

export default getFavorites;
