import { Knex } from "knex";

export async function seed(knex: Knex): Promise<void> {
  // Deletes ALL existing entries
  await knex("item").del();

  // Inserts seed entries
  await knex("item").insert([
    {
      name: "Supplier1Item1",
      unit_size: "12",
      upc1: "123456789011",
      upc2: "123456789012",
      price: 1.99,
      supplier_code: 101,
      oos: false,
      supplier: "Test1",
    },
    {
      name: "Supplier1Item2",
      unit_size: "6",
      upc1: "123456789013",
      upc2: "123456789014",
      price: 2.99,
      supplier_code: 102,
      oos: false,
      supplier: "Test1",
    },
    {
      name: "Supplier1Item3",
      unit_size: "24",
      upc1: "123456789011",
      upc2: "123456789012",
      price: 3.99,
      supplier_code: 103,
      oos: false,
      supplier: "Test1",
    },
    {
      name: "Supplier1Item4",
      unit_size: "1",
      upc1: "123456789011",
      upc2: "123456789012",
      price: 4.99,
      supplier_code: 104,
      oos: false,
      supplier: "Test1",
    },
  ]);
}
