import knex from "../../../knex/knex";
import { GraphQLError } from "graphql";
import { QueryInvoiceArgs } from "../../generated/graphql";
import axios from "axios";
import { scraper } from "../../config/environment";
import { URLSearchParams } from "url";

const dateToString = (d: Date) => {
  let month = "" + (d.getMonth() + 1),
    day = "" + d.getDate();
  const year = d.getFullYear();

  if (month.length < 2) month = "0" + month;
  if (day.length < 2) day = "0" + day;

  return [year, month, day].join("-");
};

const Invoice = async (_, args: QueryInvoiceArgs) => {
  // TODO: use userId to fetch encrypted scraper credentials and decrypt them
  const { orderId } = args;

  const invoice = await knex("invoice")
    .select("*")
    .where("order_id", orderId)
    .first();
  if (invoice) {
    return {
      invoice,
      processing: false, // TODO: Define processing status in next few iterations.
    };
  }

  let order_date_submitted,
    order_date_start_window,
    order_date_end_window,
    order_supplier,
    userId,
    supplier_id;
  try {
    ({
      date_submitted: order_date_submitted,
      single_supplier: order_supplier,
      user_id: userId,
    } = await knex("order_detail")
      .select("single_supplier", "date_submitted", "user_id")
      .where("id", orderId)
      .first());
    if (!order_supplier || !order_date_submitted || !userId) {
      throw new Error(
        "Order doesn't have a supplier, submitted date, or an associated user ID."
      );
    }
    const supplier_id_data = await knex("supplier")
      .select("id")
      .where("name", order_supplier)
      .first();
    if (!supplier_id_data || !supplier_id_data.id) {
      throw new Error(`Supplier with name ${order_supplier} not found.`);
    }
    supplier_id = supplier_id_data.id;
    order_date_start_window = new Date(order_date_submitted);
    order_date_start_window.setDate(order_date_start_window.getDate() + 1); // TODO: fine tune window or find a better way to match invoices and orders.
    order_date_end_window = new Date(order_date_submitted);
    order_date_end_window.setDate(order_date_end_window.getDate() + 5); // TODO: fine tune window or find a better way to match invoices and orders.
  } catch (err) {
    throw new GraphQLError(
      `Order with ID ${orderId} not found, doesn't have a provided/valid supplier, or a submitted date (${err}).`,
      {
        extensions: { code: "BAD_USER_INPUT" },
      }
    );
  }
  // TODO: define exact naming format for future spiders.
  order_supplier = order_supplier.toLowerCase().replaceAll(" ", "-");
  // TODO: add robust checking for supplier and make code scalable by supplier.
  if (order_supplier !== "coremark") {
    return {
      invoice: null,
      processing: null, // TODO: Define processing status in next few iterations.
    };
  }

  try {
    // TODO: use userId to fetch encrypted credentials/type of supplier, decrypt them, and use them to call the scraper.
    const username = process.env.SCRAPER_USERNAME,
      password = process.env.SCRAPER_PASSWORD;
    if (!username || !password) {
      throw new Error("Scraper credentials not found.");
    }
    const params = new URLSearchParams({
      project: scraper.project ?? "default",
      spider: order_supplier,
      supplier_id,
      order_id: orderId,
      username: encodeURIComponent(username),
      password: encodeURIComponent(password),
      start_date_str: dateToString(order_date_start_window),
      end_date_str: dateToString(order_date_end_window),
    }).toString();
    await axios.post(`${scraper.url}/schedule.json?` + params.toString());
    return {
      invoice: null,
      processing: true, // TODO: define processing status in next few iterations.
    };
  } catch (err) {
    throw new GraphQLError(
      `Error occurred calling the scraper to fetch invoice for order with ID ${orderId}: ${err}.`,
      {
        extensions: { code: "INTERNAL_SERVER_ERROR" },
      }
    );
  }
};

export default Invoice;
