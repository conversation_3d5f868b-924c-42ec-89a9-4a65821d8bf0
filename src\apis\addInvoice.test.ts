import AddInvoice from "./addInvoice";

describe("addInvoice REST endpoint", () => {
  const mockReq = {
    body: { supplier: "coremark" },
  };
  const mockRes = {
    send: jest.fn().mockReturnThis(),
    status: jest.fn().mockReturnThis(),
  };
  const addInvoiceApi: AddInvoice = new AddInvoice(
    jest.fn() as never,
    jest.fn() as never
  );
  const endpointApolloHttpPostSpy: jest.SpyInstance = jest.spyOn(
    addInvoiceApi as never,
    "apolloHttpPost"
  );
  const sampleInvoiceItems = [
    {
      name: "itemName",
      quantity: 12,
      upc1: "upc1",
      upc2: "upc2",
      price: 4.56,
      unit_size: "12",
    },
    {
      name: "itemName2",
      quantity: 123,
      upc1: "upc1",
      upc2: "upc2",
      price: 45.67,
      unit_size: "12 pack",
    },
    {
      name: "itemName3",
      quantity: 1234,
      upc1: "upc1",
      upc2: "upc2",
      upc3: "upc3",
      upc4: "upc4",
      price: 567.89,
      unit_size: "12",
    },
    {
      name: "itemName4",
      quantity: 12345,
      upc1: "upc1",
      upc2: "upc2",
      upc3: "upc3",
      upc4: "upc4",
      price: 678.99,
      unit_size: "12",
    },
  ];
  const sampleOrderData = {
    order_id: 123,
    supplier_id: 321,
    date_received: new Date().getTime(),
    invoice_id: 456,
    total: 9876.54,
  };

  beforeEach(() => {
    endpointApolloHttpPostSpy.mockClear();
  });

  const supplierInvoiceTests = [
    {
      supplier: "coremark",
      processFn: "processCoremarkInvoice",
      data: {
        items: [
          {
            description: sampleInvoiceItems[0].name,
            quantityShipped: sampleInvoiceItems[0].quantity,
            upc: sampleInvoiceItems[0].upc1,
            upc2: sampleInvoiceItems[0].upc2,
            price: sampleInvoiceItems[0].price,
            unitSize: parseInt(sampleInvoiceItems[0].unit_size),
          },
        ],
      },
    },
  ];
  it.concurrent.each(supplierInvoiceTests)(
    "correctly processes and maps items data for supplier $supplier",
    ({ processFn, data }) => {
      expect(addInvoiceApi[processFn](data).items).toEqual([
        sampleInvoiceItems[0],
      ]);
    }
  );

  it("sends processed invoice data for supported supplier", async () => {
    const processCoremarkInvoiceSpy = jest
      .spyOn(addInvoiceApi as never, "processCoremarkInvoice")
      .mockReturnValue({ ...sampleOrderData, items: [] } as never);
    const result = {
      addInvoice: {},
    };
    endpointApolloHttpPostSpy.mockResolvedValue(result as never);

    await addInvoiceApi.handler(mockReq as never, mockRes as never);

    expect(mockRes.status).toHaveBeenCalledWith(200);
    expect(mockRes.send).toHaveBeenCalledWith("OK");

    processCoremarkInvoiceSpy.mockRestore();
  });

  it("sends 400 error code for unsupported supplier", async () => {
    const unsupportedSupplier = "unsupported-supplier";
    const modifiedReq = {
      body: { ...mockReq.body, supplier: unsupportedSupplier },
    };

    await addInvoiceApi.handler(modifiedReq as never, mockRes as never);

    expect(mockRes.status).toHaveBeenCalledWith(400);
  });

  it("send 500 error code for unknown errors", async () => {
    const processCoremarkInvoiceSpy = jest
      .spyOn(addInvoiceApi as never, "processCoremarkInvoice")
      .mockReturnValue({ ...sampleOrderData, items: [] } as never);
    endpointApolloHttpPostSpy.mockImplementation(async () => {
      throw "some weird error";
    });

    await addInvoiceApi.handler(mockReq as never, mockRes as never);

    expect(mockRes.status).toHaveBeenCalledWith(500);

    processCoremarkInvoiceSpy.mockRestore();
  });
});
