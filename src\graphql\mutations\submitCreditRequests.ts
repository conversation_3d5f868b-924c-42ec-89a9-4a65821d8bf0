import { format } from "mathjs";
import { postTextViaSlackWebhook } from "../../services/notificationService/slackWebhook";
import knex from "../../../knex/knex";
const submitCreditRequests = async (_, args) => {
  const { userId, supplier, orderId, creditRequests } =
    args.submitCreditRequestsInput;

  const supplier_id = await knex("supplier")
    .select("id")
    .where("name", supplier);

  const creditRequestsToInsert = creditRequests.map((creditRequest) => {
    return {
      user_id: userId,
      supplier_id: supplier_id[0].id,
      order_id: orderId,
      item_id: creditRequest.itemId,
      quantity: creditRequest.quantity,
      mispick: creditRequest.isMispick,
      damaged: creditRequest.isDamaged,
      expired: creditRequest.isExpired,
      status: "Pending",
      price_purchased_at: creditRequest.pricePurchasedAt,
    };
  });

  await knex("credit_request").insert(creditRequestsToInsert);
  const text =
    "Credit Requests Submitted for " +
    userId +
    " to " +
    supplier +
    " for order " +
    orderId +
    ":\n" +
    formatCreditRequests(creditRequests);

  const url =
    "*********************************************************************************";
  await postTextViaSlackWebhook(url, text);

  return "success";
};

const formatCreditRequests = (creditRequests) => {
  let text = "";
  creditRequests.forEach((creditRequest) => {
    text +=
      "item id: " +
      creditRequest.itemId +
      ", quantity: " +
      creditRequest.quantity +
      ", reason: " +
      "mispick: " +
      creditRequest.isMispick +
      ", damaged: " +
      creditRequest.isDamaged +
      ", expired: " +
      creditRequest.isExpired +
      "\n";
  });
  return text;
};
export default submitCreditRequests;
