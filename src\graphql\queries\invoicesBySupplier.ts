import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import knex from "../../../knex/knex";
import { Ordering, QueryInvoicesBySupplierArgs } from "../../generated/graphql";
import { Knex } from "knex";
import { getSupplierConfig } from "../../services/supplierService";

// Create accessors for JSON and other fields
function convertToQuery(inputField: string) {
  const [field, key] = inputField.split(".");
  if (field === "customerDetails") {
    return `"${field}" ->> '${key}'`;
  } else if (key) {
    return field + "_" + key;
  }
  return field;
}

dayjs.extend(utc);

const InvoicesBySupplier = async (_, args: QueryInvoicesBySupplierArgs) => {
  const {
    supplierId,
    filters,
    pagination: { offset, limit } = { offset: 0, limit: 100 },
    sortBy: { field: sortField, ordering: sortOrdering } = {
      field: "id",
      ordering: Ordering.Desc,
    },
  } = args.getInvoicesBySupplierInput;

  const supplierConfig = await getSupplierConfig(supplierId);

  const sortFieldAccessor = convertToQuery(
    sortField === "date_submitted" ? "date_created" : sortField // use date created instead
  ); // Sorting accessors for field types JSON and columns

  const filter = (queryBuilder: Knex.QueryBuilder) => {
    // For supplier 31, automatically exclude canceled orders since they don't show in table
    if (supplierConfig.exclude_canceled_orders) {
      queryBuilder.whereNotExists(function () {
        this.select("*")
          .from("order_detail")
          .whereRaw("order_detail.id = invoice.order_id")
          .where("order_detail.status", "Canceled");
      });
    }

    if (filters?.status) {
      switch (filters.status.toLowerCase()) {
        case "in transit":
          queryBuilder.whereExists(function () {
            this.select("*")
              .from("order_detail")
              .whereRaw("order_detail.id = invoice.order_id")
              .where("order_detail.status", "In Transit");
          });
          break;
        case "canceled":
          queryBuilder.whereExists(function () {
            this.select("*")
              .from("order_detail")
              .whereRaw("order_detail.id = invoice.order_id")
              .where("order_detail.status", "Canceled");
          });
          break;
        case "delivered":
          queryBuilder.whereExists(function () {
            this.select("*")
              .from("order_detail")
              .whereRaw("order_detail.id = invoice.order_id")
              .where("order_detail.status", "Delivered");
          });
          break;
        case "placed":
          queryBuilder.whereExists(function () {
            this.select("*")
              .from("order_detail")
              .whereRaw("order_detail.id = invoice.order_id")
              .where("order_detail.status", "submitted");
          });
          break;
        case "overdue":
          queryBuilder.whereExists(function () {
            this.select("*")
              .from("order_detail")
              .whereRaw("order_detail.id = invoice.order_id")
              .where("order_detail.status", "In Transit");
          });
          break;
      }
    }
    if (filters?.paidStatus) {
      switch (filters.paidStatus.toLowerCase()) {
        case "paid":
          queryBuilder.where(function () {
            this.where("invoice.payment_status", "paid").orWhere(function () {
              this.whereNull("invoice.payment_status").andWhereRaw(
                `ROUND(coalesce(invoice.total, 0)-coalesce(invoice.paid, 0), 2) <= 0`
              );
            });
          });
          break;
        case "unpaid":
          queryBuilder.where(function () {
            this.where("invoice.payment_status", "unpaid").orWhere(function () {
              this.whereNull("invoice.payment_status")
                .andWhereRaw(
                  `ROUND(coalesce(invoice.total, 0)-coalesce(invoice.paid, 0), 2) > 0`
                )
                .andWhereRaw(`ROUND(coalesce(invoice.paid, 0), 2) = 0`);
            });
          });
          break;
        case "bounced":
          queryBuilder.where("invoice.payment_status", "bounced");
          break;
        case "partial":
          queryBuilder.where(function () {
            this.where("invoice.payment_status", "partial").orWhere(
              function () {
                this.whereNull("invoice.payment_status").andWhereRaw(
                  `ROUND(coalesce(invoice.paid, 0), 2) > 0 AND ROUND(coalesce(invoice.total, 0)-coalesce(invoice.paid, 0), 2) > 0`
                );
              }
            );
          });
          break;
      }
    }
    if (filters?.deliveryDate) {
      queryBuilder.whereExists(function () {
        this.select("*")
          .from("order_status")
          .whereRaw("order_status.order_id = invoice.order_id")
          .andWhere(
            "delivery_date",
            dayjs(filters.deliveryDate).utc().format("YYYY-MM-DD")
          );
      });
    }
    if (filters?.dateRange) {
      queryBuilder.whereExists(function () {
        this.select("*")
          .from("order_status")
          .whereRaw("order_status.order_id = invoice.order_id")
          .andWhere(
            "delivery_date",
            ">=",
            dayjs(filters.dateRange[0]).utc().format("YYYY-MM-DD")
          )
          .andWhere(
            "delivery_date",
            "<=",
            dayjs(filters.dateRange[1]).utc().format("YYYY-MM-DD")
          );
      });
    }
    if (filters?.dateRange) {
      queryBuilder.whereBetween("date_created", [
        filters.dateRange[0],
        filters.dateRange[1],
      ]);
    }
    if ((filters?.routeIds && filters?.routeIds.length) || filters?.driver) {
      queryBuilder.where((builder) => {
        if (filters?.driver) {
          // First check if there's a custom_driver on the order
          builder.where((subBuilder) => {
            subBuilder.whereRaw(
              `invoice.config->>'custom_driver' = ?`,
              filters.driver
            );
          });
          // For orders with no custom_driver, check route_ids
          builder.orWhere((subBuilder) => {
            subBuilder
              .whereRaw(`invoice.config->>'custom_driver' IS NULL`)
              .andWhere((routeBuilder) => {
                if (filters?.routeIds && filters?.routeIds.length) {
                  routeBuilder.where(
                    knex.raw(
                      `?::text[] && string_to_array(attain_user.route_id, ',')`,
                      [filters.routeIds]
                    )
                  );
                  routeBuilder.orWhereRaw(
                    `invoice.config->>'custom_route' = ANY(?::text[])`,
                    [filters.routeIds]
                  );
                }
              });
          });
        } else if (filters?.routeIds && filters?.routeIds.length) {
          // If using route instead of driver, check routes
          // First check if there's a custom_route on the order
          builder.where((subBuilder) => {
            subBuilder.whereRaw(
              `invoice.config->>'custom_route' = ANY(?::text[])`,
              [filters.routeIds]
            );
          });
          // For orders with no custom_route, check route_ids
          builder.orWhere((subBuilder) => {
            subBuilder
              .whereRaw(`invoice.config->>'custom_route' IS NULL`)
              .andWhere((routeBuilder) => {
                if (filters?.routeIds && filters?.routeIds.length) {
                  routeBuilder.where(
                    knex.raw(
                      `?::text[] && string_to_array(attain_user.route_id, ',')`,
                      [filters.routeIds]
                    )
                  );
                }
              });
          });
        }
      });
    }
    if (filters?.userId) {
      queryBuilder.andWhere("invoice.user_id", filters.userId);
    }
    if (filters?.lastPaidDate) {
      queryBuilder.whereRaw(
        "invoice.config->>'date_last_paid' = ?",
        dayjs(filters.lastPaidDate).utc().format("YYYY-MM-DD")
      );
    }
    if (filters?.query) {
      const isNumericQuery = /^\d+$/.test(filters.query);
      queryBuilder.whereExists(function () {
        if (isNumericQuery && filters.query.length < 6) {
          this.select("*")
            .from("order_detail")
            .whereRaw("order_detail.id = invoice.order_id")
            .where("order_detail.order_number", filters.query);
        } else {
          this.select("*")
            .from("order_detail")
            .whereRaw("order_detail.id = invoice.order_id")
            .where("order_detail.notes", "ilike", `%${filters.query}%`);
        }
      });
    }
    if (filters?.signed !== undefined && filters.signed !== null) {
      queryBuilder.andWhere(
        "invoice.signature_name",
        filters.signed ? "is not" : "is",
        null
      );
    }
  };

  const baseQuery = knex("invoice")
    .select(
      "invoice.*",
      knex.raw(`SUM(
                CASE 
                  WHEN ${
                    supplierId === "31"
                  } AND (invoice_item.price - invoice_item.cog_price) <= 0 THEN 0
                  ELSE (invoice_item.price - invoice_item.cog_price) * invoice_item.quantity
                END
                ) as profit`),
      knex.raw(
        `json_agg(row_to_json(invoice_item) ORDER BY invoice_item.id) as "invoiceItems"`
      ),
      knex.raw(`row_to_json(attain_user) as "customerDetails"`),
      knex.raw(`row_to_json(order_detail) as "orderDetails"`)
    )
    .leftJoin("attain_user", "invoice.user_id", "attain_user.id")
    .leftJoin("order_detail", "invoice.order_id", "order_detail.id")
    .leftJoin("invoice_item", "invoice.id", "invoice_item.invoice_id")
    .where("invoice.supplier_id", supplierId)
    .where("invoice.subtotal", ">", 0)
    .whereNot("invoice.archived", true)
    .groupBy("invoice.id", "attain_user.id", "order_detail.id")
    .modify(filter);

  const totalCountSelect = knex
    .count({ total_count: "*" })
    .from("invoices")
    .as("total_count");

  const totalProfitSelect = knex
    .sum({ total_profit: "profit" })
    .from("invoices")
    .as("total_profit");

  const totalCreditSelect = knex
    .sum({ total_credit: "credit" })
    .from("invoices")
    .as("total_credit");

  const totalCasesSelect = knex
    .sum({ total_cases: "invoice_item.quantity" })
    .from("invoice_item")
    .whereIn("invoice_id", knex.select("id").from("invoices"))
    .as("total_cases");

  const paymentTotalRows = knex
    .select(
      knex.raw(`
        COALESCE(invoices.payment_method, 'Other') as payment_method,
        SUM(COALESCE(invoices.subtotal, 0)) as total,
        SUM(COALESCE(invoices.paid, 0)) as paid,
        SUM(COALESCE(invoices.total, 0)) - SUM(COALESCE(invoices.paid, 0)) as unpaid
      `)
    )
    .from("invoices")
    // .where("invoice.supplier_id", supplierId) // TODO: remove these, I'm leaving them here while we debug
    // .whereNot("invoice.archived", true)
    // .modify(filter)
    .groupBy(knex.raw("COALESCE(invoices.payment_method, 'Other')"))
    .orderBy("payment_method")
    .as("payment_totals");

  const paymentTotalsQuery = knex
    .select(
      knex.raw(`coalesce(json_agg(json_build_object(
                              payment_method,
                              json_build_object(
                                'total', payment_totals.total,
                                'paid', payment_totals.paid,
                                'unpaid', payment_totals.unpaid)
                              )
              ), '[]'::json) as payment_totals`)
    )
    .from(paymentTotalRows)
    .as("payment_totals");

  const fullQuery = knex
    .with("invoices", baseQuery)
    .select("*")
    .from("invoices")
    .orderByRaw(`("invoices".` + sortFieldAccessor + ")" + sortOrdering) // Some string munging for accessing different field types
    .limit(limit)
    .offset(offset)
    .rightJoin(totalCountSelect, knex.raw("true"))
    .rightJoin(totalProfitSelect, knex.raw("true"))
    .rightJoin(totalCreditSelect, knex.raw("true"))
    .rightJoin(paymentTotalsQuery, knex.raw("true"))
    .rightJoin(totalCasesSelect, knex.raw("true"));

  const result = await fullQuery;
  const invoices = result.filter((invoice) => invoice.id);
  const totalCount = result[0]?.total_count || 0;
  const totalProfit = result[0]?.total_profit || 0;
  const totalCredit = result[0]?.total_credit || 0;
  const paymentTotals =
    result[0]?.payment_totals.reduce(
      (acc: Record<string, unknown>, curr: Record<string, unknown>) => {
        Object.keys(curr).forEach((key) => {
          acc[key] = curr[key];
        });
        return acc;
      },
      {}
    ) || {};
  const totalCases = result[0]?.total_cases || 0;

  return {
    invoices,
    totalCount,
    totalProfit,
    totalCredit,
    paymentTotals,
    totalCases,
  };
};

export default InvoicesBySupplier;
