import { QueryExpectedOrdersArgs } from "../../generated/graphql";
import { getExpectedOrders } from "../../services/orderService/orderService";

const ExpectedOrders = async (_, args: QueryExpectedOrdersArgs) => {
  const { supplierId, date } = args;
  const expectedOrders = await getExpectedOrders(
    supplierId,
    date ?? new Date()
  );
  return expectedOrders;
};

export default ExpectedOrders;
