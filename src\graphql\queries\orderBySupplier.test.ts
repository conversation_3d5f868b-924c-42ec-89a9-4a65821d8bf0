import { mockKnex, queryBuilder } from "../../../knex/mock";
import OrderBySupplierQuery from "./orderBySupplier";

mockKnex();
const mockedGetOrderItemsBySupplierResult = "result";
const mockedGetOrderItemsBySupplier = jest
  .fn()
  .mockResolvedValue(mockedGetOrderItemsBySupplierResult);
jest.mock(
  "../../services/getOrderItemsBySupplier",
  () => mockedGetOrderItemsBySupplier
);

describe("Order by Supplier query", () => {
  let orderBySupplier: typeof OrderBySupplierQuery;
  beforeAll(async () => {
    ({ default: orderBySupplier } = await import("./orderBySupplier"));
  });

  const orderBySupplierInput = {
    orderId: "1234",
    supplierId: "5678",
  };

  it("fetches supplier names from IDs and order items from given order ID for that supplier", async () => {
    const supplierName = "supplierName";
    queryBuilder.where.mockResolvedValueOnce([{ name: supplierName }]);

    const res = await orderBySupplier(undefined, orderBySupplierInput);

    expect(queryBuilder.where).toHaveBeenCalledWith(
      "id",
      orderBySupplierInput.supplierId
    );
    expect(mockedGetOrderItemsBySupplier).toHaveBeenCalledWith(
      orderBySupplierInput.orderId,
      supplierName
    );
    expect(res).toEqual(mockedGetOrderItemsBySupplierResult);
  });
});
