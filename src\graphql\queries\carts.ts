import knex from "../../../knex/knex";
import { QueryCartsArgs } from "../../generated/graphql";
import getCart from "../../services/cartService/getCart";
import modifyToCustomPricing from "../../services/itemService/modifyToCustomPricing";

const Carts = async (_, args: QueryCartsArgs) => {
  const { getCartsInput } = args;
  const { ids, userId, userIds } = getCartsInput;
  let carts = [];
  const combinedIds = [...(userId ? [userId] : []), ...(userIds || [])].filter(
    (id) => id != null
  );
  if (!ids) {
    carts = await knex.select().table("cart").whereIn("user_id", combinedIds);
  } else {
    carts = await knex.select("*").from("cart").whereIn("id", ids);
  }

  const result = [];

  for (let i = 0; i < carts.length; i++) {
    const cartId = carts[i].id;
    const cartDetails = await getCart(cartId);
    if (combinedIds) {
      await modifyToCustomPricing(cartDetails.cartItems, carts[i].user_id);
    }

    result.push(cartDetails);
  }

  return result;
};

export default Carts;
