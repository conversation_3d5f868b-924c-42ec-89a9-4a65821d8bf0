import knex from "../../../knex/knex";
import { Knex } from "knex";
import { UpsertUomInput, CustomUomPriceInput } from "../../generated/graphql";

export const getGroupCustomUOMPrices = async (
  groupname: string,
  supplierId: string
): Promise<CustomUomPriceInput[] | null> => {
  const user = await knex("attain_user")
    .where({ store_group: groupname })
    .first();

  if (!user) {
    throw new Error(`Group does not exist: ${groupname}`);
  }

  const customUOMPrices = await getUserCustomUOMPrices(user.id, supplierId);
  return customUOMPrices.map((price) => ({
    item_uom_id: price.item_uom_id,
    user_id: price.user_id,
    price: price.price,
  }));
};

export const updateGroupCustomUOMPrices = async (
  txn: Knex.Transaction,
  store_group: string,
  custom_uom_prices: CustomUomPriceInput[]
) => {
  const usersInGroup = await txn("attain_user")
    .where({ store_group })
    .select("id");

  const result = await Promise.all(
    usersInGroup.map(async (user) => {
      await updateUserCustomUOMPrices(
        txn,
        user.id.toString(),
        custom_uom_prices
      );
    })
  );
  return result;
};

export const populateItemsUOMs = async (items: any[], supplierId?: string) => {
  if (!items || items.length === 0) {
    return;
  }

  const itemIds = items.map((item) => item.id);

  // Get all UOMs for these items
  let query = knex
    .select([
      "item_uom.id",
      "uom.name",
      "uom.supplier_id",
      "item_uom.uom_id",
      "item_uom.quantity",
      "item_uom.item_id",
      "item_uom.price",
      "item_uom.upc",
      "item_uom.archived",
    ])
    .from("item_uom")
    .innerJoin("uom", "item_uom.uom_id", "uom.id")
    .whereIn("item_uom.item_id", itemIds)
    .where("item_uom.archived", false)
    .where("uom.archived", false);

  // Only filter by supplier if supplierId is provided
  if (supplierId) {
    query = query.where("uom.supplier_id", supplierId);
  }

  const allItemUOMs = await query;

  // Group UOMs by item_id
  const uomsByItem = new Map();

  allItemUOMs.forEach((uom) => {
    const itemId = uom.item_id;

    if (!uomsByItem.has(itemId)) {
      uomsByItem.set(itemId, []);
    }

    uomsByItem.get(itemId).push(uom);
  });

  // Assign UOMs to items
  items.forEach((item) => {
    item.uoms = uomsByItem.get(item.id) || [];
  });
};

export const upsertUOM = async (
  trx: Knex.Transaction | null,
  input: UpsertUomInput
) => {
  const { item_id, supplier_id, uoms } = input;

  if (!item_id || !supplier_id || !uoms || uoms.length === 0) {
    throw new Error("item_id, supplier_id, and uoms are required");
  }

  // Convert string IDs to numbers for database operations
  const itemId = parseInt(item_id, 10);
  const supplierId = parseInt(supplier_id, 10);

  if (isNaN(itemId) || isNaN(supplierId)) {
    throw new Error("Invalid item_id or supplier_id");
  }

  // Check if transaction exists, if not create our own
  const shouldManageTransaction = !trx;
  let transaction = trx;

  if (!transaction) {
    const trxProvider = knex.transactionProvider();
    transaction = await trxProvider();
  }

  try {
    // First, get existing item_uom records and their custom prices
    const existingItemUOMs = await transaction
      .select([
        "item_uom.id",
        "item_uom.uom_id",
        "uom.name as uom_name",
        "item_uom.quantity",
        "item_uom.price",
        "item_uom.upc",
      ])
      .from("item_uom")
      .innerJoin("uom", "item_uom.uom_id", "uom.id")
      .where("item_uom.item_id", itemId)
      .where("uom.supplier_id", supplierId)
      .where("item_uom.archived", false)
      .where("uom.archived", false);

    // Get custom prices for existing item_uom records
    const existingCustomPrices = await transaction
      .select([
        "custom_item_uom_price.item_uom_id",
        "custom_item_uom_price.user_id",
        "custom_item_uom_price.price",
        "uom.name as uom_name",
        "item_uom.quantity",
      ])
      .from("custom_item_uom_price")
      .innerJoin("item_uom", "custom_item_uom_price.item_uom_id", "item_uom.id")
      .innerJoin("uom", "item_uom.uom_id", "uom.id")
      .where("item_uom.item_id", itemId)
      .where("uom.supplier_id", supplierId)
      .where("item_uom.archived", false)
      .where("uom.archived", false);

    // Create a map of UOM characteristics to custom prices
    const customPriceMap = new Map();
    existingCustomPrices.forEach((customPrice) => {
      const key = `${customPrice.uom_name}-${customPrice.quantity}`;
      if (!customPriceMap.has(key)) {
        customPriceMap.set(key, []);
      }
      customPriceMap.get(key).push({
        user_id: customPrice.user_id,
        price: customPrice.price,
      });
    });

    // Delete existing item_uom rows for this item
    await transaction("item_uom").where("item_id", itemId).del();

    // Process each UOM input
    const createdUOMs = [];
    const newItemUOMMap = new Map(); // Map to store new item_uom records by their characteristics

    for (const uomInput of uoms) {
      const { name, quantity, price, upc } = uomInput;

      // Check if UOM with this name already exists for the supplier
      let uom = await transaction("uom")
        .where("name", name)
        .where("supplier_id", supplierId)
        .where("archived", false)
        .first();

      // If UOM doesn't exist, create it
      if (!uom) {
        const [newUOM] = await transaction("uom")
          .insert({
            name,
            supplier_id: supplierId,
            archived: false,
          })
          .returning("*");
        uom = newUOM;
      }

      // Create the item_uom record
      const [newItemUOM] = await transaction("item_uom")
        .insert({
          uom_id: uom.id,
          quantity,
          item_id: itemId,
          price: price || null,
          upc: upc || null,
          archived: false,
        })
        .returning("*");

      // Store the new item_uom record for custom price restoration
      const key = `${name}-${quantity}`;
      newItemUOMMap.set(key, newItemUOM);

      createdUOMs.push(uom);
    }

    // Restore custom prices with new item_uom_ids
    const customPricesToRestore = [];
    for (const [key, customPrices] of customPriceMap.entries()) {
      const newItemUOM = newItemUOMMap.get(key);
      if (newItemUOM) {
        customPrices.forEach((customPrice) => {
          customPricesToRestore.push({
            item_uom_id: newItemUOM.id,
            user_id: customPrice.user_id,
            price: customPrice.price,
          });
        });
      }
    }

    // Insert the restored custom prices
    if (customPricesToRestore.length > 0) {
      await transaction("custom_item_uom_price").insert(customPricesToRestore);
    }

    // Only commit if we're managing the transaction
    if (shouldManageTransaction) {
      await transaction.commit();
    }

    // Return unique UOMs
    const uniqueUOMs = createdUOMs.filter(
      (uom, index, self) => index === self.findIndex((u) => u.id === uom.id)
    );

    return uniqueUOMs;
  } catch (error) {
    // Only rollback if we're managing the transaction
    if (shouldManageTransaction) {
      await transaction.rollback();
    }
    console.error("Error upserting UOM:", error);
    throw new Error(`Error upserting UOM: ${error.message}`);
  }
};

export const getItemUOMs = async (itemId: string, supplierId: string) => {
  if (!itemId || !supplierId) {
    throw new Error("Both itemId and supplierId are required");
  }

  const itemUOMs = await knex
    .select([
      "item_uom.id",
      "uom.name",
      "uom.supplier_id",
      "item_uom.uom_id",
      "item_uom.quantity",
      "item_uom.item_id",
      "item_uom.price",
      "item_uom.upc",
      "item_uom.archived",
    ])
    .from("item_uom")
    .innerJoin("uom", "item_uom.uom_id", "uom.id")
    .where("item_uom.item_id", itemId)
    .where("uom.supplier_id", supplierId)
    .where("item_uom.archived", false)
    .where("uom.archived", false);

  return itemUOMs;
};

export const getItemUOMById = async (itemUomId: string) => {
  if (!itemUomId) {
    return null;
  }

  const itemUOM = await knex
    .select([
      "item_uom.id",
      "uom.name",
      "uom.supplier_id",
      "item_uom.uom_id",
      "item_uom.quantity",
      "item_uom.item_id",
      "item_uom.price",
      "item_uom.upc",
      "item_uom.archived",
    ])
    .from("item_uom")
    .innerJoin("uom", "item_uom.uom_id", "uom.id")
    .where("item_uom.id", itemUomId)
    .where("item_uom.archived", false)
    .where("uom.archived", false)
    .first();

  return itemUOM;
};

export const populateCartItemsUOMs = async (cartItems: any[]) => {
  if (!cartItems || cartItems.length === 0) {
    return;
  }

  // Get all unique item_uom_ids
  const itemUomIds = cartItems
    .map((item) => item.item_uom_id)
    .filter((id) => id != null);

  if (itemUomIds.length === 0) {
    // Set uoms to null for all items
    cartItems.forEach((item) => {
      item.uoms = null;
    });
    return;
  }

  // Fetch UOM data for all item_uom_ids
  const allItemUOMs = await knex
    .select([
      "item_uom.id",
      "uom.name",
      "uom.supplier_id",
      "item_uom.uom_id",
      "item_uom.quantity",
      "item_uom.item_id",
      "item_uom.price",
      "item_uom.upc",
      "item_uom.archived",
    ])
    .from("item_uom")
    .innerJoin("uom", "item_uom.uom_id", "uom.id")
    .whereIn("item_uom.id", itemUomIds)
    .where("item_uom.archived", false)
    .where("uom.archived", false);

  // Create a map for quick lookup
  const uomsByItemUomId = new Map();
  allItemUOMs.forEach((uom) => {
    uomsByItemUomId.set(uom.id, uom);
  });

  // Assign UOM data to cart items
  cartItems.forEach((item) => {
    if (item.item_uom_id) {
      item.uoms = uomsByItemUomId.get(item.item_uom_id) || null;
    } else {
      item.uoms = null;
    }
  });
};

export const populateInvoiceItemsUOMs = async (invoiceItems: any[]) => {
  if (!invoiceItems || invoiceItems.length === 0) {
    return;
  }

  // Get all unique item_uom_ids
  const itemUomIds = invoiceItems
    .map((item) => item.item_uom_id)
    .filter((id) => id != null);

  if (itemUomIds.length === 0) {
    // Set uoms to null for all items
    invoiceItems.forEach((item) => {
      item.uoms = null;
    });
    return;
  }

  // Fetch UOM data for all item_uom_ids
  const allItemUOMs = await knex
    .select([
      "item_uom.id",
      "uom.name",
      "uom.supplier_id",
      "item_uom.uom_id",
      "item_uom.quantity",
      "item_uom.item_id",
      "item_uom.price",
      "item_uom.upc",
      "item_uom.archived",
    ])
    .from("item_uom")
    .innerJoin("uom", "item_uom.uom_id", "uom.id")
    .whereIn("item_uom.id", itemUomIds)
    .where("item_uom.archived", false)
    .where("uom.archived", false);

  // Create a map for quick lookup
  const uomsByItemUomId = new Map();
  allItemUOMs.forEach((uom) => {
    uomsByItemUomId.set(uom.id, uom);
  });

  // Assign UOM data to invoice items
  invoiceItems.forEach((item) => {
    if (item.item_uom_id) {
      item.uoms = uomsByItemUomId.get(item.item_uom_id) || null;
    } else {
      item.uoms = null;
    }
  });
};

export const getUOMs = async (supplierId: string) => {
  if (!supplierId) {
    throw new Error("supplierId is required");
  }

  const uoms = await knex
    .select("*")
    .from("uom")
    .where("supplier_id", supplierId)
    .where("archived", false)
    .orderBy("name");

  return uoms;
};

export const getCustomUOMPrices = async (
  itemId: string,
  userId: string,
  supplierId: string
) => {
  if (!itemId || !userId || !supplierId) {
    throw new Error("itemId, userId, and supplierId are required");
  }

  // Get custom UOM prices from custom_item_uom_price table
  const customUOMPrices = await knex
    .select([
      "custom_item_uom_price.item_uom_id",
      "custom_item_uom_price.user_id",
      "custom_item_uom_price.price",
      "item_uom.item_id",
      "uom.name as uom_name",
      "uom.id as uom_id",
    ])
    .from("custom_item_uom_price")
    .innerJoin("item_uom", "custom_item_uom_price.item_uom_id", "item_uom.id")
    .innerJoin("uom", "item_uom.uom_id", "uom.id")
    .where("item_uom.item_id", itemId)
    .where("custom_item_uom_price.user_id", userId)
    .where("uom.supplier_id", supplierId)
    .where("item_uom.archived", false)
    .where("uom.archived", false);

  // Build result grouped by item_id
  const itemPricesMap = new Map();

  // Add custom UOM prices
  customUOMPrices.forEach((customPrice) => {
    const itemId = customPrice.item_id;

    if (!itemPricesMap.has(itemId)) {
      itemPricesMap.set(itemId, {
        item_id: itemId,
        uom_prices: [],
      });
    }

    itemPricesMap.get(itemId).uom_prices.push({
      item_uom_id: customPrice.item_uom_id,
      user_id: customPrice.user_id,
      price: customPrice.price,
      uom_name: customPrice.uom_name,
      uom_id: customPrice.uom_id,
    });
  });

  return Array.from(itemPricesMap.values());
};

export const upsertCustomUOMPrices = async (
  trx: Knex.Transaction | null,
  customUOMPrices: CustomUomPriceInput[]
) => {
  if (!customUOMPrices || customUOMPrices.length === 0) {
    throw new Error("customUOMPrices array is required");
  }

  // Check if transaction exists, if not create our own
  const shouldManageTransaction = !trx;
  let transaction = trx;

  if (!transaction) {
    const trxProvider = knex.transactionProvider();
    transaction = await trxProvider();
  }

  try {
    const result = [];

    for (const customPrice of customUOMPrices) {
      const { item_uom_id, user_id, price } = customPrice;

      if (!item_uom_id || !user_id || price === null || price === undefined) {
        throw new Error("item_uom_id, user_id, and price are required");
      }

      // Convert IDs to numbers
      const itemUomId = parseInt(item_uom_id, 10);
      const userId = parseInt(user_id, 10);

      if (isNaN(itemUomId) || isNaN(userId)) {
        throw new Error("Invalid item_uom_id or user_id");
      }

      // Upsert custom UOM price
      const upsertResult = await transaction("custom_item_uom_price")
        .insert({
          item_uom_id: itemUomId,
          user_id: userId,
          price,
        })
        .onConflict(["item_uom_id", "user_id"])
        .merge(["price"])
        .returning("*");

      result.push(upsertResult[0]);
    }

    // Only commit if we're managing the transaction
    if (shouldManageTransaction) {
      await transaction.commit();
    }

    return result;
  } catch (error) {
    // Only rollback if we're managing the transaction
    if (shouldManageTransaction) {
      await transaction.rollback();
    }
    console.error("Error upserting custom UOM prices:", error);
    throw new Error(`Error upserting custom UOM prices: ${error.message}`);
  }
};

export const getUserCustomUOMPrices = async (
  userId: string,
  supplierId: string
) => {
  if (!userId || !supplierId) {
    throw new Error("userId and supplierId are required");
  }

  // Get custom UOM prices from custom_item_uom_price table
  const customUOMPrices = await knex
    .select([
      "custom_item_uom_price.item_uom_id",
      "custom_item_uom_price.user_id",
      "custom_item_uom_price.price",
      "item_uom.item_id",
      "uom.name as uom_name",
      "uom.id as uom_id",
    ])
    .from("custom_item_uom_price")
    .innerJoin("item_uom", "custom_item_uom_price.item_uom_id", "item_uom.id")
    .innerJoin("uom", "item_uom.uom_id", "uom.id")
    .where("custom_item_uom_price.user_id", userId)
    .where("uom.supplier_id", supplierId)
    .where("item_uom.archived", false)
    .where("uom.archived", false);

  // Build result array
  const result = [];

  // Add custom UOM prices
  customUOMPrices.forEach((customPrice) => {
    result.push({
      item_uom_id: customPrice.item_uom_id,
      user_id: customPrice.user_id,
      price: customPrice.price,
      uom_name: customPrice.uom_name,
      uom_id: customPrice.uom_id,
    });
  });

  return result;
};

export const populateUsersCustomUOMPrices = async (
  users: any[],
  supplierId?: string
) => {
  if (!users || users.length === 0) {
    return;
  }

  const userIds = users.map((user) => user.id);

  // Get all custom UOM prices for these users
  let query = knex
    .select([
      "custom_item_uom_price.item_uom_id",
      "custom_item_uom_price.user_id",
      "custom_item_uom_price.price",
      "item_uom.item_id",
      "uom.name as uom_name",
      "uom.id as uom_id",
    ])
    .from("custom_item_uom_price")
    .innerJoin("item_uom", "custom_item_uom_price.item_uom_id", "item_uom.id")
    .innerJoin("uom", "item_uom.uom_id", "uom.id")
    .whereIn("custom_item_uom_price.user_id", userIds)
    .where("item_uom.archived", false)
    .where("uom.archived", false);

  // Only filter by supplier if supplierId is provided
  if (supplierId) {
    query = query.where("uom.supplier_id", supplierId);
  }

  const allCustomUOMPrices = await query;

  // Group by user_id, then by item_id
  const customUOMPricesByUser = new Map();

  // Add custom UOM prices
  allCustomUOMPrices.forEach((customPrice) => {
    const userId = customPrice.user_id;
    const itemId = customPrice.item_id;

    if (!customUOMPricesByUser.has(userId)) {
      customUOMPricesByUser.set(userId, new Map());
    }

    if (!customUOMPricesByUser.get(userId).has(itemId)) {
      customUOMPricesByUser.get(userId).set(itemId, {
        item_id: itemId,
        uom_prices: [],
      });
    }

    customUOMPricesByUser.get(userId).get(itemId).uom_prices.push({
      item_uom_id: customPrice.item_uom_id,
      user_id: customPrice.user_id,
      price: customPrice.price,
      uom_name: customPrice.uom_name,
      uom_id: customPrice.uom_id,
    });
  });

  // Assign custom UOM prices to users
  users.forEach((user) => {
    const userPrices = customUOMPricesByUser.get(user.id);
    user.custom_uom_prices = userPrices ? Array.from(userPrices.values()) : [];
  });
};

export const updateUserCustomUOMPrices = async (
  trx: Knex.Transaction,
  userId: string,
  customUOMPrices: CustomUomPriceInput[]
) => {
  if (!userId) {
    throw new Error("userId is required");
  }

  // Delete existing custom UOM prices for this user
  await trx("custom_item_uom_price").where("user_id", userId).del();

  // Insert new custom UOM prices if provided
  if (customUOMPrices && customUOMPrices.length > 0) {
    const validPrices = customUOMPrices.filter(
      (price) =>
        price.item_uom_id &&
        userId &&
        price.price !== null &&
        price.price !== undefined
    );

    if (validPrices.length > 0) {
      await trx("custom_item_uom_price").insert(
        validPrices.map((price) => ({
          item_uom_id: parseInt(price.item_uom_id, 10),
          user_id: parseInt(userId, 10),
          price: price.price,
        }))
      );
    }
  }
};
