import QuickBooksDesktopClient from "./client";

const QuickBooksToDBCustomerMap = {
  Name: "name",
  ListID: "qb_id",
  Phone: "phone_number",
  Email: "contact_email",
  "new Date:TimeModified": "updated_at",
  "new Date:TimeCreated": "created_at",
  BillAddress: "address",
  ParentRef: "store_group",
  CustomerTypeRef: "customerTypeRef",
  SalesRepRef: "salesRepRef",
};

export const pullCustomersInfo = async (
  client: QuickBooksDesktopClient,
  ids: string[] = []
) => {
  try {
    const customers = await client.getCustomers(
      ids.length > 0 ? { ListID: ids } : undefined
    );

    const customersToSave = [];
    customers.forEach((customer) => {
      if (customersToSave.find((c) => c.qb_id === customer.ListID)) return;
      const customerToSave = {};
      Object.keys(QuickBooksToDBCustomerMap).forEach((key) => {
        const keyContainsType = key.indexOf(":") !== -1;
        const keyContainsSubParts = key.indexOf(".") !== -1;

        customerToSave[QuickBooksToDBCustomerMap[key]] = !keyContainsSubParts
          ? customer[keyContainsType ? key.split(":")[1] : key]
          : (keyContainsType ? key.split(":")[1] : key)
              .split(".")
              .reduce((obj, i) => obj[i], customer);
        if (keyContainsType) {
          customerToSave[QuickBooksToDBCustomerMap[key]] = eval(
            `${key.split(":")[0].replace("_", ".")}("${
              customerToSave[QuickBooksToDBCustomerMap[key]]
            }")`
          );
        }

        if (QuickBooksToDBCustomerMap[key] === "phone_number") {
          customerToSave["phone_number"] = customerToSave["phone_number"] || "";
        }
        if (QuickBooksToDBCustomerMap[key] === "contact_email") {
          customerToSave["contact_email"] =
            customerToSave["contact_email"] || "";
        }
        if (QuickBooksToDBCustomerMap[key] === "address") {
          let address =
            customer["ShipAddress"] ||
            customer["ShipToAddress"] ||
            customer["BillAddress"];
          if (Array.isArray(address)) {
            address = address[0];
          }
          const addrString = address
            ? `${address.Addr1 || ""}${
                address.Addr2 ? ", " + address.Addr2 : ""
              }${address.City ? ", " + address.City : ""}${
                address.State ? ", " + address.State : ""
              }${address.PostalCode ? " " + address.PostalCode : ""}`
            : "";
          customerToSave["address"] = addrString;
        }
        if (QuickBooksToDBCustomerMap[key] === "store_group") {
          customerToSave["store_group"] = customer["ParentRef"]?.FullName || "";
        }
      });
      customersToSave.push(customerToSave);
    });

    return customersToSave;
  } catch (error) {
    console.error(error);
    const errorMessage = `Couldn't pull customers from QBD: ${JSON.stringify(
      error,
      undefined,
      2
    )}`;
    console.error(errorMessage);
    throw new Error(errorMessage);
  }
};
