import knex from "../../../knex/knex";
import { useDayJsWithTz } from "../../util/dayjsConfig";
import {
  Item,
  QueryOrderItemTotalsArgs,
  Supplier,
  User,
} from "../../generated/graphql";
import { getSupplierConfig } from "../../services/supplierService";

const orderItemTotals = async (_, args: QueryOrderItemTotalsArgs) => {
  const { supplierId, numWeeks, routeId, dayOffset, currentTz } =
    args.getOrderItemTotalsInput;
  const shiftByDays = `${(dayOffset || 0) >= 0 ? "+" : "-"} INTERVAL '${
    dayOffset || 0
  } days'`;
  const tzToUse = currentTz || "America/New_York";
  const today = useDayJsWithTz(tzToUse).startOf("day").toDate();
  const tomorrow = useDayJsWithTz(tzToUse)
    .startOf("day")
    .add(1, "day")
    .toDate();
  const supplierConfig = await getSupplierConfig(supplierId);
  const supplierIsDsd = supplierConfig.is_dsd || false;

  const { name: supplier } = await knex<Supplier>("supplier")
    .select("name")
    .where("id", supplierId)
    .orderBy("id", "desc")
    .first();

  const items = await knex<Item>("item")
    .select("id", "name", "nacs_category")
    .where("supplier", supplier)
    .whereNot("item.archived", true)
    .orderBy("name");

  const weekRanges = knex
    .select(
      knex.raw(`ARRAY [
                (date_trunc('week', '${today.toISOString()}'::date) - INTERVAL '7 days' * week_counter ${shiftByDays})::timestamp WITHOUT TIME ZONE AT TIME ZONE '${tzToUse}',
                (date_trunc('week', '${today.toISOString()}'::date) - INTERVAL '7 days' * week_counter ${shiftByDays} + INTERVAL '6 days')::timestamp WITHOUT TIME ZONE AT TIME ZONE '${tzToUse}'
              ] as date_range`)
    )
    .from(knex.raw(`generate_series(${numWeeks}, 1, -1) as week_counter`))
    .as("week_ranges");

  const orderItems = supplierIsDsd
    ? knex
        .select("ii.item_id", "ii.quantity")
        .from({ ii: "invoice_item" })
        .innerJoin({ i: "invoice" }, "ii.invoice_id", "i.id")
        .innerJoin({ u: "attain_user" }, "i.user_id", "u.id")
        .where("i.supplier_id", supplierId)
        .whereNot("i.archived", true)
        .andWhere(
          knex.raw(`i.date_created AT TIME ZONE '${tzToUse}'`),
          ">=",
          knex.raw("date_range[1]")
        )
        .andWhere(
          knex.raw(`i.date_created AT TIME ZONE '${tzToUse}'`),
          "<=",
          knex.raw("date_range[2]")
        )
        .modify((query) => {
          if (routeId) {
            query.where((builder) => {
              builder.whereRaw("i.config->>'custom_route' = ?", routeId);
              builder.orWhere((subBuilder) => {
                subBuilder
                  .whereRaw("i.config->>'custom_route' IS NULL")
                  .whereRaw("?::text[] && string_to_array(u.route_id, ',')", [
                    [routeId],
                  ]);
              });
            });
          }
        })
        .as("oinew")
    : knex
        .select("oi.item_id", "oi.quantity")
        .from({ oi: "order_item" })
        .innerJoin({ od: "order_detail" }, "oi.order_id", "od.id")
        .innerJoin({ u: "attain_user" }, "od.user_id", "u.id")
        .where("od.single_supplier", supplier)
        .andWhere("od.date_submitted", ">=", knex.raw("date_range[1]"))
        .andWhere("od.date_submitted", "<=", knex.raw("date_range[2]"))
        .modify((query) => {
          if (routeId) {
            query.where((builder) => {
              builder.whereRaw("od.config->>'custom_route' = ?", routeId);
              builder.orWhere((subBuilder) => {
                subBuilder
                  .whereRaw("od.config->>'custom_route' IS NULL")
                  .whereRaw("?::text[] && string_to_array(u.route_id, ',')", [
                    [routeId],
                  ]);
              });
            });
          }
        })
        .as("oinew");

  const itemTotals = knex
    .select(knex.raw(`COALESCE(SUM(oinew.quantity), 0)::int AS weekly_count`))
    .from({ i: "item" })
    .leftJoin(orderItems, "i.id", "oinew.item_id")
    .where("i.supplier", supplier)
    .whereNot("i.archived", true)
    .groupBy("i.id", "i.name")
    .orderBy("i.name");

  const orderItemByWeekTotals = await knex
    .select(knex.raw(`array(?) as weekly_totals`, itemTotals))
    .from(weekRanges);

  // Query for next day presale totals
  const nextDayPresaleItems = knex
    .select("ii.item_id", "ii.quantity")
    .from({ ii: "invoice_item" })
    .innerJoin({ i: "invoice" }, "ii.invoice_id", "i.id")
    .innerJoin({ od: "order_detail" }, "i.order_id", "od.id")
    .innerJoin({ u: "attain_user" }, "i.user_id", "u.id")
    .where("i.supplier_id", supplierId)
    .whereNot("i.archived", true)
    .where("od.status", "In Transit")
    .modify((query) => {
      // Check if today is Friday (day 5 in dayjs)
      const isFriday = useDayJsWithTz(tzToUse).day() === 5;
      if (isFriday) {
        // If today is Friday, compare with next Monday (add 3 days)
        const nextMonday = useDayJsWithTz(tzToUse)
          .startOf("day")
          .add(3, "day")
          .toDate();
        console.log("nextMonday", nextMonday);
        query.whereRaw(
          `DATE(i.date_created) = DATE('${nextMonday.toISOString()}')`
        );
      } else {
        // Use tomorrow as before
        query.whereRaw(
          `DATE(i.date_created) = DATE('${tomorrow.toISOString()}')`
        );
      }
    })
    .modify((query) => {
      if (routeId) {
        query.where((builder) => {
          builder.whereRaw("i.config->>'custom_route' = ?", routeId);
          builder.orWhere((subBuilder) => {
            subBuilder
              .whereRaw("i.config->>'custom_route' IS NULL")
              .whereRaw("?::text[] && string_to_array(u.route_id, ',')", [
                [routeId],
              ]);
          });
        });
      }
    });

  const nextDayPresaleTotals = await knex
    .select("i.id")
    .select(knex.raw(`COALESCE(SUM(npi.quantity), 0)::int AS presale_count`))
    .from({ i: "item" })
    .leftJoin(nextDayPresaleItems.as("npi"), "i.id", "npi.item_id")
    .where("i.supplier", supplier)
    .whereNot("i.archived", true)
    .groupBy("i.id")
    .orderBy("i.name");

  // Create a lookup map for presale totals by item id
  const presaleTotalsMap = nextDayPresaleTotals.reduce((acc, item) => {
    acc[item.id] = item.presale_count;
    return acc;
  }, {});

  return items.map((item, index) => ({
    ...item,
    weekly_totals: orderItemByWeekTotals.map(
      (week) => week["weekly_totals"][index]
    ),
    next_day_presale_totals: presaleTotalsMap[item.id] || 0,
  }));
};

export default orderItemTotals;
