import { quickBooksDesktopConfig } from "../../src/config/environment";
import QuickBooksDesktopClient from "../../src/lib/quickBooksDesktop/client";
import {
  syncCreditMemos,
  syncCustomers,
  syncInvoices,
  syncItems,
} from "../../src/services/integrationService/quickBooksDesktop";

/*
# Whitestone Syncing Script

This script is used to sync the Whitestone invoices/credit memos/customers
/items from our server to their QuickBooks Desktop. We use conductor for
QB APIs, they can also be used in other scripts.

To run the script:
1. You need the quickBooksDesktopConfig credentials in your .env file.
2. Uncomment the desired functions to run.
3. Run the script with `ts-node scripts/tools/whitestoneSync.ts > whitestoneSync.log 2>&1`
   - I like to log to a file incase of any errors.

*/
// QuickBooks Desktop Client
// const client = new QuickBooksDesktopClient(quickBooksDesktopConfig.userId);

// Sync Whitestone Invoices
// const syncWhitestoneInvoices = async () => {
//   await syncInvoices(client, "68", false);
//   await syncCreditMemos(client, "68", false, true);
// };

// Sync Whitestone Customers
// const syncWhitestoneCustomers = async () => {
//   await syncCustomers(client, "68", false);
// };

// // Sync Whitestone Items
// const syncWhitestoneItems = async () => {
//   await syncItems(client, "68", "Whitestone Foods", false);
// };

// syncWhitestoneCustomers();
// syncWhitestoneInvoices();
// syncWhitestoneItems();
