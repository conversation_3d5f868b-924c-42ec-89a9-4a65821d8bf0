import {
  createPromotion,
  getPromotions,
  getApplicablePromotions,
  calculatePromotionDiscount,
  recordPromotionUsage,
  getPromotionUsageForOrders,
  getPromotionTypes,
} from "../index";
import { createOrder } from "../../orderService/createOrder";
import createInvoiceFromOrder from "../../invoiceService/createInvoiceFromOrder";
import { getOrders } from "../../orderService/orderService";
import knex from "../../../../knex/knex";
import dayjs from "dayjs";

describe("Promotion Service Integration Tests", () => {
  let testSupplierId: string;
  let testUserId: string;
  let testItemId: string;
  const cleanupPromotionIds: string[] = [];
  const cleanupOrderIds: string[] = [];

  beforeAll(async () => {
    // Use test supplier and user IDs
    testSupplierId = "31";
    testUserId = "1";
    testItemId = "1";

    // Clear existing promotions for clean test environment
    await knex("promotion_usage")
      .where(
        "promotion_id",
        "in",
        knex
          .select("id")
          .from("promotions")
          .where("supplier_id", testSupplierId)
      )
      .delete();
    await knex("promotion_items")
      .where(
        "promotion_id",
        "in",
        knex
          .select("id")
          .from("promotions")
          .where("supplier_id", testSupplierId)
      )
      .delete();
    await knex("promotion_users")
      .where(
        "promotion_id",
        "in",
        knex
          .select("id")
          .from("promotions")
          .where("supplier_id", testSupplierId)
      )
      .delete();
    await knex("promotions").where("supplier_id", testSupplierId).delete();
  });

  afterAll(async () => {
    // Clean up test data
    if (cleanupPromotionIds.length > 0) {
      await knex("promotion_usage")
        .whereIn("promotion_id", cleanupPromotionIds)
        .delete();
      await knex("promotions").whereIn("id", cleanupPromotionIds).delete();
    }
    if (cleanupOrderIds.length > 0) {
      await knex("invoice_item")
        .whereIn(
          "invoice_id",
          knex.select("id").from("invoice").whereIn("order_id", cleanupOrderIds)
        )
        .delete();
      await knex("invoice").whereIn("order_id", cleanupOrderIds).delete();
      await knex("order_item").whereIn("order_id", cleanupOrderIds).delete();
      await knex("order_status").whereIn("order_id", cleanupOrderIds).delete();
      await knex("order_detail").whereIn("id", cleanupOrderIds).delete();
    }
  });

  describe("Promotion CRUD Operations", () => {
    it("should create a new promotion", async () => {
      const promotionData = {
        name: "Test Integration Promotion",
        supplier_id: testSupplierId,
        promotion_type: "2", // BUY_X_GET_DISCOUNT
        start_date: dayjs().subtract(1, "day").toISOString(),
        end_date: dayjs().add(1, "day").toISOString(),
        applies_to_all_items: true,
        applies_to_all_users: true,
        buy_quantity: 5,
        discount_percentage: 15,
        active: true,
      };

      const promotion = await createPromotion(promotionData);
      cleanupPromotionIds.push(promotion.id);

      expect(promotion).toBeDefined();
      expect(promotion.name).toBe(promotionData.name);
      expect(promotion.supplier_id).toBe(parseInt(testSupplierId));
      expect(promotion.buy_quantity).toBe(5);
      expect(promotion.discount_percentage).toBe("15");
      expect(promotion.active).toBe(true);
    });

    it("should retrieve promotions", async () => {
      const { promotions } = await getPromotions({
        supplierId: testSupplierId,
        filters: { active: true },
      });

      expect(promotions).toBeDefined();
      expect(Array.isArray(promotions)).toBe(true);
      expect(promotions.length).toBeGreaterThan(0);
    });

    it("should get promotion types", async () => {
      const promotionTypes = await getPromotionTypes();

      expect(promotionTypes).toBeDefined();
      expect(Array.isArray(promotionTypes)).toBe(true);
      expect(promotionTypes.length).toBeGreaterThan(0);
      expect(promotionTypes[0]).toHaveProperty("id");
      expect(promotionTypes[0]).toHaveProperty("name");
      expect(promotionTypes[0]).toHaveProperty("code");
    });
  });

  describe("Promotion Application Logic", () => {
    let testPromotion: any;

    beforeEach(async () => {
      // Create a test promotion for each test
      testPromotion = await createPromotion({
        name: "Test Buy 5 Get 15% Off",
        supplier_id: testSupplierId,
        promotion_type: "2", // BUY_X_GET_DISCOUNT
        start_date: dayjs().subtract(1, "day").toISOString(),
        end_date: dayjs().add(1, "day").toISOString(),
        applies_to_all_items: true,
        applies_to_all_users: true,
        buy_quantity: 5,
        discount_percentage: 15,
        active: true,
      });
      cleanupPromotionIds.push(testPromotion.id);
    });

    it("should find applicable promotions", async () => {
      const items = [{ item_id: testItemId, quantity: 5, price: 10.0 }];

      const applicablePromotions = await getApplicablePromotions(
        testSupplierId,
        testUserId,
        items
      );

      expect(applicablePromotions).toBeDefined();
      expect(applicablePromotions.length).toBeGreaterThan(0);
      expect(applicablePromotions[0]).toHaveProperty("id");
      expect(applicablePromotions[0]).toHaveProperty("name");
    });

    it("should not find applicable promotions when quantity is insufficient", async () => {
      const items = [
        { item_id: testItemId, quantity: 2, price: 10.0 }, // Less than buy_quantity of 5
      ];

      const applicablePromotions = await getApplicablePromotions(
        testSupplierId,
        testUserId,
        items
      );

      expect(applicablePromotions).toBeDefined();
      expect(applicablePromotions.length).toBe(0);
    });

    it("should calculate promotion discount correctly", async () => {
      const items = [{ item_id: testItemId, quantity: 5, price: 10.0 }];
      const orderSubtotal = 50.0;

      const { discountAmount, affectedItems } =
        await calculatePromotionDiscount(
          testSupplierId,
          testPromotion.id,
          items,
          orderSubtotal
        );

      // 15% discount on 5 items at $10 each = $7.50
      expect(discountAmount).toBe(7.5);
      expect(affectedItems).toBeDefined();
      expect(affectedItems.length).toBe(1);
      expect(affectedItems[0].item_id).toBe(testItemId);
      expect(affectedItems[0].discountAmount).toBe(7.5);
    });

    it("should record promotion usage", async () => {
      // Create a real order for the foreign key constraint
      const order = await createOrder({
        supplierId: testSupplierId,
        userId: testUserId,
        orderItems: [
          {
            id: testItemId,
            quantity: 1,
            price_purchased_at: 10.0,
          },
        ],
        deliveryDate: new Date(),
        order_name: "Test Usage Order",
        notes: "Test promotion usage",
        config: {},
        status: "In Transit",
      });
      cleanupOrderIds.push(order.id);

      const promotions = [{ id: testPromotion.id, discountAmount: 7.5 }];

      await recordPromotionUsage(promotions, order.id.toString(), testUserId);

      // Verify usage was recorded
      const usage = await knex("promotion_usage")
        .where("promotion_id", testPromotion.id)
        .where("order_id", order.id)
        .first();

      expect(usage).toBeDefined();
      expect(usage.user_id).toBe(parseInt(testUserId));
      expect(usage.discount_amount).toBe("7.5");

      // Clean up
      await knex("promotion_usage").where("id", usage.id).delete();
    });

    it("should get promotion usage for orders", async () => {
      // Create a real order for the foreign key constraint
      const order = await createOrder({
        supplierId: testSupplierId,
        userId: testUserId,
        orderItems: [
          {
            id: testItemId,
            quantity: 1,
            price_purchased_at: 10.0,
          },
        ],
        deliveryDate: new Date(),
        order_name: "Test Usage Order 2",
        notes: "Test promotion usage",
        config: {},
        status: "In Transit",
      });
      cleanupOrderIds.push(order.id);

      const promotions = [{ id: testPromotion.id, discountAmount: 7.5 }];

      // Record usage
      await recordPromotionUsage(promotions, order.id.toString(), testUserId);

      // Get usage
      const usageRecords = await getPromotionUsageForOrders(testSupplierId, [
        order.id.toString(),
      ]);

      expect(usageRecords).toBeDefined();
      expect(usageRecords.length).toBe(1);
      expect(usageRecords[0].promotion_id).toBe(testPromotion.id);
      expect(usageRecords[0].order_id).toBe(order.id);
      expect(usageRecords[0].user_id).toBe(parseInt(testUserId));

      // Clean up
      await knex("promotion_usage")
        .where("promotion_id", testPromotion.id)
        .where("order_id", order.id)
        .delete();
    });
  });

  describe("End-to-End Promotion Flow", () => {
    it("should create order with promotion applied and generate invoice", async () => {
      // 1. Create a promotion
      const promotion = await createPromotion({
        name: "E2E Test Promotion",
        supplier_id: testSupplierId,
        promotion_type: "2", // BUY_X_GET_DISCOUNT
        start_date: dayjs().subtract(1, "day").toISOString(),
        end_date: dayjs().add(1, "day").toISOString(),
        applies_to_all_items: true,
        applies_to_all_users: true,
        buy_quantity: 3,
        discount_percentage: 10,
        active: true,
      });
      cleanupPromotionIds.push(promotion.id);

      // 2. Create order with qualifying items (createOrder should auto-apply promotion)
      const order = await createOrder({
        supplierId: testSupplierId,
        userId: testUserId,
        orderItems: [
          {
            id: testItemId,
            quantity: 3,
            price_purchased_at: 15.0,
          },
        ],
        deliveryDate: new Date(),
        order_name: "E2E Test Order",
        notes: "Integration test order",
        config: {},
        status: "In Transit",
      });
      cleanupOrderIds.push(order.id);

      // 3. Verify order has promotion applied
      expect(order.discount).toBe(4.5); // 10% of (3 * $15)
      expect(order.config.appliedPromotions).toBeDefined();
      expect(order.config.appliedPromotions.length).toBe(1);
      expect(order.config.appliedPromotions[0].id).toBe(promotion.id);

      // 4. Update order status to delivered
      await knex("order_detail").where("id", order.id).update({
        status: "Delivered",
      });

      // 5. Create invoice from order
      const invoice = await createInvoiceFromOrder(
        testSupplierId,
        order.id.toString()
      );

      // 6. Verify invoice has correct discount and config
      expect(Number(invoice.discount)).toBe(4.5);
      expect(Number(invoice.total)).toBe(40.5); // 45 - 4.5
      expect(invoice.config.appliedPromotions).toBeDefined();
      expect(invoice.config.appliedPromotions.length).toBe(1);

      // 7. Get final order with promotions populated
      const { orders } = await getOrders({
        supplierId: testSupplierId,
        filters: { ids: [order.id] },
      });

      const finalOrder = orders[0];
      expect(finalOrder.discount).toBe(4.5);
      expect(finalOrder.promotions).toBeDefined();
      expect(finalOrder.promotions.length).toBe(1);
      expect(finalOrder.promotions[0].promotion_id).toBe(promotion.id);
      expect(finalOrder.invoice).toBeDefined();
      expect(finalOrder.invoice.discount).toBe("4.5");
    });

    it("should handle multiple promotion scenarios correctly", async () => {
      // Create multiple promotions
      const promotion1 = await createPromotion({
        name: "First Promotion",
        supplier_id: testSupplierId,
        promotion_type: "2", // BUY_X_GET_DISCOUNT
        start_date: dayjs().subtract(1, "day").toISOString(),
        end_date: dayjs().add(1, "day").toISOString(),
        applies_to_all_items: true,
        applies_to_all_users: true,
        buy_quantity: 2,
        discount_percentage: 20,
        active: true,
      });
      cleanupPromotionIds.push(promotion1.id);

      const promotion2 = await createPromotion({
        name: "Second Promotion",
        supplier_id: testSupplierId,
        promotion_type: "2", // BUY_X_GET_DISCOUNT
        start_date: dayjs().subtract(1, "day").toISOString(),
        end_date: dayjs().add(1, "day").toISOString(),
        applies_to_all_items: true,
        applies_to_all_users: true,
        buy_quantity: 3,
        discount_percentage: 15,
        active: true,
      });
      cleanupPromotionIds.push(promotion2.id);

      // Create order that qualifies for both promotions
      const order = await createOrder({
        supplierId: testSupplierId,
        userId: testUserId,
        orderItems: [
          {
            id: testItemId,
            quantity: 4,
            price_purchased_at: 10.0,
          },
        ],
        deliveryDate: new Date(),
        order_name: "Multi-Promotion Test Order",
        notes: "Testing multiple promotions",
        config: {},
        status: "In Transit",
      });
      cleanupOrderIds.push(order.id);

      // Verify multiple promotions are applied
      expect(order.discount).toBeGreaterThan(0);
      expect(order.config.appliedPromotions).toBeDefined();
      expect(order.config.appliedPromotions.length).toBeGreaterThan(1);
    });
  });

  describe("Different Promotion Types", () => {
    beforeEach(async () => {
      // Clear all promotions before each test to avoid interference
      await knex("promotion_usage").where("promotion_id", "in", 
        knex.select("id").from("promotions").where("supplier_id", testSupplierId)
      ).delete();
      await knex("promotion_items").where("promotion_id", "in", 
        knex.select("id").from("promotions").where("supplier_id", testSupplierId)
      ).delete();
      await knex("promotion_users").where("promotion_id", "in", 
        knex.select("id").from("promotions").where("supplier_id", testSupplierId)
      ).delete();
      await knex("promotions").where("supplier_id", testSupplierId).delete();
    });
    describe("BUY_X_GET_PRICE Promotion", () => {
      it("should apply fixed price discount when buying required quantity", async () => {
        // Debug: Check available promotion types
        const promotionTypes = await getPromotionTypes();
        console.log("Available promotion types:", promotionTypes.map(t => ({ id: t.id, name: t.name, code: t.code })));
        
        // Find BUY_X_GET_PRICE type
        const buyXGetPriceType = promotionTypes.find(t => t.code === 'BUY_X_GET_PRICE');
        if (!buyXGetPriceType) {
          throw new Error('BUY_X_GET_PRICE promotion type not found');
        }
        
        console.log("Using BUY_X_GET_PRICE type:", buyXGetPriceType.id);
        
        const promotion = await createPromotion({
          name: "Buy 5 Get Fixed Price $8",
          supplier_id: testSupplierId,
          promotion_type: buyXGetPriceType.id.toString(),
          start_date: dayjs().subtract(1, "day").toISOString(),
          end_date: dayjs().add(1, "day").toISOString(),
          applies_to_all_items: true,
          applies_to_all_users: true,
          buy_quantity: 5,
          discount_amount: 8.0, // Fixed price
          active: true,
        });
        cleanupPromotionIds.push(promotion.id);

        const items = [{ item_id: testItemId, quantity: 5, price: 12.0 }];
        const orderSubtotal = 60.0;

        const { discountAmount } = await calculatePromotionDiscount(
          testSupplierId,
          promotion.id,
          items,
          orderSubtotal
        );

        // Should get fixed discount amount
        expect(Number(discountAmount)).toBe(8.0);
      });

      it("should not apply when quantity is insufficient", async () => {
        const promotionTypes = await getPromotionTypes();
        const buyXGetPriceType = promotionTypes.find(t => t.code === 'BUY_X_GET_PRICE');
        
        const promotion = await createPromotion({
          name: "Buy 10 Get Fixed Price $15",
          supplier_id: testSupplierId,
          promotion_type: buyXGetPriceType.id.toString(),
          start_date: dayjs().subtract(1, "day").toISOString(),
          end_date: dayjs().add(1, "day").toISOString(),
          applies_to_all_items: true,
          applies_to_all_users: true,
          buy_quantity: 10,
          discount_amount: 15.0,
          active: true,
        });
        cleanupPromotionIds.push(promotion.id);

        const items = [{ item_id: testItemId, quantity: 5, price: 12.0 }]; // Less than required
        const orderSubtotal = 60.0;

        const { discountAmount } = await calculatePromotionDiscount(
          testSupplierId,
          promotion.id,
          items,
          orderSubtotal
        );

        expect(discountAmount).toBe(0);
      });
    });

    describe("BUY_X_GET_Y_FREE Promotion", () => {
      it("should apply free items when buying required quantity", async () => {
        const promotionTypes = await getPromotionTypes();
        const buyXGetYFreeType = promotionTypes.find(t => t.code === 'BUY_X_GET_Y_FREE');
        
        const promotion = await createPromotion({
          name: "Buy 3 Get 1 Free",
          supplier_id: testSupplierId,
          promotion_type: buyXGetYFreeType.id.toString(),
          start_date: dayjs().subtract(1, "day").toISOString(),
          end_date: dayjs().add(1, "day").toISOString(),
          applies_to_all_items: true,
          applies_to_all_users: true,
          buy_quantity: 3,
          free_quantity: 1,
          active: true,
        });
        cleanupPromotionIds.push(promotion.id);

        const items = [{ item_id: testItemId, quantity: 4, price: 10.0 }];
        const orderSubtotal = 40.0;

        const { discountAmount } = await calculatePromotionDiscount(
          testSupplierId,
          promotion.id,
          items,
          orderSubtotal
        );

        // Should get 1 free item worth $10
        expect(discountAmount).toBe(10.0);
      });

      it("should handle multiple sets correctly", async () => {
        const promotionTypes = await getPromotionTypes();
        const buyXGetYFreeType = promotionTypes.find(t => t.code === 'BUY_X_GET_Y_FREE');
        console.log("BUY_X_GET_Y_FREE type:", buyXGetYFreeType);
        
        const promotion = await createPromotion({
          name: "Buy 2 Get 1 Free",
          supplier_id: testSupplierId,
          promotion_type: buyXGetYFreeType.id.toString(),
          start_date: dayjs().subtract(1, "day").toISOString(),
          end_date: dayjs().add(1, "day").toISOString(),
          applies_to_all_items: true,
          applies_to_all_users: true,
          buy_quantity: 2,
          free_quantity: 1,
          active: true,
        });
        cleanupPromotionIds.push(promotion.id);

        const items = [{ item_id: testItemId, quantity: 6, price: 8.0 }]; // 2 complete sets
        const orderSubtotal = 48.0;

        console.log("Promotion created:", promotion);
        console.log("Items:", items);
        console.log("Order subtotal:", orderSubtotal);

        const { discountAmount } = await calculatePromotionDiscount(
          testSupplierId,
          promotion.id,
          items,
          orderSubtotal
        );

        console.log("Discount amount:", discountAmount);
        
        // Should get 2 free items worth $16 (2 sets * 1 free item * $8)
        expect(Number(discountAmount)).toBe(16.0);
      });
    });

    describe("FIXED_DISCOUNT Promotion", () => {
      it("should apply fixed discount amount to order", async () => {
        const promotion = await createPromotion({
          name: "Fixed $5 Off",
          supplier_id: testSupplierId,
          promotion_type: "4", // FIXED_DISCOUNT
          start_date: dayjs().subtract(1, "day").toISOString(),
          end_date: dayjs().add(1, "day").toISOString(),
          applies_to_all_items: true,
          applies_to_all_users: true,
          discount_amount: 5.0,
          active: true,
        });
        cleanupPromotionIds.push(promotion.id);

        const items = [{ item_id: testItemId, quantity: 3, price: 15.0 }];
        const orderSubtotal = 45.0;

        const { discountAmount } = await calculatePromotionDiscount(
          testSupplierId,
          promotion.id,
          items,
          orderSubtotal
        );

        expect(Number(discountAmount)).toBe(5.0);
      });
    });

    describe("PERCENTAGE_OFF Promotion", () => {
      it("should apply percentage discount to order total", async () => {
        const promotion = await createPromotion({
          name: "25% Off Everything",
          supplier_id: testSupplierId,
          promotion_type: "5", // PERCENTAGE_OFF
          start_date: dayjs().subtract(1, "day").toISOString(),
          end_date: dayjs().add(1, "day").toISOString(),
          applies_to_all_items: true,
          applies_to_all_users: true,
          discount_percentage: 25,
          active: true,
        });
        cleanupPromotionIds.push(promotion.id);

        const items = [{ item_id: testItemId, quantity: 4, price: 20.0 }];
        const orderSubtotal = 80.0;

        const { discountAmount } = await calculatePromotionDiscount(
          testSupplierId,
          promotion.id,
          items,
          orderSubtotal
        );

        // 25% of $80 = $20
        expect(discountAmount).toBe(20.0);
      });
    });

    describe("Minimum Order Amount Requirements", () => {
      it("should apply promotion when order meets minimum amount", async () => {
        const promotion = await createPromotion({
          name: "10% Off Orders Over $50",
          supplier_id: testSupplierId,
          promotion_type: "5", // PERCENTAGE_OFF
          start_date: dayjs().subtract(1, "day").toISOString(),
          end_date: dayjs().add(1, "day").toISOString(),
          applies_to_all_items: true,
          applies_to_all_users: true,
          discount_percentage: 10,
          min_order_amount: 50.0,
          active: true,
        });
        cleanupPromotionIds.push(promotion.id);

        const items = [{ item_id: testItemId, quantity: 6, price: 10.0 }];

        const applicablePromotions = await getApplicablePromotions(
          testSupplierId,
          testUserId,
          items
        );

        // Should find the promotion since order total ($60) meets minimum ($50)
        expect(applicablePromotions.length).toBeGreaterThan(0);
        expect(applicablePromotions.some(p => p.id === promotion.id)).toBe(true);
      });

      it("should not apply promotion when order is below minimum amount", async () => {
        const promotion = await createPromotion({
          name: "10% Off Orders Over $100",
          supplier_id: testSupplierId,
          promotion_type: "5", // PERCENTAGE_OFF
          start_date: dayjs().subtract(1, "day").toISOString(),
          end_date: dayjs().add(1, "day").toISOString(),
          applies_to_all_items: true,
          applies_to_all_users: true,
          discount_percentage: 10,
          min_order_amount: 100.0,
          active: true,
        });
        cleanupPromotionIds.push(promotion.id);

        const items = [{ item_id: testItemId, quantity: 3, price: 15.0 }]; // Total: $45

        const applicablePromotions = await getApplicablePromotions(
          testSupplierId,
          testUserId,
          items
        );

        // Should not find the promotion since order total ($45) is below minimum ($100)
        expect(applicablePromotions.some(p => p.id === promotion.id)).toBe(false);
      });
    });

    describe("End-to-End Different Promotion Types", () => {
      it("should create order with BUY_X_GET_Y_FREE promotion applied", async () => {
        const promotion = await createPromotion({
          name: "Buy 2 Get 1 Free E2E",
          supplier_id: testSupplierId,
          promotion_type: "3", // BUY_X_GET_Y_FREE
          start_date: dayjs().subtract(1, "day").toISOString(),
          end_date: dayjs().add(1, "day").toISOString(),
          applies_to_all_items: true,
          applies_to_all_users: true,
          buy_quantity: 2,
          free_quantity: 1,
          active: true,
        });
        cleanupPromotionIds.push(promotion.id);

        const order = await createOrder({
          supplierId: testSupplierId,
          userId: testUserId,
          orderItems: [
            {
              id: testItemId,
              quantity: 3, // Qualifies for 1 free item
              price_purchased_at: 12.0,
            },
          ],
          deliveryDate: new Date(),
          order_name: "BUY_X_GET_Y_FREE Test Order",
          notes: "Testing buy X get Y free",
          config: {},
          status: "In Transit",
        });
        cleanupOrderIds.push(order.id);

        // Should have discount for 1 free item = $12
        expect(order.discount).toBe(12.0);
        expect(order.config.appliedPromotions).toBeDefined();
        expect(order.config.appliedPromotions.length).toBe(1);
        expect(order.config.appliedPromotions[0].id).toBe(promotion.id);
      });

      it("should create order with FIXED_DISCOUNT promotion applied", async () => {
        const promotion = await createPromotion({
          name: "Fixed $7 Off E2E",
          supplier_id: testSupplierId,
          promotion_type: "4", // FIXED_DISCOUNT
          start_date: dayjs().subtract(1, "day").toISOString(),
          end_date: dayjs().add(1, "day").toISOString(),
          applies_to_all_items: true,
          applies_to_all_users: true,
          discount_amount: 7.0,
          active: true,
        });
        cleanupPromotionIds.push(promotion.id);

        const order = await createOrder({
          supplierId: testSupplierId,
          userId: testUserId,
          orderItems: [
            {
              id: testItemId,
              quantity: 2,
              price_purchased_at: 25.0,
            },
          ],
          deliveryDate: new Date(),
          order_name: "FIXED_DISCOUNT Test Order",
          notes: "Testing fixed discount",
          config: {},
          status: "In Transit",
        });
        cleanupOrderIds.push(order.id);

        // Should have $7 discount
        expect(order.discount).toBe(7.0);
        expect(order.config.appliedPromotions).toBeDefined();
        expect(order.config.appliedPromotions.length).toBe(1);
        expect(order.config.appliedPromotions[0].id).toBe(promotion.id);
      });
    });
  });

  describe("Specific Items and Users Promotions", () => {
    let testItemId2: string;
    let testUserId2: string;

    beforeAll(async () => {
      // Set up additional test data
      testItemId2 = "2";
      testUserId2 = "2";
    });

    beforeEach(async () => {
      // Clear all promotions before each test to avoid interference
      await knex("promotion_usage").where("promotion_id", "in", 
        knex.select("id").from("promotions").where("supplier_id", testSupplierId)
      ).delete();
      await knex("promotion_items").where("promotion_id", "in", 
        knex.select("id").from("promotions").where("supplier_id", testSupplierId)
      ).delete();
      await knex("promotion_users").where("promotion_id", "in", 
        knex.select("id").from("promotions").where("supplier_id", testSupplierId)
      ).delete();
      await knex("promotions").where("supplier_id", testSupplierId).delete();
    });

    describe("Item-Specific Promotions", () => {
      it("should apply promotion only to specified items", async () => {
        const promotion = await createPromotion({
          name: "Specific Item 15% Off",
          supplier_id: testSupplierId,
          promotion_type: "2", // BUY_X_GET_DISCOUNT
          start_date: dayjs().subtract(1, "day").toISOString(),
          end_date: dayjs().add(1, "day").toISOString(),
          applies_to_all_items: false,
          applies_to_all_users: true,
          buy_quantity: 2,
          discount_percentage: 15,
          active: true,
          items: [{ item_id: testItemId }], // Only applies to testItemId
        });
        cleanupPromotionIds.push(promotion.id);

        // Test with qualifying item
        const itemsWithQualifyingItem = [
          { item_id: testItemId, quantity: 3, price: 10.0 }, // Should qualify
          { item_id: testItemId2, quantity: 5, price: 12.0 }, // Should not qualify
        ];

        const applicablePromotions = await getApplicablePromotions(
          testSupplierId,
          testUserId,
          itemsWithQualifyingItem
        );

        expect(applicablePromotions.length).toBe(1);
        expect(applicablePromotions[0].id).toBe(promotion.id);

        // Calculate discount - should only apply to testItemId
        const { discountAmount, affectedItems } = await calculatePromotionDiscount(
          testSupplierId,
          promotion.id,
          itemsWithQualifyingItem,
          90.0
        );

        // 15% discount on 3 items at $10 each = $4.50
        expect(discountAmount).toBe(4.5);
        expect(affectedItems.length).toBe(1);
        expect(affectedItems[0].item_id).toBe(testItemId);
      });

      it("should not apply promotion when items don't match", async () => {
        const promotion = await createPromotion({
          name: "Specific Item Only Promo",
          supplier_id: testSupplierId,
          promotion_type: "5", // PERCENTAGE_OFF
          start_date: dayjs().subtract(1, "day").toISOString(),
          end_date: dayjs().add(1, "day").toISOString(),
          applies_to_all_items: false,
          applies_to_all_users: true,
          discount_percentage: 20,
          active: true,
          items: [{ item_id: testItemId }], // Only applies to testItemId
        });
        cleanupPromotionIds.push(promotion.id);

        // Test with non-qualifying items
        const itemsWithoutQualifyingItem = [
          { item_id: testItemId2, quantity: 3, price: 10.0 }, // Different item
        ];

        const applicablePromotions = await getApplicablePromotions(
          testSupplierId,
          testUserId,
          itemsWithoutQualifyingItem
        );

        expect(applicablePromotions.length).toBe(0);
      });

      it("should apply promotion to multiple specified items", async () => {
        const promotion = await createPromotion({
          name: "Multi-Item Specific Promo",
          supplier_id: testSupplierId,
          promotion_type: "3", // BUY_X_GET_Y_FREE
          start_date: dayjs().subtract(1, "day").toISOString(),
          end_date: dayjs().add(1, "day").toISOString(),
          applies_to_all_items: false,
          applies_to_all_users: true,
          buy_quantity: 2,
          free_quantity: 1,
          active: true,
          items: [
            { item_id: testItemId },
            { item_id: testItemId2 }
          ],
        });
        cleanupPromotionIds.push(promotion.id);

        const items = [
          { item_id: testItemId, quantity: 3, price: 10.0 }, // Qualifies
          { item_id: testItemId2, quantity: 3, price: 15.0 }, // Qualifies
          { item_id: "3", quantity: 5, price: 8.0 }, // Should not qualify
        ];

        const applicablePromotions = await getApplicablePromotions(
          testSupplierId,
          testUserId,
          items
        );

        expect(applicablePromotions.length).toBe(1);

        const { discountAmount, affectedItems } = await calculatePromotionDiscount(
          testSupplierId,
          promotion.id,
          items,
          115.0
        );

        // Should get 1 free item for each qualifying item = $10 + $15 = $25
        expect(discountAmount).toBe(25.0);
        expect(affectedItems.length).toBe(2);
        expect(affectedItems.some(item => item.item_id === testItemId)).toBe(true);
        expect(affectedItems.some(item => item.item_id === testItemId2)).toBe(true);
      });
    });

    describe("User-Specific Promotions", () => {
      it("should apply promotion only to specified users", async () => {
        const promotion = await createPromotion({
          name: "VIP User 25% Off",
          supplier_id: testSupplierId,
          promotion_type: "5", // PERCENTAGE_OFF
          start_date: dayjs().subtract(1, "day").toISOString(),
          end_date: dayjs().add(1, "day").toISOString(),
          applies_to_all_items: true,
          applies_to_all_users: false,
          discount_percentage: 25,
          active: true,
          users: [{ user_id: testUserId }], // Only applies to testUserId
        });
        cleanupPromotionIds.push(promotion.id);

        const items = [{ item_id: testItemId, quantity: 2, price: 20.0 }];

        // Test with qualifying user
        const applicablePromotionsForUser1 = await getApplicablePromotions(
          testSupplierId,
          testUserId,
          items
        );

        expect(applicablePromotionsForUser1.length).toBe(1);
        expect(applicablePromotionsForUser1[0].id).toBe(promotion.id);

        // Test with non-qualifying user
        const applicablePromotionsForUser2 = await getApplicablePromotions(
          testSupplierId,
          testUserId2,
          items
        );

        expect(applicablePromotionsForUser2.length).toBe(0);
      });

      it("should apply promotion to multiple specified users", async () => {
        const promotion = await createPromotion({
          name: "Multi-User Specific Promo",
          supplier_id: testSupplierId,
          promotion_type: "4", // FIXED_DISCOUNT
          start_date: dayjs().subtract(1, "day").toISOString(),
          end_date: dayjs().add(1, "day").toISOString(),
          applies_to_all_items: true,
          applies_to_all_users: false,
          discount_amount: 10.0,
          active: true,
          users: [
            { user_id: testUserId },
            { user_id: testUserId2 }
          ],
        });
        cleanupPromotionIds.push(promotion.id);

        const items = [{ item_id: testItemId, quantity: 3, price: 15.0 }];

        // Test with first qualifying user
        const applicablePromotionsForUser1 = await getApplicablePromotions(
          testSupplierId,
          testUserId,
          items
        );

        expect(applicablePromotionsForUser1.length).toBe(1);

        // Test with second qualifying user
        const applicablePromotionsForUser2 = await getApplicablePromotions(
          testSupplierId,
          testUserId2,
          items
        );

        expect(applicablePromotionsForUser2.length).toBe(1);

        // Test with non-qualifying user
        const applicablePromotionsForUser3 = await getApplicablePromotions(
          testSupplierId,
          "999", // Non-existent user
          items
        );

        expect(applicablePromotionsForUser3.length).toBe(0);
      });
    });

    describe("Combined Item and User Specific Promotions", () => {
      it("should apply promotion only when both item and user criteria are met", async () => {
        const promotion = await createPromotion({
          name: "Specific User & Item Combo",
          supplier_id: testSupplierId,
          promotion_type: "2", // BUY_X_GET_DISCOUNT
          start_date: dayjs().subtract(1, "day").toISOString(),
          end_date: dayjs().add(1, "day").toISOString(),
          applies_to_all_items: false,
          applies_to_all_users: false,
          buy_quantity: 2,
          discount_percentage: 30,
          active: true,
          items: [{ item_id: testItemId }],
          users: [{ user_id: testUserId }],
        });
        cleanupPromotionIds.push(promotion.id);

        const items = [{ item_id: testItemId, quantity: 3, price: 12.0 }];

        // Test with qualifying user and item
        const applicablePromotions1 = await getApplicablePromotions(
          testSupplierId,
          testUserId,
          items
        );
        expect(applicablePromotions1.length).toBe(1);

        // Test with qualifying item but wrong user
        const applicablePromotions2 = await getApplicablePromotions(
          testSupplierId,
          testUserId2,
          items
        );
        expect(applicablePromotions2.length).toBe(0);

        // Test with qualifying user but wrong item
        const wrongItems = [{ item_id: testItemId2, quantity: 3, price: 12.0 }];
        const applicablePromotions3 = await getApplicablePromotions(
          testSupplierId,
          testUserId,
          wrongItems
        );
        expect(applicablePromotions3.length).toBe(0);

        // Test with wrong user and wrong item
        const applicablePromotions4 = await getApplicablePromotions(
          testSupplierId,
          testUserId2,
          wrongItems
        );
        expect(applicablePromotions4.length).toBe(0);
      });

      it("should create order with specific item/user promotion applied", async () => {
        const promotion = await createPromotion({
          name: "VIP Customer Special Item Deal",
          supplier_id: testSupplierId,
          promotion_type: "3", // BUY_X_GET_Y_FREE
          start_date: dayjs().subtract(1, "day").toISOString(),
          end_date: dayjs().add(1, "day").toISOString(),
          applies_to_all_items: false,
          applies_to_all_users: false,
          buy_quantity: 3,
          free_quantity: 1,
          active: true,
          items: [{ item_id: testItemId }],
          users: [{ user_id: testUserId }],
        });
        cleanupPromotionIds.push(promotion.id);

        // Create order with qualifying user and item
        const order = await createOrder({
          supplierId: testSupplierId,
          userId: testUserId,
          orderItems: [
            {
              id: testItemId,
              quantity: 4, // Qualifies for 1 free item
              price_purchased_at: 18.0,
            },
          ],
          deliveryDate: new Date(),
          order_name: "VIP Special Order",
          notes: "Testing specific user/item promotion",
          config: {},
          status: "In Transit",
        });
        cleanupOrderIds.push(order.id);

        // Should have discount for 1 free item = $18
        expect(order.discount).toBe(18.0);
        expect(order.config.appliedPromotions).toBeDefined();
        expect(order.config.appliedPromotions.length).toBe(1);
        expect(order.config.appliedPromotions[0].id).toBe(promotion.id);
      });
    });

    describe("Mixed Promotion Scenarios", () => {
      it("should handle mix of general and specific promotions", async () => {
        // Create a general promotion (applies to all)
        const generalPromotion = await createPromotion({
          name: "General 10% Off",
          supplier_id: testSupplierId,
          promotion_type: "5", // PERCENTAGE_OFF
          start_date: dayjs().subtract(1, "day").toISOString(),
          end_date: dayjs().add(1, "day").toISOString(),
          applies_to_all_items: true,
          applies_to_all_users: true,
          discount_percentage: 10,
          active: true,
        });
        cleanupPromotionIds.push(generalPromotion.id);

        // Create a specific item promotion
        const specificPromotion = await createPromotion({
          name: "Specific Item 20% Off",
          supplier_id: testSupplierId,
          promotion_type: "2", // BUY_X_GET_DISCOUNT
          start_date: dayjs().subtract(1, "day").toISOString(),
          end_date: dayjs().add(1, "day").toISOString(),
          applies_to_all_items: false,
          applies_to_all_users: true,
          buy_quantity: 2,
          discount_percentage: 20,
          active: true,
          items: [{ item_id: testItemId }],
        });
        cleanupPromotionIds.push(specificPromotion.id);

        const items = [
          { item_id: testItemId, quantity: 3, price: 15.0 }, // Qualifies for both
          { item_id: testItemId2, quantity: 2, price: 10.0 }, // Only qualifies for general
        ];

        const applicablePromotions = await getApplicablePromotions(
          testSupplierId,
          testUserId,
          items
        );

        // Should find both promotions
        expect(applicablePromotions.length).toBe(2);
        expect(applicablePromotions.some(p => p.id === generalPromotion.id)).toBe(true);
        expect(applicablePromotions.some(p => p.id === specificPromotion.id)).toBe(true);
      });

      it("should prioritize best promotion when multiple apply to same items", async () => {
        // Create two overlapping promotions for the same item
        const promotion1 = await createPromotion({
          name: "Item 15% Off",
          supplier_id: testSupplierId,
          promotion_type: "5", // PERCENTAGE_OFF
          start_date: dayjs().subtract(1, "day").toISOString(),
          end_date: dayjs().add(1, "day").toISOString(),
          applies_to_all_items: false,
          applies_to_all_users: true,
          discount_percentage: 15,
          active: true,
          items: [{ item_id: testItemId }],
        });
        cleanupPromotionIds.push(promotion1.id);

        const promotion2 = await createPromotion({
          name: "Item $25 Off",
          supplier_id: testSupplierId,
          promotion_type: "4", // FIXED_DISCOUNT
          start_date: dayjs().subtract(1, "day").toISOString(),
          end_date: dayjs().add(1, "day").toISOString(),
          applies_to_all_items: false,
          applies_to_all_users: true,
          discount_amount: 25.0,
          active: true,
          items: [{ item_id: testItemId }],
        });
        cleanupPromotionIds.push(promotion2.id);

        const items = [{ item_id: testItemId, quantity: 5, price: 20.0 }];
        const orderSubtotal = 100.0;

        const applicablePromotions = await getApplicablePromotions(
          testSupplierId,
          testUserId,
          items
        );

        // Should find both promotions
        expect(applicablePromotions.length).toBe(2);

        // Calculate discounts for both
        const discount1 = await calculatePromotionDiscount(
          testSupplierId,
          promotion1.id,
          items,
          orderSubtotal
        );
        expect(discount1.discountAmount).toBe(15.0); // 15% of $100

        const discount2 = await calculatePromotionDiscount(
          testSupplierId,
          promotion2.id,
          items,
          orderSubtotal
        );
        expect(discount2.discountAmount).toBe(25.0); // Fixed $25

        // Fixed discount is better in this case
        expect(discount2.discountAmount).toBeGreaterThan(discount1.discountAmount);
      });
    });
  });

  describe("Edge Cases and Error Handling", () => {
    it("should handle expired promotions correctly", async () => {
      // Create expired promotion
      const expiredPromotion = await createPromotion({
        name: "Expired Promotion",
        supplier_id: testSupplierId,
        promotion_type: "2", // BUY_X_GET_DISCOUNT
        start_date: dayjs().subtract(2, "days").toISOString(),
        end_date: dayjs().subtract(1, "day").toISOString(), // Expired yesterday
        applies_to_all_items: true,
        applies_to_all_users: true,
        buy_quantity: 1,
        discount_percentage: 50,
        active: true,
      });
      cleanupPromotionIds.push(expiredPromotion.id);

      const items = [{ item_id: testItemId, quantity: 5, price: 10.0 }];

      const applicablePromotions = await getApplicablePromotions(
        testSupplierId,
        testUserId,
        items
      );

      // Should not find the expired promotion
      const expiredFound = applicablePromotions.some(
        (p) => p.id === expiredPromotion.id
      );
      expect(expiredFound).toBe(false);
    });

    it("should handle inactive promotions correctly", async () => {
      // Create inactive promotion
      const inactivePromotion = await createPromotion({
        name: "Inactive Promotion",
        supplier_id: testSupplierId,
        promotion_type: "2", // BUY_X_GET_DISCOUNT
        start_date: dayjs().subtract(1, "day").toISOString(),
        end_date: dayjs().add(1, "day").toISOString(),
        applies_to_all_items: true,
        applies_to_all_users: true,
        buy_quantity: 1,
        discount_percentage: 50,
        active: false, // Inactive
      });
      cleanupPromotionIds.push(inactivePromotion.id);

      const items = [{ item_id: testItemId, quantity: 5, price: 10.0 }];

      const applicablePromotions = await getApplicablePromotions(
        testSupplierId,
        testUserId,
        items
      );

      // Should not find the inactive promotion
      const inactiveFound = applicablePromotions.some(
        (p) => p.id === inactivePromotion.id
      );
      expect(inactiveFound).toBe(false);
    });

    it("should handle zero discount amounts", async () => {
      const promotion = await createPromotion({
        name: "Zero Discount Promotion",
        supplier_id: testSupplierId,
        promotion_type: "2", // BUY_X_GET_DISCOUNT
        start_date: dayjs().subtract(1, "day").toISOString(),
        end_date: dayjs().add(1, "day").toISOString(),
        applies_to_all_items: true,
        applies_to_all_users: true,
        buy_quantity: 100, // Very high quantity requirement
        discount_percentage: 10,
        active: true,
      });
      cleanupPromotionIds.push(promotion.id);

      const items = [
        { item_id: testItemId, quantity: 1, price: 10.0 }, // Doesn't meet requirement
      ];

      const { discountAmount } = await calculatePromotionDiscount(
        testSupplierId,
        promotion.id,
        items,
        10.0
      );

      expect(discountAmount).toBe(0);
    });
  });
});
