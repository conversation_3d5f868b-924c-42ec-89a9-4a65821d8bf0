import { initializeApp, cert, ServiceAccount } from "firebase-admin/app";
import { getAuth, UserRecord } from "firebase-admin/auth";
import { getFirestore } from "firebase-admin/firestore";
import { ManagementClient } from "auth0";

// Firebase Admin initialization
const serviceAccount = {
  projectId: process.env.FIREBASE_PROJECT_ID,
  privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, "\n"),
  clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
} as ServiceAccount;

// Initialize Firebase Admin
const app = initializeApp({
  credential: cert(serviceAccount),
});

const auth = getAuth(app);
const db = getFirestore(app);

// Auth0 configuration
const auth0Config = {
  domain: process.env.AUTH0_DOMAIN,
  clientId: process.env.AUTH0_CLIENT_ID,
  clientSecret: process.env.AUTH0_CLIENT_SECRET,
  audience: `https://${process.env.AUTH0_DOMAIN}/api/v2/`,
};

const management = new ManagementClient({
  domain: auth0Config.domain,
  clientId: auth0Config.clientId,
  clientSecret: auth0Config.clientSecret,
});

// Types for function parameters and return values
interface FirebaseUserData {
  employeeId: string;
  supplierId: string;
}

interface CreateFirebaseAccountParams {
  email: string;
  password: string;
  employeeId: string;
  supplierId: string;
}

interface CreateAuth0AccountParams {
  email: string;
  password: string;
  name: string;
  employeeId: string;
  supplierId: string;
}

interface DeleteAccountByIdsParams {
  employeeId: string;
  supplierId: string;
}

interface Auth0UserCreateData {
  email: string;
  password: string;
  name: string;
  user_metadata: {
    employeeId: string;
    supplierId: string;
    dsd: boolean;
  };
  connection: string;
}

/**
 * Creates a Firebase account for app access
 * @param email - User email
 * @param password - User password
 */
export const createFirebaseAccount = async (
  params: CreateFirebaseAccountParams
): Promise<string> => {
  const { email, password, employeeId, supplierId } = params;

  try {
    // Create user with Firebase Admin SDK
    const userRecord: UserRecord = await auth.createUser({
      email,
      password,
      emailVerified: false,
      disabled: false,
    });

    console.log(`Firebase account created for employee: ${email}`);

    // Store user metadata in Firestore
    const userData: FirebaseUserData = {
      employeeId: employeeId.toString(),
      supplierId: supplierId.toString(),
    };

    await db.collection("users").doc(userRecord.uid).set(userData);
    return userRecord.uid;
  } catch (error) {
    console.error("Error creating Firebase account:", error);
    throw error;
  }
};

/**
 * Creates an Auth0 account for dashboard access
 * @param email - User email
 * @param password - User password
 * @param name - User name
 * @param employeeId - Database employee ID
 */
export const createAuth0Account = async (
  params: CreateAuth0AccountParams
): Promise<string> => {
  const { email, password, name, employeeId, supplierId } = params;

  try {
    const userData: Auth0UserCreateData = {
      email,
      password,
      name,
      user_metadata: {
        employeeId,
        supplierId,
        dsd: true,
      },
      connection: "Username-Password-Authentication",
    };

    const user = await management.users.create(userData);

    console.log(`Auth0 account created for employee: ${email}`);
    return user.data.user_id;
  } catch (error) {
    console.error("Error creating Auth0 account:", error);
    throw error;
  }
};

/**
 * Deletes a Firebase account by employee and supplier IDs
 * @param params - Delete parameters including employee ID and supplier ID
 * @returns Promise resolving to void when the account is deleted
 */
export const deleteFirebaseAccount = async (
  params: DeleteAccountByIdsParams
): Promise<void> => {
  const { employeeId, supplierId } = params;

  try {
    // Query Firestore to find the user with matching employeeId and supplierId
    const querySnapshot = await db
      .collection("users")
      .where("employeeId", "==", employeeId.toString())
      .where("supplierId", "==", supplierId.toString())
      .get();

    if (querySnapshot.empty) {
      throw new Error(
        `No Firebase user found for employeeId ${employeeId} and supplierId ${supplierId}`
      );
    }

    // Should only be one matching document
    const userDoc = querySnapshot.docs[0];
    const uid = userDoc.id;

    // Delete the user from Firebase Authentication
    await auth.deleteUser(uid);

    // Delete user data from Firestore
    await db.collection("users").doc(uid).delete();

    console.log(
      `Firebase account deleted for employeeId: ${employeeId}, supplierId: ${supplierId}`
    );
  } catch (error) {
    console.error("Error deleting Firebase account:", error);
    throw error;
  }
};

/**
 * Deletes an Auth0 account by employee and supplier IDs
 * @param params - Delete parameters including employee ID and supplier ID
 * @returns Promise resolving to void when the account is deleted
 */
export const deleteAuth0Account = async (
  params: DeleteAccountByIdsParams
): Promise<void> => {
  const { employeeId, supplierId } = params;

  try {
    // Query Auth0 to find users with matching metadata
    const users = await management.users.getAll({
      q: `user_metadata.employeeId:${employeeId} AND user_metadata.supplierId:${supplierId}`,
      search_engine: "v3",
    });

    if (!users.data || users.data.length === 0) {
      throw new Error(
        `No Auth0 user found for employeeId ${employeeId} and supplierId ${supplierId}`
      );
    }

    // Should only be one matching user
    const userId = users.data[0].user_id;

    // Delete the user from Auth0
    await management.users.delete({ id: userId });

    console.log(
      `Auth0 account deleted for employeeId: ${employeeId}, supplierId: ${supplierId}`
    );
  } catch (error) {
    console.error("Error deleting Auth0 account:", error);
    throw error;
  }
};
