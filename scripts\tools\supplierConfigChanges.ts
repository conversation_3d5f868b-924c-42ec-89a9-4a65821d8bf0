import {
  getSupplierConfig,
  updateSupplierConfig,
  upsertSupplierConfig,
} from "../../src/services/supplierService/supplierService";

const run = async () => {
  const supplierConfigs = await getSupplierConfig("68");
  console.log(supplierConfigs);
};

const update = async () => {
  const upsertedConfig = await upsertSupplierConfig(
    "31",
    "show_order_tabs",
    true
  );
  console.log("Upsert result:", upsertedConfig);
};

update();
