import axios from "axios";
import knex from "../../../knex/knex";
import {
  InvoiceItem,
  MutationReconcileInvoiceWithItemArgs,
} from "../../generated/graphql";

const reconcileInvoiceWithItem = async (
  _,
  args: MutationReconcileInvoiceWithItemArgs
) => {
  const { invoice_id, invoice_item_id, quantity, is_mispick, checked_in } =
    args.reconcileInvoiceWithItemInput;
  const invoiceItem: InvoiceItem[] = await knex("invoice_item")
    .where("id", invoice_item_id)
    .where("invoice_id", invoice_id)
    .update({
      checked_in_quantity: quantity,
      is_mispick: is_mispick,
      checked_in: checked_in,
    })
    .returning("*");
  return invoiceItem[0];
};

export default reconcileInvoiceWithItem;
