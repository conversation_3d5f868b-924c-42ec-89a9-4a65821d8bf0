type AllOrNone<T> = T | { [K in keyof T]?: never };
type BaseSupplierConfig = {
  name: string;
  orderNotificationSlackURL?: string;
  dsd?: boolean;
};
type ApprovalSupplierConfig = {
  needs_approval: true;
  phone_number: string;
};
export type SupplierConfig = BaseSupplierConfig &
  AllOrNone<ApprovalSupplierConfig>;

export const dsdSupplierConfigMap: { [key: string]: SupplierConfig } = {
  "30": {
    name: "A&I Beverage",
    orderNotificationSlackURL:
      "*********************************************************************************",
  },
  "31": {
    name: "C&G Snacks",
    dsd: true,
    orderNotificationSlackURL:
      "*********************************************************************************",
  },
  "47": {
    name: "Empire Snacks",
    // needs_approval: true,
    // phone_number: "6319401864",
    orderNotificationSlackURL:
      "*********************************************************************************",
  },
  "60": {
    name: "A&G Wholesale",
  },
  "68": {
    name: "Whitestone Foods",
    dsd: true,
    orderNotificationSlackURL:
      "*********************************************************************************",
  },
  "69": {
    name: "Planet Express",
    dsd: true,
  },
};
