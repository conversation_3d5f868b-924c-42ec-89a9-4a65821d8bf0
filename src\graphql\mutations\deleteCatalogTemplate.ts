import knex from "../../../knex/knex";
import { MutationDeleteCatalogTemplateArgs } from "../../generated/graphql";
import { deleteCatalogTemplate as deleteCatalogTemplateService } from "../../services/catalogTemplateService";

const deleteCatalogTemplate = async (
  _,
  args: MutationDeleteCatalogTemplateArgs
) => {
  const { id: catalogToDelete } = args;

  const trx = await knex.transaction();
  try {
    const result = await deleteCatalogTemplateService(trx, catalogToDelete);
    await trx.commit();
    return result;
  } catch (error) {
    await trx.rollback();
    throw error;
  }
};

export default deleteCatalogTemplate;
