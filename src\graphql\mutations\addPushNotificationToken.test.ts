import { mockKnex, queryBuilder } from "../../../knex/mock";
import AddPushNotificationTokenMutation from "./addPushNotificationToken";

mockKnex();

describe("Add Push Notification Token mutation", () => {
  let addPushNotificationToken: typeof AddPushNotificationTokenMutation;
  const consoleLogSpy = jest.spyOn(console, "log");
  beforeAll(async () => {
    ({ default: addPushNotificationToken } = await import(
      "./addPushNotificationToken"
    ));
  });
  const sampleToken = {
    user_id: "1234",
    token: "sample-token",
  };
  const addPushNotificationTokenInput = sampleToken;

  it("adds and returns a push notification token for a given user ID", async () => {
    queryBuilder.returning.mockResolvedValueOnce([sampleToken]);

    const tokenResult = await addPushNotificationToken(undefined, {
      addPushNotificationTokenInput,
    });

    expect(queryBuilder.insert).toHaveBeenCalledWith(sampleToken);
    expect(consoleLogSpy).toHaveBeenCalledWith(
      `Push Notification added with token '${sampleToken.token}' for user ${sampleToken.user_id}`
    );
    expect(tokenResult).toEqual(sampleToken);
  });
});
