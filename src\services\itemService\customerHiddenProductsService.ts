import knex from "../../../knex/knex";
import { Knex } from "knex";
import { getUsersInGroup } from "../userService";

export interface CustomerHiddenProduct {
  id: number;
  supplier_id: number;
  user_id: number;
  item_id: number;
  created_by?: number;
  created_at: Date;
}

export const updateCustomerHiddenProducts = async (
  txn: Knex.Transaction,
  supplierId: string,
  customerId: string,
  productIds: string[],
  createdBy?: number
): Promise<void> => {
  // 1. Find current state
  const currentRows = await txn("customer_hidden_product")
    .select("item_id")
    .where("supplier_id", supplierId)
    .where("user_id", customerId);

  const currentSet = new Set(currentRows.map((r) => r.item_id.toString()));
  const desiredSet = new Set(productIds);

  const toAdd = [...desiredSet].filter((id) => !currentSet.has(id));
  const toRemove = [...currentSet].filter((id) => !desiredSet.has(id));

  // 2a. Bulk insert new hidden products
  if (toAdd.length > 0) {
    const hiddenProductsToInsert = toAdd.map((productId) => ({
      supplier_id: parseInt(supplierId),
      user_id: parseInt(customerId),
      item_id: parseInt(productId),
      created_by: createdBy || null,
      created_at: new Date(),
    }));

    await txn.batchInsert("customer_hidden_product", hiddenProductsToInsert);
  }

  // 2b. Bulk delete removed hidden products
  if (toRemove.length > 0) {
    await txn("customer_hidden_product")
      .where("supplier_id", supplierId)
      .where("user_id", customerId)
      .whereIn(
        "item_id",
        toRemove.map((id) => parseInt(id))
      )
      .del();
  }
};

/**
 * Get all hidden products for a customer
 * @param supplierId - The supplier ID
 * @param customerId - The customer (user) ID, optional
 * @returns Array of hidden product IDs
 */
export const getCustomerHiddenProducts = async (
  supplierId: string,
  customerId?: string
): Promise<CustomerHiddenProduct[]> => {
  const query = knex("customer_hidden_product")
    .select("item_id")
    .where("supplier_id", supplierId);

  if (customerId) {
    query.where("user_id", customerId);
  }

  const hiddenProducts: CustomerHiddenProduct[] = await query;

  return hiddenProducts;
};

/**
 * Utility function to get hidden product IDs for a customer and supplier
 */
export const getHiddenProductIds = async (
  supplierId: string,
  customerId: string
): Promise<string[]> => {
  const hiddenProducts = await getCustomerHiddenProducts(supplierId, customerId);

  return hiddenProducts.map((product) => product.item_id.toString());
};

export const getGroupHiddenProducts = async (
  supplierId: string,
  groupname: string
): Promise<string[]> => {
  const user = await knex("attain_user")
    .where("store_group", groupname)
    .first();

  if (!user) {
    throw new Error(`Group does not exist: ${groupname}`);
  }

  const hiddenProducts = await getCustomerHiddenProducts(supplierId, user.id);
  return hiddenProducts.map((product) => product.item_id.toString());
};

/**
 * Update hidden products for all users in a group
 * @param txn - Database transaction
 * @param supplierId - The supplier ID
 * @param storeGroup - The store group name
 * @param productIds - Array of product IDs that should be hidden
 * @param createdBy - Optional: ID of the user/employee making the change
 * @returns Promise that resolves when all updates are complete
 */
export const updateGroupHiddenProducts = async (
  txn: Knex.Transaction,
  supplierId: string,
  storeGroup: string,
  productIds: string[],
  createdBy?: number
): Promise<void> => {
  const usersInGroup = await getUsersIdsInGroup(storeGroup);

  if (usersInGroup.length === 0) {
    return;
  }

  // 1. Get current hidden products for the first user to determine what needs to be added/removed
  const firstUserId = usersInGroup[0];
  const currentRows = await txn("customer_hidden_product")
    .select("item_id")
    .where("supplier_id", supplierId)
    .where("user_id", firstUserId);

  const currentSet = new Set(currentRows.map((r) => r.item_id.toString()));
  const desiredSet = new Set(productIds);

  // 2. Calculate what items need to be added and removed (same logic as single user)
  const itemsToAdd = [...desiredSet].filter((id) => !currentSet.has(id));
  const itemsToRemove = [...currentSet].filter((id) => !desiredSet.has(id));

  // 3. Apply the same add/remove operations to all users in the group

  // 3a. Bulk insert new hidden products for all users
  if (itemsToAdd.length > 0) {
    const hiddenProductsToInsert = usersInGroup.flatMap((userId) =>
      itemsToAdd.map((itemId) => ({
        supplier_id: parseInt(supplierId),
        user_id: parseInt(userId),
        item_id: parseInt(itemId),
        created_by: createdBy || null,
        created_at: new Date(),
      }))
    );

    await txn.batchInsert("customer_hidden_product", hiddenProductsToInsert);
  }

  // 3b. Bulk delete removed hidden products for all users
  if (itemsToRemove.length > 0) {
    await txn("customer_hidden_product")
      .where("supplier_id", supplierId)
      .whereIn(
        "user_id",
        usersInGroup.map((id) => parseInt(id))
      )
      .whereIn(
        "item_id",
        itemsToRemove.map((id) => parseInt(id))
      )
      .del();
  }
};


const getUsersIdsInGroup = async (groupname: string): Promise<string[]> => {
  const result = await getUsersInGroup(groupname);
  return result.map((user) => user.id);
};
