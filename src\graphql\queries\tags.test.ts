import Tags from "./tags";

describe("Tags query", () => {
  const categoryTests = [
    { category: "tobacco", expectedLength: 10 },
    { category: "snack", expectedLength: 2 },
    { category: "pastry", expectedLength: 1 },
    { category: "gum", expectedLength: 5 },
    { category: "beverage", expectedLength: 5 },
    { category: "health", expectedLength: 5 },
    { category: "cleaning", expectedLength: 16 },
    { category: "energy", expectedLength: 0 },
    { category: "alcohol", expectedLength: 0 },
  ];

  it.concurrent.each(categoryTests)(
    "correctly returns tags for category $category",
    async ({ category, expectedLength }) => {
      expect(
        (await Tags(undefined, { getTagsInput: { category } })).length
      ).toEqual(expectedLength);
    }
  );
});
