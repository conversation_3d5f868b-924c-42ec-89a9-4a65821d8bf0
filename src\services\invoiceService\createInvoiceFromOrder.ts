import dayjs from "dayjs";
import addInvoice from "./addInvoice";
import { getOrders } from "../orderService/orderService";
import { getQbIds } from "../integrationService/quickbooksService";
import { getItems } from "../itemService/itemService";
import knex from "../../../knex/knex";

export default async function createInvoiceFromOrder(
  supplierId: string,
  orderId: string
) {
  let order;

  try {
    const orderResult = await knex("order_detail")
      .select("order_number")
      .where("id", orderId)
      .first();

    if (orderResult) {
      const { orders } = await getOrders({
        filters: { ids: [orderResult.order_number.toString()] },
        supplierId,
      });

      if (orders.length > 0) {
        order = orders[0];
      }
    }
  } catch (error) {
    console.error("Error finding order by database ID:", error);
  }

  if (!order) {
    try {
      const { orders } = await getOrders({
        filters: { ids: [orderId] },
        supplierId,
      });

      if (orders.length > 0) {
        order = orders[0];
      }
    } catch (error) {
      console.error("Error finding order by order_number:", error);
    }
  }

  if (!order) {
    throw new Error("Order not found. Unable to create invoice.");
  }

  const qb_ids = await getQbIds(
    (order.orderItems || []).map((item) => item.item_id.toString())
  );
  const { items } = await getItems({
    supplierId,
    userId: order.customerDetails?.id,
    filters: {
      ids: (order.orderItems || []).map((item) => item.item_id.toString()),
    },
  });

  return await addInvoice({
    order_id: orderId.toString(),
    order_number: order.order_number,
    date_created: order?.delivery_date || null,
    supplier_id: supplierId,
    user_id: order.customerDetails?.id,
    discount: order.discount,
    subtotal: order.subtotal,
    notes: order.notes,
    config: order.config,
    total: order.subtotal,
    paid: 0,
    invoice_items: (order.orderItems || []).map((item) => {
      const invoiceItemMetadata = items.find(
        (itemData) => itemData.id === item.item_id
      )?.metadata;
      const invoiceItemName = invoiceItemMetadata
        ? `${item.name} (${invoiceItemMetadata})`
        : item.name;
      return {
        item_id: item.item_id.toString(),
        name: invoiceItemName,
        price: item.price_purchased_at ?? item.price,
        quantity: item.quantity,
        cog_price: item.cog_price,
        upc1: item.upc1,
        unit_size: item.unit_size,
        size: item.size,
        qb_id: qb_ids.get(item.item_id.toString()) ?? null,
        item_uom_id: item.item_uom_id,
      };
    }),
    return_items: false,
  });
}
