import knex from "../../../knex/knex";
import { MutationCreateAccountFromBalanceArgs } from "../../generated/graphql";

const createAccountFromBalance = async (
  _,
  args: MutationCreateAccountFromBalanceArgs
) => {
  const { payload } = args;

  const accountBody = JSON.parse(decodeURIComponent(payload));
  const buyerId = accountBody["buyerId"];
  const paymentMethodId = accountBody["paymentMethodId"];

  const user = await knex
    .select("*")
    .from("attain_user")
    .where("buyer_id", buyerId);
  const existing_accounts = await knex("account").where("user_id", user[0].id);

  await knex("account").insert({
    user_id: user[0].id,
    external_id: paymentMethodId,
    is_default: existing_accounts.length === 0 ? true : false,
    type: accountBody["paymentMethodType"] === "creditCard" ? "cc" : "bank",
  });

  return "success";
};

export default createAccountFromBalance;
