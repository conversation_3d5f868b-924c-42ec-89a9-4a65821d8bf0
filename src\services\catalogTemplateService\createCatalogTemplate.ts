import { K<PERSON> } from "knex";
import { CatalogTemplateRow, InsertableCatalogRow } from "./db.types";

export const createCatalogTemplate = async (
  trx: Knex.Transaction,
  input: InsertableCatalogRow
): Promise<CatalogTemplateRow> => {
  const [row] = await trx<CatalogTemplateRow>("catalog_template")
    .insert({ ...input, config: JSON.stringify(input.config) })
    .returning("*");

  return row;
};

export const updateCatalogTemplate = async (
  trx: Knex.Transaction,
  id: string,
  patch: Partial<InsertableCatalogRow>
): Promise<CatalogTemplateRow> => {
  let configToUpdate = patch.config;

  // If config is being updated, merge with existing config
  if (patch.config) {
    const [existingRow] = await trx<CatalogTemplateRow>("catalog_template")
      .where({ id })
      .select("config");

    if (existingRow) {
      const existingConfig =
        typeof existingRow.config === "string"
          ? JSON.parse(existingRow.config)
          : existingRow.config;

      configToUpdate = {
        ...existingConfig,
        ...(typeof patch.config === "object" ? patch.config : {}),
      };
    }
  }

  const payload = {
    ...patch,
    ...(configToUpdate && { config: JSON.stringify(configToUpdate) }),
    updated_at: trx.fn.now(),
  };

  const [row] = await trx<CatalogTemplateRow>("catalog_template")
    .where({ id })
    .update(payload)
    .returning("*");

  return row;
};
