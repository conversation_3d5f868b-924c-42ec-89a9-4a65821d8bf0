import { mockKnex, queryBuilder } from "../../../knex/mock";

mockKnex();

describe("getOrderItemsBySupplier service", () => {
  let getOrderItemsBySupplier: typeof import("./getOrderItemsBySupplier").default;

  beforeAll(async () => {
    // Import the service AFTER setting up mocks
    ({ default: getOrderItemsBySupplier } = await import(
      "./getOrderItemsBySupplier"
    ));
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  const orderId = "123";
  const supplier = "Test Supplier";

  it("should return all order items when order has single_supplier matching the supplier", async () => {
    const orderDetail = {
      id: orderId,
      single_supplier: supplier,
    };
    const orderItems = [
      { id: 1, item_id: "item1", quantity: 5, name: "Item 1" },
      { id: 2, item_id: "item2", quantity: 3, name: "Item 2" },
    ];

    queryBuilder.where.mockResolvedValueOnce([orderDetail]);
    queryBuilder.orderBy.mockResolvedValueOnce(orderItems);

    const result = await getOrderItemsBySupplier(orderId, supplier);

    expect(queryBuilder.select).toHaveBeenCalledWith("*");
    expect(queryBuilder.from).toHaveBeenCalledWith("order_detail");
    expect(queryBuilder.where).toHaveBeenCalledWith("id", orderId);
    expect(queryBuilder.from).toHaveBeenCalledWith("item");
    expect(queryBuilder.join).toHaveBeenCalledWith(
      "order_item",
      "item.id",
      "order_item.item_id"
    );
    expect(queryBuilder.where).toHaveBeenCalledWith("order_id", orderId);
    expect(queryBuilder.orderBy).toHaveBeenCalledWith("order_item.id", "desc");
    expect(result).toEqual({
      orderItems: orderItems,
    });
  });

  it("should filter items by supplier when order doesn't have single_supplier", async () => {
    const orderDetail = {
      id: orderId,
      single_supplier: null,
    };
    const allOrderItems = [
      {
        id: 1,
        item_id: "item1",
        supplier: "Other Supplier",
        new_supplier: null,
      },
      { id: 2, item_id: "item2", supplier: supplier, new_supplier: null },
      {
        id: 3,
        item_id: "item3",
        supplier: "Other Supplier",
        new_supplier: supplier,
      },
      {
        id: 4,
        item_id: "item4",
        supplier: supplier,
        new_supplier: "Different Supplier",
      },
    ];
    const itemsFromDb = [allOrderItems[1], allOrderItems[2], allOrderItems[3]];

    queryBuilder.where
      .mockImplementationOnce(() => Promise.resolve([orderDetail]))
      .mockImplementationOnce(function () {
        return this;
      })
      .mockImplementationOnce(() => Promise.resolve(itemsFromDb));

    const result = await getOrderItemsBySupplier(orderId, supplier);

    const expectedFinalItems = [allOrderItems[1], allOrderItems[2]];
    expect(result.orderItems).toEqual(expectedFinalItems);
    expect(result.orderItems).toHaveLength(2);
  });

  it("should handle orders with no items", async () => {
    const orderDetail = {
      id: orderId,
      single_supplier: supplier,
    };

    queryBuilder.where.mockResolvedValueOnce([orderDetail]);
    queryBuilder.orderBy.mockResolvedValueOnce([]);

    const result = await getOrderItemsBySupplier(orderId, supplier);

    expect(result).toEqual({
      orderItems: [],
    });
  });

  it("should handle mixed supplier scenario correctly", async () => {
    const orderDetail = {
      id: orderId,
      single_supplier: null,
    };
    const mixedItems = [
      { id: 1, supplier: supplier, new_supplier: null },
      { id: 2, supplier: "Other", new_supplier: supplier },
      { id: 3, supplier: supplier, new_supplier: "Different" },
      { id: 4, supplier: "Other", new_supplier: null },
    ];
    const itemsFromDb = [mixedItems[0], mixedItems[1], mixedItems[2]];

    queryBuilder.where
      .mockImplementationOnce(() => Promise.resolve([orderDetail]))
      .mockImplementationOnce(function () {
        return this;
      })
      .mockImplementationOnce(() => Promise.resolve(itemsFromDb));

    const result = await getOrderItemsBySupplier(orderId, supplier);

    const expectedFinalItems = [mixedItems[0], mixedItems[1]];
    expect(result.orderItems).toEqual(expectedFinalItems);
    expect(result.orderItems).toHaveLength(2);
  });

  afterAll(() => {
    jest.restoreAllMocks();
  });
});
