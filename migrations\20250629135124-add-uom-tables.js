"use strict";

var dbm;
var type;
var seed;

/**
 * We receive the dbmigrate dependency from dbmigrate initially.
 * This enables us to not have to rely on NODE_PATH.
 */
exports.setup = function (options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

var async = require("async");

exports.up = function (db, callback) {
  async.series(
    [
      // Create UOM table
      db.createTable.bind(db, "uom", {
        id: { type: "serial", primaryKey: true, autoIncrement: true },
        name: { type: "string", length: 255, notNull: true },
        supplier_id: {
          type: "int",
          notNull: true,
          foreignKey: {
            name: "fk_uom_to_supplier",
            table: "supplier",
            mapping: "id",
            rules: {
              onDelete: "CASCADE",
              onUpdate: "RESTRICT",
            },
          },
        },
        archived: { type: "boolean", defaultValue: false },
      }),

      // Create ItemUOM table
      db.createTable.bind(db, "item_uom", {
        id: { type: "serial", primaryKey: true, autoIncrement: true },
        uom_id: {
          type: "int",
          notNull: true,
          foreignKey: {
            name: "fk_item_uom_to_uom",
            table: "uom",
            mapping: "id",
            rules: {
              onDelete: "CASCADE",
              onUpdate: "RESTRICT",
            },
          },
        },
        quantity: { type: "int", notNull: true },
        item_id: {
          type: "int",
          notNull: true,
          foreignKey: {
            name: "fk_item_uom_to_item",
            table: "item",
            mapping: "id",
            rules: {
              onDelete: "CASCADE",
              onUpdate: "RESTRICT",
            },
          },
        },
        price: { type: "decimal", precision: 10, scale: 2 },
        upc: { type: "string", length: 255 },
        archived: { type: "boolean", defaultValue: false },
      }),

      // Create custom_item_uom_price table for UOM-level custom pricing
      db.createTable.bind(db, "custom_item_uom_price", {
        item_uom_id: {
          type: "int",
          notNull: true,
          foreignKey: {
            name: "fk_custom_item_uom_price_to_item_uom",
            table: "item_uom",
            mapping: "id",
            rules: {
              onDelete: "CASCADE",
              onUpdate: "RESTRICT",
            },
          },
        },
        user_id: {
          type: "int",
          notNull: true,
          foreignKey: {
            name: "fk_custom_item_uom_price_to_user",
            table: "attain_user",
            mapping: "id",
            rules: {
              onDelete: "CASCADE",
              onUpdate: "RESTRICT",
            },
          },
        },
        price: { type: "decimal", precision: 10, scale: 2, notNull: true },
      }),

      // Add composite primary key for custom_item_uom_price
      db.runSql.bind(
        db,
        "ALTER TABLE custom_item_uom_price ADD PRIMARY KEY (item_uom_id, user_id)"
      ),
    ],
    callback
  );
};

exports.down = function (db, callback) {
  async.series(
    [
      // Drop the new custom_item_uom_price table
      db.dropTable.bind(db, "custom_item_uom_price"),

      // Drop the UOM tables in reverse order (to handle foreign key constraints)
      db.dropTable.bind(db, "item_uom"),
      db.dropTable.bind(db, "uom"),
    ],
    callback
  );
};

exports._meta = {
  version: 1,
};
