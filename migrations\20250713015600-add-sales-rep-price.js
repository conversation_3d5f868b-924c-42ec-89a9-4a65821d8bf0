"use strict";

var dbm;
var type;
var seed;

/**
 * We receive the dbmigrate dependency from dbmigrate initially.
 * This enables us to not have to rely on NODE_PATH.
 */
exports.setup = function (options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

exports.up = function (db) {
  return db
    .addColumn("item", "min_sale_price", {
      type: "numeric",
    })
    .then(() => {
      return db.addColumn("order_item", "notes", {
        type: "text",
      });
    })
    .then(() => {
      return db.addColumn("cart_item", "notes", {
        type: "text",
      });
    })
    .then(() => {
      return db.addColumn("cart_item", "custom_price", {
        type: "numeric",
      });
    });
};

exports.down = function (db) {
  return db
    .removeColumn("cart", "updated_at")
    .then(() => {
      return db.removeColumn("cart_item", "custom_price");
    })
    .then(() => {
      return db.removeColumn("cart_item", "notes");
    })
    .then(() => {
      return db.removeColumn("order_item", "notes");
    });
};

exports._meta = {
  version: 1,
};
