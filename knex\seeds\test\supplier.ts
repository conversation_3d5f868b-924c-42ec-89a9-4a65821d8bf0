import { Knex } from "knex";

export async function seed(knex: Knex): Promise<void> {
  // Deletes ALL existing entries
  await knex("supplier").del();

  // Inserts seed entries
  await knex("supplier").insert([
    {
      name: "Supplier1",
      logo: "https://attain-app-resources-bucket.s3.amazonaws.com/logos/quokka+logo+(2).png",
      spotlight_image:
        "https://attain-app-resources-bucket.s3.amazonaws.com/promo/Quokka+Brew+(81).png",
      need_signup: true,
      // minimum: 100,
      // address: "test1_address",
      // phone_number: "1234567890",
    },
    {
      name: "Supplier2",
      logo: "https://attain-app-resources-bucket.s3.amazonaws.com/logos/Group+929+(9).png",
      spotlight_image:
        "https://attain-app-resources-bucket.s3.amazonaws.com/promo/Liquid+Death+(39).png",
      need_signup: true,
      // minimum: 500,
      // address: "test2_address",
      // phone_number: "0987654321",
    },
  ]);
}
