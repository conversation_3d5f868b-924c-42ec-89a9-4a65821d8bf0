import axios from "axios";

export const upcMatch = (upc1, upc2) => {
  if (upc1.length === 8) {
    const modifiedUpc = upcEToUpcA(upc1);
    return upc2.includes(modifiedUpc.substring(1, modifiedUpc.length - 1));
  } else {
    return upc2.includes(upc1.substring(1, upc1.length - 1));
  }
};
export const modifyUpc = (upc) => {
  if (!upc || upc.length < 3) {
    return "---------";
  }
  if (upc.length === 8) {
    const modifiedUpc = upcEToUpcA(upc);
    return "%" + modifiedUpc.substring(1, modifiedUpc.length - 1) + "%";
  } else {
    return "%" + upc.substring(1, upc.length - 1) + "%";
  }
};

export const filterByUpc = (whereBuilder, upc) =>
  whereBuilder
    .whereRaw("upc1 similar to ?", [upc])
    .orWhereRaw("upc2 similar to ?", [upc])
    .orWhereRaw("upc3 similar to ?", [upc])
    .orWhereRaw("upc4 similar to ?", [upc]);

export const filterByUpcForInvoiceItems = (whereBuilder, upc) =>
  whereBuilder
    .whereRaw("upc1 similar to ?", [upc])
    .orWhereRaw("upc2 similar to ?", [upc]);

const SLACK_URL =
  "*********************************************************************************";

export const logUpc = async (
  user,
  userId,
  supplier,
  upc,
  modifiedUpc,
  result
) => {
  try {
    await axios.post(
      SLACK_URL,
      {
        text: `${user} (ID: ${userId}) just scanned ${upc} for supplier ${supplier}, modified to ${modifiedUpc}, sucess: ${
          result.length > 0
        }`,
      },
      {
        headers: {
          accept: "application/json",
          "content-type": "application/json",
        },
      }
    );
  } catch (error) {
    console.log("Error sending Slack message", error);
  }
};

export const modifyUpcForSuggestedMatches = (upc) => {
  if (!upc || upc.length < 3) {
    return "---------";
  }

  const modifiedUpc = upc.length === 8 ? upcEToUpcA(upc) : upc;
  return "%" + modifiedUpc.substring(2, modifiedUpc.length - 2) + "%";
};

function upcEToUpcA(E) {
  if (E[0] !== "0" && E[0] !== "1") {
    return "FAIL: Invalid number system.";
  }

  if (E[6] <= "2") {
    return E.slice(0, 3) + E[6] + "0000" + E.slice(3, 6) + E[7];
  } else if (E[6] === "3") {
    return E.slice(0, 4) + "00000" + E.slice(4, 6) + E[7];
  } else if (E[6] === "4") {
    return E.slice(0, 5) + "00000" + E[5] + E[7];
  } else if (E[6] >= "5") {
    return E.slice(0, 6) + "0000" + E[6] + E[7];
  } else {
    return "FAIL: Invalid number system.";
  }
}
