import { CreditStatus, Item } from "../../generated/graphql";

export interface CreditRow {
  id: number;
  total: number;
  cash_amount: number;
  invoice_id: number | null;
  order_id: number | null;
  order_number: number | null;
  credit_number: number;
  user_id: number;
  supplier_id: number;
  created_at: Date;
  updated_at: Date;
  status: CreditStatus;
  archived: boolean;
}

export interface CreditItemRow {
  id: number;
  credit_id: number;
  item_id: number;
  quantity: number;
  reason: string;
  note: string | null;
  unit_price: number;
  total_price: number;
  invoice_item_id: number | null;
  item_snapshot: Item; // This is a JSON snapshot of the item.
}

export interface CreditImageRow {
  id: number;
  credit_id: number;
  url: string;
  created_at: Date;
}

// Types for inserting new records
export type InsertableCreditRow = Omit<
  CreditRow,
  "id" | "created_at" | "updated_at"
>;

export type InsertableCreditItemRow = Omit<CreditItemRow, "id" | "credit_id">;

// Types for updating existing records
export type UpdatableCreditRow = Partial<
  Omit<CreditRow, "id" | "created_at" | "updated_at">
> & {
  updated_at: Date;
};

export type UpdatableCreditItemRow = Partial<
  Omit<CreditItemRow, "id" | "credit_id">
>;
