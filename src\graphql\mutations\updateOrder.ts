import { MutationUpdateOrderArgs } from "../../generated/graphql";
import { updateOrder as updateOrderFn } from "../../services/orderService/updateOrder";

const updateOrder = async (_, args: MutationUpdateOrderArgs) => {
  const {
    orderId,
    supplierId,
    userId,
    orderItems,
    invoice,
    deliveryDate,
    ...order
  } = args.updateOrderInput;

  return await updateOrderFn({
    supplierId,
    userId,
    orderId,
    order: {
      ...order,
      ...(deliveryDate && { delivery_date: deliveryDate }),
    },
    orderItems,
    invoice,
  });
};

export default updateOrder;
