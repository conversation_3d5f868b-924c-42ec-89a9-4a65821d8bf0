import dayjs from "dayjs";
import knex from "../../knex/knex";
import { getInvoiceByOrderId } from "../../src/services/invoiceService/getInvoice";
import { updateOrder } from "../../src/services/orderService/updateOrder";
import { Invoice, UpdateOrderItemInput } from "../../src/generated/graphql";

/**
 * Updates an order based on the invoice data
 * This is the opposite of updateInvoiceFromOrder - it transfers data from invoice to order
 *
 * @param supplierId - The supplier ID
 * @param invoiceId - The invoice ID
 */
async function updateOrderFromSingleInvoice(
  supplierId: string,
  invoiceId: string
) {
  // Get the invoice details
  const invoice = await knex("invoice")
    .select("*")
    .where("id", invoiceId)
    .first();

  if (!invoice) {
    console.error(`Invoice with ID ${invoiceId} not found`);
    throw new Error(`Invoice with ID ${invoiceId} not found`);
  }

  if (!invoice.order_id) {
    console.error(`Invoice ${invoiceId} does not have an associated order ID`);
    throw new Error(
      `Invoice ${invoiceId} does not have an associated order ID`
    );
  }

  const orderId = invoice.order_id.toString();

  const oldOrder = await knex("order_detail")
    .select("*")
    .where("id", orderId)
    .first();

  // Get invoice items
  const invoiceItems = await knex("invoice_item")
    .select("*")
    .where("invoice_id", invoiceId);

  if (!invoiceItems || invoiceItems.length === 0) {
    console.error(`No items found for invoice ${invoiceId}`);
    throw new Error(`No items found for invoice ${invoiceId}`);
  }

  // Map invoice items to order items format
  const orderItems: UpdateOrderItemInput[] = invoiceItems.map((item) => ({
    id: item.item_id,
    quantity: item.quantity,
    price_purchased_at: item.price,
  }));

  // Calculate subtotal from invoice items
  const subtotal = invoiceItems
    .map((item) => item.price * item.quantity)
    .reduce((acc, itemTotal) => acc + itemTotal, 0);

  try {
    // Update the order using the invoice data
    const result = await updateOrder({
      supplierId,
      userId: invoice.user_id,
      orderId,
      order: {
        subtotal,
        notes: oldOrder.notes,
        config: oldOrder.config,
        status: oldOrder.status,
      },
      orderItems,
      invoice: {}, // Empty invoice object since we're going from invoice to order, not the other way around
    });

    return result;
  } catch (error) {
    console.error(`Error updating order from invoice ${invoiceId}:`, error);
    throw new Error(
      `Error updating order from invoice ${invoiceId}: ${error.message}`
    );
  }
}

/**
 * Updates orders based on invoice data for multiple invoices
 *
 * @param supplierId - The supplier ID
 * @param invoiceIds - Array of invoice IDs to process
 */
async function updateOrderFromInvoice(
  supplierId: string,
  invoiceIds: string[]
) {
  console.log(`Starting batch update for ${invoiceIds.length} invoices`);

  const results = [];
  const errors = [];

  for (const invoiceId of invoiceIds) {
    try {
      const result = await updateOrderFromSingleInvoice(supplierId, invoiceId);
      results.push({ invoiceId, success: true, result });
    } catch (error) {
      console.error(`Failed to update order from invoice ${invoiceId}:`, error);
      errors.push({ invoiceId, error: error.message });
      results.push({ invoiceId, success: false, error: error.message });
    }
  }

  console.log(
    `Batch update completed. Successful: ${
      results.filter((r) => r.success).length
    }, Failed: ${errors.length}`
  );

  if (errors.length > 0) {
    console.log(`Errors encountered: ${JSON.stringify(errors, null, 2)}`);
  }

  return {
    totalProcessed: invoiceIds.length,
    successful: results.filter((r) => r.success).length,
    failed: errors.length,
    results,
  };
}

// Allow script to be run from command line
if (require.main === module) {
  console.log("Starting updateOrderFromInvoice script in standalone mode");
  const supplierId = process.argv[2];
  const invoiceIdsArg = process.argv.slice(3);

  if (!supplierId || invoiceIdsArg.length === 0) {
    console.error(
      "Usage: node updateOrderFromInvoice.js <supplierId> <invoiceId1> [invoiceId2 invoiceId3 ...]"
    );
    process.exit(1);
  }

  console.log(
    `Running with parameters - supplierId: ${supplierId}, invoiceIds: ${invoiceIdsArg.join(
      ", "
    )}`
  );

  updateOrderFromInvoice(supplierId, invoiceIdsArg)
    .then((result) => {
      console.log(
        `Order updates completed. Summary: ${JSON.stringify(
          {
            total: result.totalProcessed,
            successful: result.successful,
            failed: result.failed,
          },
          null,
          2
        )}`
      );
      process.exit(0);
    })
    .catch((error) => {
      console.error("Error:", error.message);
      process.exit(1);
    });
}
