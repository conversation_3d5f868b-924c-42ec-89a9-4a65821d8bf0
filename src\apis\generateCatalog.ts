import { ApolloServer } from "@apollo/server";
import Expo from "expo-server-sdk";
import { Request, Response } from "express";
import RestEndpoint from "./_restEndpoint";
import { getItems } from "../services/itemService/itemService";
import { generateCatalogPDF, CatalogData } from "../lib/catalogPDF";
import { CatalogTemplateConfig } from "../services/catalogTemplateService/db.types";

export default class GenerateCatalog extends RestEndpoint {
  constructor(apolloServerInit: ApolloServer, expoClient: Expo) {
    super(apolloServerInit, expoClient);
  }

  public async handler(req: Request, res: Response) {
    const { supplierId, config, userId } = req.body;

    try {
      if (!supplierId) {
        throw {
          code: 400,
          message: "supplierId is required",
        };
      }

      const pdfBuffer = await this.worker(supplierId, config, userId);

      // Set headers for PDF download
      res.setHeader("Content-Type", "application/pdf");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="catalog-${config.title || "default"}.pdf"`
      );
      res.setHeader("Content-Length", pdfBuffer.length);

      // Send the PDF buffer directly
      res.send(pdfBuffer);
    } catch (err) {
      const errorMessage = `Failed to generate catalog PDF: ${
        err.message || err
      }`;
      console.error(errorMessage);
      res.status(err.code || 500).send(errorMessage);
    }
  }

  protected async worker(
    supplierId: string,
    config: CatalogTemplateConfig,
    userId?: string
  ): Promise<Buffer> {
    // Get items based on config
    const { items } = await getItems({
      supplierId,
      userId: userId || "",
      filters: {
        archived: false,
        ...(config.category && { category: config.category }),
      },
      pagination: { offset: 0, limit: 1000 },
      sortBy: {
        field: "name",
        ordering: "asc",
      },
    });

    // Sort items by nacs_category if sort_by is CATEGORY
    let sortedItems = items;
    if (config.sort_by === "CATEGORY") {
      sortedItems = [...items].sort((a, b) => {
        const categoryA = a.nacs_category || "";
        const categoryB = b.nacs_category || "";
        return categoryA.localeCompare(categoryB);
      });
    }

    const catalogData: CatalogData = {
      items: sortedItems,
      config: {
        category: config.category,
        sort_by: config.sort_by as any, // Map string to enum
        title: config.title,
        subtitle: config.subtitle || "",
        company_info: config.company_info,
        pricing_enabled: config.pricing_enabled,
        display_options: {
          show_sku: config.display_options.show_sku,
          show_case_size: config.display_options.show_case_size,
          show_upc: config.display_options.show_upc,
          show_stock: config.display_options.show_stock,
        },
      },
    };

    const pdfBuffer = await generateCatalogPDF(catalogData);

    return pdfBuffer;
  }
}
