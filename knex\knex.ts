import dotenv from "dotenv";
import Knex from "knex";
import knexConfig from "../knexfile";
// import { types } from "pg";

dotenv.config();

const environment = process.env.NODE_ENV || "development";
const knex = Knex(knexConfig[environment]);

// Set typecast from PostgreSQL NodeJS Driver for 'NUMERIC' datatype
// types.setTypeParser(types.builtins.NUMERIC, (value: string) => {
//   return parseFloat(value);
// });

export default knex;
