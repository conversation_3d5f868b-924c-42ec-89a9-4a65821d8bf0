import { <PERSON><PERSON> } from "knex";
import knex from "../../../knex/knex";
import {
  Goal,
  CreateGoalInput,
  UpdateGoalInput,
  GoalsInput,
  Ordering,
  GoalStatus,
} from "../../generated/graphql";
import dayjs from "dayjs";

interface GoalPeriodSelections {
  [goalId: string]: string;
}

/**
 * Creates a new goal and its associated assignments within a database transaction.
 * @param input - The data for the new goal, including name, type, period, target, dates, and assignments.
 * @returns A promise that resolves to the newly created goal object, including its assignments.
 */
export const createGoal = async (input: CreateGoalInput): Promise<Goal> => {
  return knex.transaction(async (trx) => {
    const [goal] = await trx("goals")
      .insert({
        supplier_id: parseInt(input.supplier_id),
        name: input.name,
        type: input.type.toLowerCase(),
        period: input.period.toLowerCase(),
        target_amount: input.target_amount,
        start_date: input.start_date,
        end_date: input.end_date,
        is_active: true,
        created_at: new Date(),
        updated_at: new Date(),
      })
      .returning("*");

    const assignments = input.assignments.map((assignment) => ({
      goal_id: goal.id,
      employee_id: assignment.employee_id
        ? parseInt(assignment.employee_id)
        : null,
      target_amount: assignment.target_amount,
      created_at: new Date(),
      updated_at: new Date(),
    }));

    await trx("goal_assignments").insert(assignments);

    return getGoalById(goal.id.toString(), trx);
  });
};

/**
 * Updates an existing goal and its assignments in a database transaction.
 * If new assignments are provided, existing ones are replaced.
 * @param input - The data to update for the goal. Requires the goal ID and any fields to be changed.
 * @returns A promise that resolves to the updated goal object.
 */
export const updateGoal = async (input: UpdateGoalInput): Promise<Goal> => {
  return knex.transaction(async (trx) => {
    const updateData: any = {
      updated_at: new Date(),
    };

    if (input.name !== undefined) updateData.name = input.name;
    if (input.type !== undefined) updateData.type = input.type.toLowerCase();
    if (input.period !== undefined)
      updateData.period = input.period.toLowerCase();
    if (input.target_amount !== undefined)
      updateData.target_amount = input.target_amount;
    if (input.start_date !== undefined)
      updateData.start_date = input.start_date;
    if (input.end_date !== undefined) updateData.end_date = input.end_date;
    if (input.is_active !== undefined) updateData.is_active = input.is_active;

    await trx("goals")
      .where({ id: parseInt(input.id) })
      .update(updateData);

    if (input.assignments) {
      await trx("goal_assignments")
        .where({ goal_id: parseInt(input.id) })
        .delete();

      const assignments = input.assignments.map((assignment) => ({
        goal_id: parseInt(input.id),
        employee_id: assignment.employee_id
          ? parseInt(assignment.employee_id)
          : null,
        target_amount: assignment.target_amount,
        created_at: new Date(),
        updated_at: new Date(),
      }));

      await trx("goal_assignments").insert(assignments);
    }

    return getGoalById(input.id, trx);
  });
};

/**
 * Deletes a goal and all of its associated assignments from the database within a transaction.
 * @param id - The ID of the goal to delete.
 * @returns A promise that resolves to true if the goal was successfully deleted, false otherwise.
 */
export const deleteGoal = async (id: string): Promise<boolean> => {
  return knex.transaction(async (trx) => {
    await trx("goal_assignments")
      .where({ goal_id: parseInt(id) })
      .delete();

    // Delete the goal
    const deletedCount = await trx("goals")
      .where({ id: parseInt(id) })
      .delete();

    return deletedCount > 0;
  });
};

/**
 * Retrieves a list of goals based on specified filters, pagination, and sorting criteria.
 * Calculates progress for each goal based on the provided period selections.
 * @param input - An object containing filters, pagination, sorting, and period selections.
 * @returns A promise that resolves to an object containing the list of goals and the total count of goals matching the filters.
 */
export const getGoals = async (
  input: GoalsInput
): Promise<{ goals: Goal[]; totalCount: number }> => {
  const {
    filters,
    pagination = { offset: 0, limit: 50 },
    sortBy = { field: "created_at", ordering: Ordering.Desc },
    goal_period_selections = [],
  } = input;

  const goalPeriodSelections: GoalPeriodSelections = {};
  if (goal_period_selections) {
    goal_period_selections.forEach((selection) => {
      goalPeriodSelections[selection.goal_id] = selection.period_start;
    });
  }

  let baseQuery = knex("goals").where(
    "supplier_id",
    parseInt(filters.supplier_id)
  );

  if (filters.is_active !== undefined) {
    baseQuery = baseQuery.where("is_active", filters.is_active);
  }
  if (filters.type) {
    baseQuery = baseQuery.where("type", filters.type.toLowerCase());
  }
  if (filters.period) {
    baseQuery = baseQuery.where("period", filters.period.toLowerCase());
  }

  const countQuery = baseQuery.clone().count("* as count").first();

  // Apply sorting and pagination
  const sortField = [
    "id",
    "name",
    "type",
    "period",
    "target_amount",
    "start_date",
    "created_at",
  ].includes(sortBy.field)
    ? sortBy.field
    : "created_at";

  const goals = await baseQuery
    .orderBy(sortField, sortBy.ordering.toLowerCase())
    .offset(pagination.offset || 0)
    .limit(pagination.limit || 50);

  const goalsWithProgress = await Promise.all(
    goals.map(async (goal) => {
      const selectedPeriodStart = goalPeriodSelections[goal.id.toString()];
      return getGoalWithProgress(
        goal,
        filters.employee_id,
        selectedPeriodStart
      );
    })
  );

  let filteredGoals = goalsWithProgress;
  if (filters.status) {
    filteredGoals = goalsWithProgress.filter((goal) => {
      const status = getGoalStatus(goal, goal.assignments);
      return status.status === filters.status;
    });
  }

  // Recalculate total count for filtered results
  const totalCount = filters.status
    ? filteredGoals.length
    : parseInt(((await countQuery)?.count as string) || "0", 10);

  return {
    goals: filteredGoals,
    totalCount,
  };
};

/**
 * Retrieves a single goal by its ID, along with its associated assignments.
 * @param id - The ID of the goal to retrieve.
 * @param [trx] - An optional Knex transaction object. If not provided, a new connection is used.
 * @returns A promise that resolves to the goal object.
 * @throws Throws an error if no goal is found with the specified ID.
 */
export const getGoalById = async (
  id: string,
  trx?: Knex.Transaction
): Promise<Goal> => {
  const db = trx || knex;

  const goal = await db("goals")
    .where({ id: parseInt(id) })
    .first();

  if (!goal) {
    throw new Error(`Goal with id ${id} not found`);
  }

  const assignments = await db("goal_assignments")
    .leftJoin("employees", "goal_assignments.employee_id", "employees.id")
    .where("goal_assignments.goal_id", parseInt(id))
    .select(
      "goal_assignments.*",
      "employees.name as employee_name",
      "employees.email as employee_email"
    );

  return {
    ...goal,
    id: goal.id.toString(),
    supplier_id: goal.supplier_id.toString(),
    type: goal.type.toUpperCase(),
    period: goal.period.toUpperCase(),
    assignments: assignments.map((assignment) => ({
      ...assignment,
      id: assignment.id.toString(),
      goal_id: assignment.goal_id.toString(),
      employee_id: assignment.employee_id
        ? assignment.employee_id.toString()
        : null,
      employee: assignment.employee_id
        ? {
            id: assignment.employee_id.toString(),
            name: assignment.employee_name,
            email: assignment.employee_email,
          }
        : null,
      current_progress: 0,
      percentage_complete: 0,
    })),
  };
};

/**
 * Retrieves a goal and calculates the current progress for its assignments.
 * @param goal - The raw goal object from the database.
 * @param [employeeId] - An optional employee ID to filter assignments and calculate progress for a specific employee.
 * @param [selectedPeriodStart] - An optional start date of a specific period to calculate progress for (time-travel).
 * @returns A promise that resolves to the goal object with progress calculated for its assignments.
 */
export const getGoalWithProgress = async (
  goal: any,
  employeeId?: string,
  selectedPeriodStart?: string
): Promise<Goal> => {
  let assignmentsQuery = knex("goal_assignments")
    .leftJoin("employees", "goal_assignments.employee_id", "employees.id")
    .where("goal_assignments.goal_id", goal.id)
    .select(
      "goal_assignments.*",
      "employees.name as employee_name",
      "employees.email as employee_email"
    );

  if (employeeId) {
    assignmentsQuery = assignmentsQuery.where(
      "goal_assignments.employee_id",
      parseInt(employeeId)
    );
  }

  const assignments = await assignmentsQuery;

  const assignmentsWithProgress = await Promise.all(
    assignments.map(async (assignment) => {
      const progress = await calculateProgress(
        goal,
        assignment,
        selectedPeriodStart
      );
      return {
        ...assignment,
        id: assignment.id.toString(),
        goal_id: assignment.goal_id.toString(),
        employee_id: assignment.employee_id
          ? assignment.employee_id.toString()
          : null,
        employee: assignment.employee_id
          ? {
              id: assignment.employee_id.toString(),
              name: assignment.employee_name,
              email: assignment.employee_email,
            }
          : null,
        current_progress: progress,
        percentage_complete:
          assignment.target_amount > 0
            ? (progress / assignment.target_amount) * 100
            : 0,
      };
    })
  );

  return {
    ...goal,
    id: goal.id.toString(),
    supplier_id: goal.supplier_id.toString(),
    type: goal.type.toUpperCase(),
    period: goal.period.toUpperCase(),
    assignments: assignmentsWithProgress,
  };
};

/**
 * Calculates the progress of a single goal assignment for a specific period.
 * Progress is determined by the goal's type (e.g., 'sales_amount' or 'stores_sold_to').
 * @param goal - The goal object.
 * @param assignment - The assignment object.
 * @param [selectedPeriodStart] - Optional start date for a specific period (time-travel). If not provided, the current period is used.
 * @returns A promise that resolves to the calculated progress value.
 */
export const calculateProgress = async (
  goal: any,
  assignment: any,
  selectedPeriodStart?: string
): Promise<number> => {
  const { start_date, end_date, period, type, supplier_id } = goal;
  const { employee_id } = assignment;

  const now = dayjs();
  const goalStartDate = dayjs(start_date);

  let progressStartDate: dayjs.Dayjs;
  let progressEndDate: dayjs.Dayjs;

  if (selectedPeriodStart) {
    // Time travel feature - calculate for specific period
    progressStartDate = dayjs(selectedPeriodStart);
    progressEndDate = calculatePeriodEnd(progressStartDate, period);
  } else if (end_date) {
    // Non-recurring goal with end date - calculate for entire duration
    progressStartDate = goalStartDate;
    progressEndDate = dayjs(end_date);
  } else {
    // Recurring goal - calculate for current period based on start_date
    progressStartDate = calculatePeriodStart(goalStartDate, period, now);
    progressEndDate = calculatePeriodEnd(progressStartDate, period);
  }

  // Calculate progress based on goal type
  let progress = 0;

  try {
    if (type.toLowerCase() === "sales_amount") {
      // Calculate total sales amount using date_created
      let salesQuery = knex("invoice")
        .where("invoice.supplier_id", supplier_id)
        .where("invoice.date_created", ">=", progressStartDate.toDate())
        .where("invoice.date_created", "<=", progressEndDate.toDate())
        .whereNot("invoice.archived", true)
        .sum("invoice.total as total_sales");

      if (employee_id) {
        const employeeRoutes = await knex("route_assignment")
          .select("route_id")
          .where("employee_id", employee_id);

        const routeIds = employeeRoutes.map((r) => r.route_id.toString());

        if (routeIds.length > 0) {
          salesQuery = salesQuery.where(function () {
            this.whereIn("invoice.route_id", routeIds).orWhereRaw(
              "invoice.config->>'custom_route' = ANY(?::text[])",
              [routeIds]
            );
          });
        } else {
          return 0;
        }
      }

      const result = await salesQuery.first();
      progress = parseFloat(result?.total_sales || "0");

    } else if (type.toLowerCase() === "stores_sold_to") {
      // Calculate unique stores sold to using date_created
      let storesQuery = knex("invoice")
        .where("invoice.supplier_id", supplier_id)
        .where("invoice.date_created", ">=", progressStartDate.toDate())
        .where("invoice.date_created", "<=", progressEndDate.toDate())
        .whereNot("invoice.archived", true)
        .countDistinct("invoice.user_id as unique_stores");

      if (employee_id) {
        const employeeRoutes = await knex("route_assignment")
          .select("route_id")
          .where("employee_id", employee_id);

        const routeIds = employeeRoutes.map((r) => r.route_id.toString());

        if (routeIds.length > 0) {
          storesQuery = storesQuery.where(function () {
            this.whereIn("invoice.route_id", routeIds).orWhereRaw(
              "invoice.config->>'custom_route' = ANY(?::text[])",
              [routeIds]
            );
          });
        } else {
          return 0;
        }
      }

      const result = await storesQuery.first();
      progress = parseInt((result?.unique_stores as string) || "0", 10);
    }
  } catch (error) {
    console.error(`Error calculating progress for goal ${goal.id}:`, error);
    progress = 0;
  }

  return progress;
};

/**
 * Calculates the start date of the current period for a recurring goal.
 * @param goalStartDate - The start date of the goal itself.
 * @param period - The recurrence period ('daily', 'weekly', 'monthly').
 * @param currentDate - The current date, used to determine the current period.
 * @returns The calculated start date of the current period.
 */
export const calculatePeriodStart = (
  goalStartDate: dayjs.Dayjs,
  period: string,
  currentDate: dayjs.Dayjs
): dayjs.Dayjs => {
  const periodLower = period.toLowerCase();

  switch (periodLower) {
    case "daily":
      return currentDate.startOf("day");

    case "weekly": {
      const daysSinceStart = currentDate.diff(goalStartDate, "day");
      const weeksSinceStart = Math.floor(daysSinceStart / 7);
      return goalStartDate.add(weeksSinceStart * 7, "day");
    }

    case "monthly": {
      const startDay = goalStartDate.date();
      let currentPeriodStart = currentDate.date(startDay);

      // If we're before the start day of this month, use last month
      if (currentDate.date() < startDay) {
        currentPeriodStart = currentPeriodStart.subtract(1, "month");
      }

      return currentPeriodStart.startOf("day");
    }

    default:
      return goalStartDate;
  }
};

/**
 * Calculates the end date of a period given its start date and duration.
 * @param periodStart - The start date of the period.
 * @param period - The period duration ('daily', 'weekly', 'monthly').
 * @returns The calculated end date of the period.
 */
export const calculatePeriodEnd = (
  periodStart: dayjs.Dayjs,
  period: string
): dayjs.Dayjs => {
  const periodLower = period.toLowerCase();

  switch (periodLower) {
    case "daily":
      return periodStart.endOf("day");

    case "weekly":
      return periodStart.add(6, "day").endOf("day");

    case "monthly":
      return periodStart.add(1, "month").subtract(1, "day").endOf("day");

    default:
      return periodStart.endOf("day");
  }
};

/**
 * Determines the status of a goal based on its completion, end date, and activity status.
 * @param goal - The goal object, including `end_date` and `is_active`.
 * @param assignments - An array of the goal's assignments, with `percentage_complete` calculated.
 * @returns An object containing the status details.
 */
export const getGoalStatus = (
  goal: any,
  assignments: any[]
): { color: string; label: string; status: GoalStatus } => {
  const now = dayjs();
  const hasEndDate = goal.end_date;
  const isAfterEndDate = hasEndDate && now.isAfter(dayjs(goal.end_date));

  const hasCompletedAssignment = assignments.some(
    (assignment) => assignment.percentage_complete >= 100
  );

  if (isAfterEndDate) {
    return { color: "gray", label: "Past", status: GoalStatus.Past };
  }

  if (hasCompletedAssignment) {
    return { color: "green", label: "Completed", status: GoalStatus.Completed };
  }

  if (!goal.is_active) {
    return { color: "gray", label: "Inactive", status: GoalStatus.Active };
  }

  const avgProgress =
    assignments.length > 0
      ? assignments.reduce((sum, a) => sum + a.percentage_complete, 0) /
        assignments.length
      : 0;

  if (avgProgress >= 80) {
    return { color: "green", label: "On Track", status: GoalStatus.Active };
  } else if (avgProgress >= 50) {
    return { color: "yellow", label: "Behind", status: GoalStatus.Active };
  } else {
    return { color: "red", label: "At Risk", status: GoalStatus.Active };
  }
};

/**
 * Generates a list of available past and current periods for a goal.
 * This is used for the "time-travel" feature, allowing users to view progress for historical periods.
 * @param goal - The goal object, containing `start_date`, `end_date`, and `period`.
 * @returns An array of objects, where each object represents a period with a display label and a value (the start date).
 */
export const getAvailablePeriods = (
  goal: any
): Array<{ label: string; value: string }> => {
  const { start_date, end_date, period } = goal;
  const goalStartDate = dayjs(start_date);
  const now = dayjs();
  const endDate = end_date ? dayjs(end_date) : now;

  const periods: Array<{ label: string; value: string }> = [];
  let currentPeriodStart = goalStartDate;

  while (
    currentPeriodStart.isBefore(endDate) ||
    currentPeriodStart.isSame(endDate, "day")
  ) {
    const periodEnd = calculatePeriodEnd(currentPeriodStart, period);

    // Stop if period start is in the future
    if (currentPeriodStart.isAfter(now)) break;

    let label = "";
    switch (period.toLowerCase()) {
      case "daily":
        label = currentPeriodStart.format("MMM D, YYYY");
        break;
      case "weekly":
        label = `${currentPeriodStart.format("MMM D")} - ${periodEnd.format(
          "MMM D, YYYY"
        )}`;
        break;
      case "monthly":
        label = `${currentPeriodStart.format("MMM D")} - ${periodEnd.format(
          "MMM D, YYYY"
        )}`;
        break;
    }

    periods.unshift({
      label,
      value: currentPeriodStart.format("YYYY-MM-DD"),
    });

    // Move to next period
    switch (period.toLowerCase()) {
      case "daily":
        currentPeriodStart = currentPeriodStart.add(1, "day");
        break;
      case "weekly":
        currentPeriodStart = currentPeriodStart.add(7, "day");
        break;
      case "monthly":
        currentPeriodStart = currentPeriodStart.add(1, "month");
        break;
    }
  }

  return periods;
};

export default {
  createGoal,
  updateGoal,
  deleteGoal,
  getGoals,
  getGoalById,
  calculateProgress,
  getGoalStatus,
  getAvailablePeriods,
  getGoalWithProgress,
};
