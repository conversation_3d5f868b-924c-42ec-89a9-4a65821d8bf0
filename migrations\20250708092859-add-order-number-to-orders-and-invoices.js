"use strict";

var dbm;
var type;
var seed;
var async = require("async");

/**
 * We receive the dbmigrate dependency from dbmigrate initially.
 * This enables us to not have to rely on NODE_PATH.
 */
exports.setup = function (options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

exports.up = function (db, callback) {
  async.series(
    [
      db.addColumn.bind(db, "order_detail", "order_number", {
        type: "int",
      }),
      db.addIndex.bind(db, "order_detail", "order_detail_order_number_idx", [
        "order_number",
      ]),

      db.addColumn.bind(db, "invoice", "order_number", {
        type: "int",
      }),
      db.addIndex.bind(db, "invoice", "invoice_order_number_idx", [
        "order_number",
      ]),

      db.runSql.bind(
        db,
        `
      UPDATE order_detail
      SET order_number = id
      WHERE order_number IS NULL
    `
      ),

      db.runSql.bind(
        db,
        `
      UPDATE invoice i
      SET order_number = od.order_number
      FROM order_detail od
      WHERE od.id = i.order_id
      AND i.order_id IS NOT NULL
      AND i.order_number IS NULL
    `
      ),
    ],
    callback
  );
};

exports.down = function (db, callback) {
  async.series(
    [
      db.removeColumn.bind(db, "order_detail", "order_number"),
      db.removeColumn.bind(db, "invoice", "order_number"),
    ],
    callback
  );
};

exports._meta = {
  version: 1,
};
