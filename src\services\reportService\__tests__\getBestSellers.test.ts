import knex from "../../../../knex/knex";
import getBestSellers from "../getBestSellers";

describe("getBestSellers Service Tests", () => {
  const testSupplierId = "31";

  beforeAll(async () => {
    await setupTestData();
  });

  afterAll(async () => {
    await cleanupTestData();
    await knex.destroy();
  });

  async function setupTestData() {
    await knex("supplier")
      .insert({
        id: testSupplierId,
        name: "Test Supplier",
        email: "<EMAIL>",
      })
      .onConflict("id")
      .merge();

    await knex("item")
      .insert([
        {
          id: 1001,
          name: "Coca Cola",
          supplier: "Test Supplier",
          price: 2.5,
          image: "coca-cola.jpg",
          archived: false,
        },
        {
          id: 1002,
          name: "Pepsi",
          supplier: "Test Supplier",
          price: 2.4,
          image: "pepsi.jpg",
          archived: false,
        },
        {
          id: 1003,
          name: "Sprite",
          supplier: "Test Supplier",
          price: 2.3,
          image: "sprite.jpg",
          archived: false,
        },
      ])
      .onConflict("id")
      .merge();

    await knex("category")
      .insert([
        {
          id: 101,
          name: "Beverages",
          supplier_id: testSupplierId,
        },
        {
          id: 102,
          name: "Snacks",
          supplier_id: testSupplierId,
        },
      ])
      .onConflict("id")
      .merge();

    await knex("category_item")
      .insert([
        { category_id: 101, item_id: 1001 },
        { category_id: 101, item_id: 1002 },
        { category_id: 101, item_id: 1003 },
      ])
      .onConflict(["category_id", "item_id"])
      .ignore();

    await knex("order_detail")
      .insert({
        id: 9001,
        user_id: 1984,
        status: "delivered",
        subtotal: 100,
        single_supplier: "Test Supplier",
        date_submitted: new Date().toISOString(),
      })
      .onConflict("id")
      .merge()
      .returning("id");

    await knex("order_detail")
      .insert({
        id: 9002,
        user_id: 1984,
        status: "delivered",
        subtotal: 150,
        single_supplier: "Test Supplier",
        date_submitted: new Date().toISOString(),
      })
      .onConflict("id")
      .merge()
      .returning("id");

    await knex("order_item").insert([
      {
        order_id: 9001,
        item_id: 1001,
        quantity: 100,
        price_purchased_at: 2.5,
      },
      {
        order_id: 9001,
        item_id: 1002,
        quantity: 80,
        price_purchased_at: 2.4,
      },
      {
        order_id: 9002,
        item_id: 1001,
        quantity: 50,
        price_purchased_at: 2.5,
      },
      {
        order_id: 9002,
        item_id: 1003,
        quantity: 70,
        price_purchased_at: 2.3,
      },
    ]);
  }

  async function cleanupTestData() {
    await knex("order_item").where("order_id", "in", [9001, 9002]).del();
    await knex("order_detail").where("id", "in", [9001, 9002]).del();
    await knex("category_item").where("category_id", "in", [101, 102]).del();
    await knex("category").where("id", "in", [101, 102]).del();
    await knex("item").where("id", "in", [1001, 1002, 1003]).del();
  }

  describe("getBestSellers functionality", () => {
    it("should return best sellers across all categories", async () => {
      const result = await getBestSellers({
        supplier_id: testSupplierId,
      });

      expect(result.bestSellers).toBeDefined();
      expect(result.totalCount).toBe(3);
      expect(result.bestSellers).toHaveLength(3);

      // Check that items are sorted by quantity descending
      expect(result.bestSellers[0]?.name).toBe("Coca Cola");
      expect(result.bestSellers[0]?.quantity).toBe(150); // 100 + 50
      expect(result.bestSellers[0]?.category?.name).toBe("Beverages");
      expect(result.bestSellers[0]?.category?.id).toBe("101");
      expect(result.bestSellers[1]?.name).toBe("Pepsi");
      expect(result.bestSellers[1]?.quantity).toBe(80);
      expect(result.bestSellers[1]?.category?.name).toBe("Beverages");
      expect(result.bestSellers[2]?.name).toBe("Sprite");
      expect(result.bestSellers[2]?.quantity).toBe(70);
      expect(result.bestSellers[2]?.category?.name).toBe("Beverages");
    });

    it("should handle limit parameter", async () => {
      const result = await getBestSellers({
        supplier_id: testSupplierId,
        limit: 2,
      });

      expect(result.bestSellers).toHaveLength(2);
      expect(result.totalCount).toBe(3);
      expect(result.bestSellers[0]?.name).toBe("Coca Cola");
      expect(result.bestSellers[1]?.name).toBe("Pepsi");
    });

    it("should handle pagination", async () => {
      const result = await getBestSellers({
        supplier_id: testSupplierId,
        pagination: { offset: 1, limit: 2 },
      });

      expect(result.bestSellers).toHaveLength(2);
      expect(result.totalCount).toBe(3);
      expect(result.bestSellers[0]?.name).toBe("Pepsi");
      expect(result.bestSellers[1]?.name).toBe("Sprite");
    });

    it("should filter by category", async () => {
      const result = await getBestSellers({
        supplier_id: testSupplierId,
        filters: { category: "101" },
      });

      expect(result.bestSellers).toHaveLength(3);
      expect(result.totalCount).toBe(3);
      expect(result.bestSellers[0]?.name).toBe("Coca Cola");
      expect(result.bestSellers[0]?.quantity).toBe(150);
      expect(result.bestSellers[0]?.category?.name).toBe("Beverages");
      expect(result.bestSellers[0]?.category?.id).toBe("101");

      // All items should have the same category since we filtered
      result.bestSellers.forEach((item) => {
        expect(item).toBeDefined();
        expect(item?.category?.name).toBe("Beverages");
        expect(item?.category?.id).toBe("101");
      });
    });

    it("should handle non-existent supplier", async () => {
      await expect(
        getBestSellers({
          supplier_id: "999999",
        })
      ).rejects.toThrow("Supplier with id 999999 not found");
    });

    it("should handle non-existent category", async () => {
      const result = await getBestSellers({
        supplier_id: testSupplierId,
        filters: { category: "999" },
      });

      expect(result.bestSellers).toHaveLength(0);
      expect(result.totalCount).toBe(0);
    });

    it("should return empty result when no orders exist for supplier", async () => {
      await knex("supplier")
        .insert({
          id: "999",
          name: "Empty Supplier",
          email: "<EMAIL>",
        })
        .onConflict("id")
        .merge();

      const result = await getBestSellers({
        supplier_id: "999",
      });

      expect(result.bestSellers).toHaveLength(0);
      expect(result.totalCount).toBe(0);

      await knex("supplier").where("id", "999").del();
    });
  });
});
