"use strict";

var dbm;
var type;
var seed;

/**
 * We receive the dbmigrate dependency from dbmigrate initially.
 * This enables us to not have to rely on NODE_PATH.
 */
exports.setup = function (options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

exports.up = function (db) {
  return db
    .createTable("user_item_favorites", {
      id: {
        type: "int",
        primaryKey: true,
        autoIncrement: true,
      },
      user_id: {
        type: "int",
        notNull: true,
        foreignKey: {
          name: "fk_favorites_user",
          table: "attain_user",
          mapping: "id",
          rules: {
            onDelete: "CASCADE",
            onUpdate: "CASCADE",
          },
        },
      },
      employee_id: {
        type: "int",
        notNull: false,
        foreignKey: {
          name: "fk_favorites_employee",
          table: "employees",
          mapping: "id",
          rules: {
            onDelete: "CASCADE",
            onUpdate: "CASCADE",
          },
        },
      },
      item_id: {
        type: "int",
        notNull: true,
        foreignKey: {
          name: "fk_favorites_item",
          table: "item",
          mapping: "id",
          rules: {
            onDelete: "CASCADE",
            onUpdate: "CASCADE",
          },
        },
      },
      created_at: {
        type: "timestamp",
        notNull: true,
        defaultValue: new String("CURRENT_TIMESTAMP"),
      },
    })
    .then(function () {
      // Create composite unique index to prevent duplicate favorites
      return db.addIndex(
        "user_item_favorites",
        "idx_unique_user_item_favorite",
        ["user_id", "item_id", "employee_id"],
        true
      );
    })
    .then(function () {
      // Create index for faster queries
      return db.addIndex("user_item_favorites", "idx_favorites_user_employee", [
        "user_id",
        "employee_id",
      ]);
    });
};

exports.down = function (db) {
  return db.dropTable("user_item_favorites");
};

exports._meta = {
  version: 1,
};
