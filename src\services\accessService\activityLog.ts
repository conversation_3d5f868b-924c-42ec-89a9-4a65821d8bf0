/**
 * Activity Log
 *
 * This service is used to log activity in the system.
 * It is used to track actions taken by users and suppliers.
 */

import {
  ActivityLog,
  ActivityLogInput,
  ActivityLogOutput,
  CreateActivityLogInput,
} from "../../generated/graphql";
import { Knex } from "knex";
import { ActivityLogRow } from "./db.types";
import knex from "../../../knex/knex";

export const createActivityLog = async (
  trx: Knex.Transaction,
  input: CreateActivityLogInput
): Promise<ActivityLog> => {
  const { supplierId, userId, employeeId, type, metadata, location } = input;

  console.log("Creating activity log", input);

  const logNumber = await trx<ActivityLogRow>("activity_log")
    .max("log_number")
    .where("supplier_id", supplierId)
    .first();
  console.log("logNumber", logNumber);
  const maxLogNumber = logNumber?.max ?? 0;

  console.log("maxLogNumber", maxLogNumber);
  const activityLog = await trx<ActivityLogRow>("activity_log")
    .insert({
      log_number: maxLogNumber + 1,
      supplier_id: supplierId,
      user_id: userId,
      employee_id: employeeId,
      type,
      metadata,
      location,
    })
    .returning("*");

  if (activityLog.length === 0) {
    throw new Error("Failed to create activity log");
  }

  return activityLog[0];
};

export const getActivityLog = async (
  input: ActivityLogInput,
  trx?: Knex.Transaction
): Promise<ActivityLogOutput> => {
  const { supplierId, filters, pagination, sortBy } = input;

  const db = trx ?? knex;

  const query = db<ActivityLogRow>("activity_log").where(
    "supplier_id",
    supplierId
  );

  if (filters?.userId) {
    query.where("user_id", filters.userId);
  }

  if (filters?.employeeId) {
    query.where("employee_id", filters.employeeId);
  }

  if (filters?.type) {
    query.where("type", filters.type);
  }

  if (filters?.location) {
    query.where("location", filters.location);
  }

  const countQuery = query.clone();
  const totalCount = await countQuery
    .count<{ count: string }>("id as count")
    .first();

  const activityLog = await query
    .orderBy(sortBy?.field ?? "created_at", sortBy?.ordering ?? "desc")
    .limit(pagination?.limit ?? 10)
    .offset(pagination?.offset ?? 0);

  return {
    activityLog,
    totalCount: Number(totalCount?.count ?? 0),
  };
};
