import knex from "../../../knex/knex";
import { MutationUpdateCategoryOrderArgs } from "../../generated/graphql";
import { updateCategoryOrder } from "../../services/itemService/categoryService";

const UpdateCategoryOrder = async (
  _,
  args: MutationUpdateCategoryOrderArgs
) => {
  const { supplierId, categoryId, newOrder } = args;

  await updateCategoryOrder(Number(supplierId), Number(categoryId), newOrder);

  // Return the updated category
  const updatedCategory = await knex("category")
    .where({ id: categoryId, supplier_id: supplierId })
    .first();

  return updatedCategory;
};

export default UpdateCategoryOrder;
