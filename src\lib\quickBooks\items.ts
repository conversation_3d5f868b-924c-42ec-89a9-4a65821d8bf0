import { Item } from "../../generated/graphql";
import QuickBooksClient from "./client";

export type AccountRef = {
  value: string;
  name: string;
};
export type QBItem = {
  Id: string;
  SyncToken: string;
  Name: string;
  Sku?: string;
  UnitPrice?: number;
  IncomeAccountRef?: AccountRef;
  ExpenseAccountRef?: AccountRef;
  AssetAccountRef?: AccountRef;
  sparse?: boolean;
  QtyOnHand?: number;
  Type: "Inventory";
} & Record<string, unknown>;

export type QBItemInput = {
  Id?: string;
  SyncToken?: string;
  Name: string;
  Sku?: string;
  UnitPrice: number;
  IncomeAccountRef: AccountRef;
  ExpenseAccountRef: AccountRef;
  AssetAccountRef: AccountRef;
  sparse?: boolean;
  Type: "Inventory";
  QtyOnHand: number;
  TrackQtyOnHand: true;
  InvStartDate: Date;
} & Record<string, unknown>;

const QuickBooksToDBItemMap = {
  Name: "name",
  Sku: "upc1",
  UnitPrice: "price",
  Id: "qb_id",
  SyncToken: "qb_sync_token",
  "new Date:MetaData.LastUpdatedTime": "updated_at",
  IncomeAccountRef: "incomeAccountRef",
  ExpenseAccountRef: "expenseAccountRef",
  AssetAccountRef: "assetAccountRef",
  QtyOnHand: "qty_on_hand",
};
const DBToQuickBooksItemMap = {
  qb_id: "Id",
  qb_sync_token: "SyncToken",
  name: "Name",
  upc1: "Sku",
  price: "UnitPrice",
  qty_on_hand: "QtyOnHand",
};

export const pullItemInfo = async (client: QuickBooksClient, id: string) => {
  try {
    const item = (await client.getItem(id)) as Record<string, unknown>;
    const itemToSave = {};
    if (item.Type === "Inventory") {
      Object.keys(QuickBooksToDBItemMap).forEach((key) => {
        itemToSave[QuickBooksToDBItemMap[key]] = item[key];
      });
    }
    return itemToSave;
  } catch (error) {
    console.error(error);
    const errorMessage = `Couldn't pull item with id ${id} from QB: ${JSON.stringify(
      error,
      undefined,
      2
    )}`;
    console.error(errorMessage);
    throw new Error(errorMessage);
  }
};

export const pullItemsInfo = async (
  client: QuickBooksClient,
  query?: Record<string, unknown>
) => {
  try {
    const items = (await client.findItems({
      type: "Inventory",
      ...query,
    })) as Record<string, unknown>[];
    // console.log(items);
    const itemsToSave = items.map((item) => {
      const itemToSave = {};
      Object.keys(QuickBooksToDBItemMap).forEach((key) => {
        const keyContainsType = key.indexOf(":") !== -1;
        const keyContainsSubParts = key.indexOf(".") !== -1;

        itemToSave[QuickBooksToDBItemMap[key]] = !keyContainsSubParts
          ? item[key]
          : (keyContainsType ? key.split(":")[1] : key)
              .split(".")
              .reduce((obj, i) => obj[i], item);
        if (keyContainsType) {
          itemToSave[QuickBooksToDBItemMap[key]] = eval(
            `${key.split(":")[0]}("${itemToSave[QuickBooksToDBItemMap[key]]}")`
          );
        }
      });
      return itemToSave;
    });
    return itemsToSave;
  } catch (error) {
    console.error(error);
    const errorMessage = `Couldn't pull inventory items from QB: ${JSON.stringify(
      error,
      undefined,
      2
    )}`;
    console.error(errorMessage);
    throw new Error(errorMessage);
  }
};

export const pushItemInfo = async (
  client: QuickBooksClient,
  item: Item,
  incomeAccountRef: AccountRef | null,
  expenseAccountRef: AccountRef | null,
  assetAccountRef: AccountRef | null
) => {
  const timeNow = new Date();
  try {
    // const items = (await client.findItems({
    //   Name: oldItemName,
    //   type: "Inventory",
    // })) as Record<string, unknown>[];
    // if (items.length === 0) {
    //   console.log(
    //     `Item ${item.name} (id: ${item.id}) not found in QuickBooks. Creating new item...`
    //   );
    // }
    const itemToUpdate: QBItem | QBItemInput | Record<string, unknown> = {
      ...(item.qb_id
        ? { sparse: true, Type: "Inventory" }
        : {
            IncomeAccountRef: incomeAccountRef,
            ExpenseAccountRef: expenseAccountRef,
            AssetAccountRef: assetAccountRef,
            QtyOnHand: 0,
            TrackQtyOnHand: true,
            InvStartDate: item.updated_at,
            Type: "Inventory",
          }),
    };
    Object.keys(DBToQuickBooksItemMap).forEach((key) => {
      itemToUpdate[DBToQuickBooksItemMap[key]] = item[key];
    });
    if (item.discounted_price) {
      itemToUpdate.UnitPrice = item.discounted_price;
    }
    if (item.archived) {
      itemToUpdate.Active = false;
      itemToUpdate.Name = `${
        itemToUpdate.Name
      } (deleted - ${timeNow.toDateString()})`;
    }

    const updatedItem = (
      item.qb_id
        ? await client.updateItem(itemToUpdate as QBItem)
        : await client.createItem(itemToUpdate as QBItemInput)
    ) as QBItem;
    // console.log(updatedItem);
    return updatedItem;
  } catch (error) {
    console.error(error);
    const errorMessage = `Couldn't push inventory item ${JSON.stringify(
      item,
      undefined,
      2
    )} to QB: ${JSON.stringify(error, undefined, 2)}`;
    console.error(errorMessage);
    throw new Error(errorMessage);
  }
};

export const pushItemsInfo = async (
  client: QuickBooksClient,
  items: Item[],
  incomeAccountRef: AccountRef | null,
  expenseAccountRef: AccountRef | null,
  assetAccountRef: AccountRef | null
) => {
  const timeNow = new Date();
  const itemsToPush = items.map((item) => {
    const itemToUpdate: QBItem | QBItemInput | Record<string, unknown> = {
      ...(item.qb_id
        ? { sparse: true, Type: "Inventory" }
        : {
            IncomeAccountRef: incomeAccountRef,
            ExpenseAccountRef: expenseAccountRef,
            AssetAccountRef: assetAccountRef,
            QtyOnHand: 1,
            TrackQtyOnHand: true,
            InvStartDate: item.updated_at,
            Type: "Inventory",
          }),
    };
    Object.keys(DBToQuickBooksItemMap).forEach((key) => {
      itemToUpdate[DBToQuickBooksItemMap[key]] = item[key];
    });
    if (item.discounted_price) {
      itemToUpdate.UnitPrice = item.discounted_price;
    }
    if (item.archived) {
      itemToUpdate.Active = false;
      itemToUpdate.Name = `${
        itemToUpdate.Name
      } (deleted - ${timeNow.toDateString()})`;
    }
    return itemToUpdate;
  });

  try {
    const updatedItems = (await Promise.all(
      itemsToPush.map((item) => {
        return (
          item.qb_id
            ? client.updateItem(item as QBItem)
            : client.createItem(item as QBItemInput)
        ).catch((error) => {
          console.error(
            `Couldn't push inventory item ${JSON.stringify(
              item,
              undefined,
              2
            )} to QB: ${JSON.stringify(error, undefined, 2)}`
          );
          return {};
        });
      })
    )) as QBItem[];
    // console.log(updatedItems);
    return updatedItems;
  } catch (error) {
    console.error(error);
    const errorMessage = `Couldn't push inventory items to QB: ${JSON.stringify(
      error,
      undefined,
      2
    )}`;
    console.error(errorMessage);
    throw new Error(errorMessage);
  }
};
