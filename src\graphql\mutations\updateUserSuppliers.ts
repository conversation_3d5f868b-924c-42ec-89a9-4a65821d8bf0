import knex from "../../../knex/knex";
import { MutationUpdateUserSuppliersArgs } from "../../generated/graphql";

const updateUserSuppliers = async (
  _,
  args: MutationUpdateUserSuppliersArgs
) => {
  const { suppliers: userSuppliers } = args.updateUserSuppliersInput;

  const suppliersUpdated: [] = await knex.transaction((trx) => {
    const queries = [];
    userSuppliers.forEach((userSupplier) => {
      const query = knex("supplier_times")
        .where("id", userSupplier.id)
        .update({
          cutoff_time: userSupplier.cutoffTime,
          cutoff_day: userSupplier.cutoffDay,
          delivery_time: userSupplier.deliveryTime,
          delivery_day: userSupplier.deliveryDay,
          days_to_delivery: userSupplier.daysToDelivery,
        })
        .returning("*")
        .transacting(trx); // This makes every update be in the same transaction
      queries.push(query);
    });

    Promise.all(queries) // Once every query is written
      .then(trx.commit) // We try to execute all of them
      .catch(trx.rollback); // And rollback in case any of them goes wrong
  });

  const suppliers = suppliersUpdated.map(
    (supplierUpdated) => supplierUpdated[0]
  );
  const results = await Promise.all(
    suppliers.map(async (row) => {
      const supplierData = await knex
        .select()
        .table("supplier")
        .where("id", row["supplier_id"]);
      if (
        supplierData.length === 0 ||
        typeof supplierData[0]["name"] !== "string"
      ) {
        return null;
      }

      return {
        id: row["id"],
        supplier: supplierData[0]["name"],
        cutoffDay: row["cutoff_day"],
        cutoffTime: row["cutoff_time"],
        deliveryDay: row["delivery_day"],
        deliveryTime: row["delivery_time"],
        daysToDelivery: row["days_to_delivery"],
      };
    })
  );
  return results;
};

export default updateUserSuppliers;
