import knex from "../../../knex/knex";
import { QueryOrderStatusesArgs } from "../../generated/graphql";

const OrderStatuses = async (_, args: QueryOrderStatusesArgs) => {
  const { orderId } = args;
  const orderStatuses = await knex
    .select("o.*", "s.name")
    .from("order_status as o")
    .where("o.order_id", orderId)
    .innerJoin("supplier as s", "o.supplier_id", "s.id");
  const result = orderStatuses.map((orderStatus) => {
    return {
      ...orderStatus,
      submission_date: getNewDate(orderStatus.submission_date),
      delivering_date: getNewDate(orderStatus.delivering_date),
      delivery_date: getNewDate(orderStatus.delivery_date),
    };
  });

  return result;
};

const getNewDate = (date) => {
  return date ? new Date(date).getTime() + 3600 * 8 * 1000 : null;
};

export default OrderStatuses;
// export {Orders, UserOrders}
