import { Knex } from "knex";
import knex from "../../../knex/knex";
import { dsdSupplierConfigMap } from "../../constants/suppliers";

export interface GMVFilters {
  lastNDays?: number;
  dateRange?: Date[];
  routeIds?: string[];
  driver?: string;
  serviceType?: string;
}

export const getGMV = async (
  supplierId: string,
  supplierName: string,
  filters: GMVFilters = {}
): Promise<number> => {
  const { lastNDays, dateRange, routeIds, driver, serviceType } = filters;

  const additionalFilters = async (
    tablePrefix?: string,
    excludeUsersFromDates?: boolean,
    filterTable = "order_detail"
  ) => {
    let dateField = "date_submitted";
    switch (filterTable) {
      case "invoice":
        dateField = "date_created";
        break;
      case "order_detail":
      default:
        dateField = "date_submitted";
    }

    const usersFromBeforeNDays =
      lastNDays && excludeUsersFromDates
        ? (
            await knex(filterTable)
              .where("single_supplier", supplierName)
              .whereRaw(
                `${dateField} <= current_date - interval '${lastNDays}' day`
              )
              .distinct("user_id")
          ).map((c) => c.user_id)
        : [];

    const usersFromNotInDateRange =
      dateRange && dateRange.length === 2 && excludeUsersFromDates
        ? (
            await knex(filterTable)
              .where("single_supplier", supplierName)
              .whereNotBetween(`${dateField}`, [dateRange[0], dateRange[1]])
              .distinct("user_id")
          ).map((c) => c.user_id)
        : [];

    const tablePrefixWithDot = `${tablePrefix || filterTable}.`;

    return (queryBuilder: Knex.QueryBuilder) => {
      if (lastNDays) {
        queryBuilder.whereRaw(
          `${tablePrefixWithDot}${dateField} > current_date - interval '${lastNDays}' day`
        );
        if (excludeUsersFromDates) {
          queryBuilder.whereNotIn(
            `${tablePrefixWithDot}user_id`,
            usersFromBeforeNDays
          );
        }
      }
      if (dateRange && dateRange.length === 2) {
        queryBuilder.whereBetween(`${tablePrefixWithDot}${dateField}`, [
          dateRange[0],
          dateRange[1],
        ]);
        if (excludeUsersFromDates) {
          queryBuilder.whereNotIn(
            `${tablePrefixWithDot}user_id`,
            usersFromNotInDateRange
          );
        }
      }

      if ((routeIds && routeIds.length) || driver || serviceType) {
        queryBuilder
          .innerJoin(
            "attain_user",
            "attain_user.id",
            `${tablePrefixWithDot}user_id`
          )
          .where((builder) => {
            if (driver) {
              // First check if there's a custom_driver on the order
              builder.where((subBuilder) => {
                subBuilder.whereRaw(
                  `${tablePrefixWithDot}config->>'custom_driver' = ?`,
                  driver
                );
              });
              // For orders with no custom_driver, check route_ids
              builder.orWhere((subBuilder) => {
                subBuilder
                  .whereRaw(
                    `${tablePrefixWithDot}config->>'custom_driver' IS NULL`
                  )
                  .andWhere((routeBuilder) => {
                    if (routeIds && routeIds.length) {
                      routeBuilder.where(
                        knex.raw(
                          `?::text[] && string_to_array(attain_user.route_id, ',')`,
                          [routeIds]
                        )
                      );
                    }
                  });
              });
            } else if (routeIds && routeIds.length) {
              // If using route instead of driver, check routes
              // First check if there's a custom_route on the order
              builder.where((subBuilder) => {
                subBuilder.whereRaw(
                  `${tablePrefixWithDot}config->>'custom_route' = ANY(?::text[])`,
                  [routeIds]
                );
              });
              // For orders with no custom_route, check route_ids
              builder.orWhere((subBuilder) => {
                subBuilder
                  .whereRaw(
                    `${tablePrefixWithDot}config->>'custom_route' IS NULL`
                  )
                  .andWhere((routeBuilder) => {
                    if (routeIds && routeIds.length) {
                      routeBuilder.where(
                        knex.raw(
                          `?::text[] && string_to_array(attain_user.route_id, ',')`,
                          [routeIds]
                        )
                      );
                    }
                  });
              });
            }
          })
          .where((builder) => {
            if (serviceType) {
              builder.whereRaw(
                "attain_user.config->>'service_type' = ?",
                serviceType
              );
            }
          });
      }
    };
  };

  const supplierIsDsd =
    Object.keys(dsdSupplierConfigMap).includes(supplierId) &&
    dsdSupplierConfigMap[supplierId].dsd;

  const gmvResult = supplierIsDsd
    ? await knex("invoice")
        .where("supplier_id", supplierId)
        .andWhereNot("invoice.archived", true)
        .andWhere("invoice.subtotal", ">", 0)
        .modify((queryBuilder) => {
          if (supplierId === "31") {
            queryBuilder.whereNotExists(function () {
              this.select("*")
                .from("order_detail")
                .whereRaw("order_detail.id = invoice.order_id")
                .where("order_detail.status", "Canceled");
            });
          } else {
            queryBuilder
              .join("order_detail", "order_detail.id", "invoice.order_id")
              .whereILike("order_detail.status", "%delivered%");
          }
        })
        .modify(await additionalFilters(undefined, undefined, "invoice"))
        .sum("invoice.subtotal")
    : await knex("order_detail")
        .where("single_supplier", supplierName)
        .andWhere("subtotal", ">", 0)
        .andWhereNot((builder) => {
          builder
            .whereILike("status", "%canceled%")
            .orWhereILike("status", "%cancelled%");
        })
        .modify(await additionalFilters())
        .sum("subtotal");

  const gmv = parseFloat(gmvResult[0]?.sum || "0");
  return gmv || 0;
};
