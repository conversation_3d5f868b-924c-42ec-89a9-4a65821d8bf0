import knex from "../../../knex/knex";
import axios from "axios";
import { v4 as uuidv4 } from "uuid";
import {
  createFirebaseAccount,
  createAuth0Account,
  deleteFirebaseAccount,
  deleteAuth0Account,
} from "./authController";

interface CreateEmployeeParams {
  supplierId: string;
  name: string;
  phone: string;
  email: string;
  password: string;
  appAccess?: boolean;
  dashboardAccess?: boolean;
  roleIds?: string[];
  routeIds?: string[];
}

/**
 * Creates an employee record in the database and sets up authentication accounts
 * Uses a transaction to ensure all operations succeed or fail together
 * @param params - Employee details
 * @returns The created employee record
 * @throws Error if any part of the process fails
 */
export const createEmployee = async (params: CreateEmployeeParams) => {
  const {
    name,
    phone,
    email,
    password,
    appAccess = false,
    dashboardAccess = false,
    roleIds = [],
    routeIds = [],
    supplierId,
  } = params;

  console.log("Creating employee:", params);

  // Track created accounts for cleanup if needed
  let firebaseUid: string | null = null;
  let auth0UserId: string | null = null;
  let employeeId: string | null = null;

  // Use a transaction to ensure data consistency
  return knex.transaction(async (trx) => {
    try {
      // 1. Create the employee record in the database
      const [employee] = await trx("employees")
        .insert({
          supplier_id: supplierId,
          name,
          phone,
          email,
          app_access: appAccess,
          dashboard_access: dashboardAccess,
          created_at: new Date(),
          updated_at: new Date(),
        })
        .returning("*");

      employeeId = employee.id;

      // 2. Create Firebase account for app access if needed
      if (appAccess) {
        firebaseUid = await createFirebaseAccount({
          email,
          password,
          employeeId: employee.id,
          supplierId,
        });
        console.log(`Firebase account created with UID: ${firebaseUid}`);
      }

      // 3. Create Auth0 account for dashboard access if needed
      if (dashboardAccess) {
        auth0UserId = await createAuth0Account({
          email,
          password,
          name,
          employeeId: employee.id,
          supplierId,
        });
        console.log(`Auth0 account created with ID: ${auth0UserId}`);
      }

      // 4. Assign roles if provided
      if (roleIds.length > 0) {
        const roleAssignments = roleIds.map((roleId) => ({
          employee_id: employee.id,
          role_id: roleId,
          assigned_at: new Date(),
        }));

        await trx("role_assignment").insert(roleAssignments);
      }

      // 5. Assign routes if provided
      if (routeIds.length > 0) {
        const routeAssignments = routeIds.map((routeId) => ({
          employee_id: employee.id,
          route_id: routeId,
          created_at: new Date(),
          updated_at: new Date(),
        }));

        await trx("route_assignment").insert(routeAssignments);
      }

      console.log("Employee created successfully:", employee);
      return employee;
    } catch (error) {
      console.error("Error in createEmployee transaction:", error);

      // Cleanup logic for external services
      // The transaction will automatically roll back the database changes
      // but Firebase and Auth0 accounts need to be manually deleted

      if (employeeId) {
        // Try to clean up Firebase account if it was created
        if (firebaseUid) {
          try {
            await deleteFirebaseAccount({
              employeeId,
              supplierId,
            });
            console.log(
              `Cleanup: Firebase account deleted for failed employee creation: ${employeeId}`
            );
          } catch (cleanupError) {
            console.error(
              `Cleanup failed: Could not delete Firebase account for employee ${employeeId}:`,
              cleanupError
            );
          }
        }

        // Try to clean up Auth0 account if it was created
        if (auth0UserId) {
          try {
            await deleteAuth0Account({
              employeeId,
              supplierId,
            });
            console.log(
              `Cleanup: Auth0 account deleted for failed employee creation: ${employeeId}`
            );
          } catch (cleanupError) {
            console.error(
              `Cleanup failed: Could not delete Auth0 account for employee ${employeeId}:`,
              cleanupError
            );
          }
        }
      }

      // Rethrow the original error
      throw error;
    }
  });
};

export default createEmployee;
