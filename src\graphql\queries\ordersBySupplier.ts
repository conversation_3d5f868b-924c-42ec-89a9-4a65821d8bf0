import { QueryOrdersBySupplierArgs } from "../../generated/graphql";
import { getOrders } from "../../services/orderService/orderService";

const OrdersBySupplier = async (_, args: QueryOrdersBySupplierArgs) => {
  const { supplierId, filters, pagination, sortBy } =
    args.getOrdersBySupplierInput;
  const { orders, totalCount } = await getOrders({
    supplierId,
    filters,
    pagination,
    sortBy,
  });
  return { orders, totalCount };
};

export default OrdersBySupplier;
