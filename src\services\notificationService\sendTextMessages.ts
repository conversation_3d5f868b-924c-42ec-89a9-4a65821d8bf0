import dotenv from "dotenv";
import twilio from "twilio";
import knex from "../../../knex/knex";
import getOrders from "../orderService/getOrder";
import { sendOrderDetailsEmail } from "../notificationService/sendEmail";
dotenv.config();

const client = twilio(
  process.env.TWILIO_ACCOUNT_SID,
  process.env.TWILIO_AUTH_TOKEN
);

export const cleanupPhoneNumber = (phoneNumber: string) => {
  return `+1${phoneNumber.replace(/\D/g, "").slice(-10)}`;
};

export default async function sendTextMessage(messageBody: string, to: string) {
  return client.messages.create({
    from: process.env.TWILIO_PHONE_NUMBER,
    to: to,
    body: messageBody,
  });
}

export async function sendOrderDetailsTextMessage(
  customerName,
  subtotal,
  phoneNUmber
) {
  const textMessage = `Hi, you've received a new order!\n\nCustomer: ${customerName}\nTotal: $${Number(
    subtotal
  ).toFixed(
    2
  )}\n\nPlease check your Attain dashboard to view and process this order.`;

  const phone_number =
    process.env.NODE_ENV === "staging"
      ? process.env.RECIPIENT_PHONE_NUMBER
      : cleanupPhoneNumber(phoneNUmber);

  await sendTextMessage(textMessage, phone_number)
    .then((message) => {
      console.log(`Sign-Up Message sent with SID: ${message.sid}`);
    })
    .catch((error) => {
      console.error(`Error sending signup message: ${error}`);
    });
}

export async function sendOrderDetailsMessage(orderId) {
  const order = await knex
    .select(
      "order_status.*",
      "order_detail.*",
      "supplier.*",
      "order_detail.id as id",
      knex.raw(
        `json_build_object('id', "attain_user".id, 'name', "attain_user".name, 'email', "attain_user".email, 'address', "attain_user".address, 'route_id', "attain_user".route_id) as "customerDetails"`
      )
    )
    .from("order_status")
    .rightOuterJoin("order_detail", "order_status.order_id", "order_detail.id")
    .leftOuterJoin("supplier", "order_detail.single_supplier", "supplier.name")
    .leftOuterJoin("attain_user", "order_detail.user_id", "attain_user.id")
    .where("order_detail.id", orderId)
    .orderBy("order_detail.id", "desc");

  const [orderDetails] = await getOrders(order);

  const customerName = orderDetails.customerDetails.name;
  const subtotal = orderDetails.subtotal;
  const phoneNumber = orderDetails.notification_number;
  const notificationEmail = orderDetails.notification_email;

  if (phoneNumber) {
    await sendOrderDetailsTextMessage(customerName, subtotal, phoneNumber);
  }

  if (notificationEmail) {
    await sendOrderDetailsEmail(customerName, subtotal, notificationEmail);
  }
}
