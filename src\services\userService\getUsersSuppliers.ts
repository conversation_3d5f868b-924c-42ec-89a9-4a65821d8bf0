import knex from "../../../knex/knex";
import { User } from "../../generated/graphql";

const getUsersSuppliers = async (users: User[]) => {
  const userIds = users.map((user) => user.id);

  const [suppliersData, defaultSuppliers] = await Promise.all([
    knex("supplier")
      .select([
        "supplier.*",
        knex.raw('COUNT(order_detail.single_supplier) as "orderCount"'),
        "supplier_times.business_id as user_id",
      ])
      .leftJoin("supplier_times", "supplier.id", "supplier_times.supplier_id")
      .leftJoin("order_detail", function () {
        this.on(
          "supplier_times.business_id",
          "=",
          "order_detail.user_id"
        ).andOn("supplier.name", "=", "order_detail.single_supplier");
      })
      .whereIn("supplier_times.business_id", userIds)
      .groupBy([
        "supplier.id",
        "supplier.name",
        "supplier.logo",
        "supplier.need_signup",
        "supplier.minimum",
        "supplier_times.business_id",
      ])
      .orderBy("orderCount", "desc"),

    knex
      .select("*")
      .from("supplier")
      .whereIn("id", [15, 12, 21, 22, 23, 24, 25]),
  ]);

  const userSuppliers = new Map();
  suppliersData.forEach((supplier) => {
    const userId = supplier.user_id;
    if (!userSuppliers.has(userId)) {
      userSuppliers.set(userId, []);
    }
    userSuppliers.get(userId).push({
      ...supplier,
      orderCount: parseInt(supplier.orderCount) || 0,
    });
  });

  users.forEach((user) => {
    user.suppliers =
      userSuppliers.get(user.id) ||
      defaultSuppliers.map((s) => ({
        ...s,
        orderCount: 0,
      }));
  });

  return users;
};

export default getUsersSuppliers;
