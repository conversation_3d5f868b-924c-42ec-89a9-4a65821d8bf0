import { ApolloServer } from "@apollo/server";
import { Request, Response, NextFunction } from "express";
import { ApolloHttpPost, apolloHttpPost } from "../util/apolloHttp";
import expoPushNotificationService, {
  ExpoPushNotification,
} from "../services/notificationService/expoPushNotification";
import Expo from "expo-server-sdk";

abstract class RestEndpoint {
  protected apolloHttpPost: ApolloHttpPost;
  protected expoPushNotification: ExpoPushNotification;
  constructor(apolloServerInit: ApolloServer, expoClient: Expo) {
    this.apolloHttpPost = apolloHttpPost(apolloServerInit);
    this.expoPushNotification = expoPushNotificationService(expoClient);
    this.handler = this.handler.bind(this);
    this.worker = this.worker.bind(this);
  }
  public abstract handler(
    req: Request,
    res: Response,
    next: NextFunction
  ): void | Promise<void>;
  protected abstract worker(...args: unknown[]): unknown | Promise<unknown>;
}

export default RestEndpoint;
