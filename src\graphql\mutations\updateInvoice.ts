import sharp from "sharp";
import knex from "../../../knex/knex";
import { Invoice, MutationUpdateInvoiceArgs } from "../../generated/graphql";
import addInvoiceItems from "../../services/invoiceService/addInvoiceItems";
import deleteInvoiceItems from "../../services/invoiceService/deleteInvoiceItems";
import updateInvoiceItems from "../../services/invoiceService/updateInvoiceItems";

const updateInvoice = async (_, args: MutationUpdateInvoiceArgs) => {
  const {
    invoice_items,
    return_items,
    id,
    payment_status,
    paid,
    ...invoiceData
  } = args.updateInvoiceInput;

  const subtotal = (invoice_items || [])
    .map((item) => item.price * item.quantity)
    .reduce((acc, itemTotal) => acc + itemTotal, 0);
  const credit = invoiceData.credit || 0;
  const invoiceItemsToAdd = (invoice_items || []).filter((item) => !item.id);
  const invoiceItemsToUpdate = (invoice_items || []).filter((item) => item.id);
  const trxProvider = knex.transactionProvider();

  const trx = await trxProvider();

  let signature = invoiceData.signature;
  if (signature) {
    let buf = Buffer.from(signature.split(",")[1], "base64");
    buf = await sharp(buf).resize(200).toBuffer();
    signature = `data:image/png;base64,${buf.toString("base64")}`;
  }

  const updateData: any = {
    ...invoiceData,
    signature,
    ...(invoice_items ? { subtotal, total: subtotal - credit } : {}),
  };

  if (payment_status === "bounced") {
    updateData.paid = 0;
    updateData.payment_status = "bounced";
    updateData.payment_method = null;
  } else {
    if (payment_status) updateData.payment_status = payment_status;
    if (paid !== undefined) updateData.paid = paid;
  }

  console.log("updateData", updateData);

  const invoice: Invoice = await trx("invoice")
    .update(updateData)
    .where("id", id)
    .returning("*")
    .then(async (updatedInvoice: Invoice[]) => {
      if (invoice_items) {
        await deleteInvoiceItems(
          updatedInvoice[0].id,
          invoiceItemsToUpdate && invoiceItemsToUpdate.length > 0
            ? invoiceItemsToUpdate
            : [],
          true,
          trxProvider
        );
        if (invoiceItemsToUpdate && invoiceItemsToUpdate.length > 0) {
          const updatedInvoiceItems = await updateInvoiceItems(
            updatedInvoice[0].id,
            invoiceItemsToUpdate,
            return_items ?? false,
            trxProvider
          );
          if (return_items)
            updatedInvoice[0].invoiceItems = updatedInvoiceItems;
        }
        if (invoiceItemsToAdd && invoiceItemsToAdd.length > 0) {
          const addedInvoiceItems = await addInvoiceItems(
            updatedInvoice[0].id,
            invoiceItemsToAdd,
            return_items ?? false,
            trxProvider
          );
          if (return_items)
            updatedInvoice[0].invoiceItems =
              updatedInvoice[0].invoiceItems.concat(addedInvoiceItems);
        }
      }
      (await trxProvider()).commit();
      return updatedInvoice[0];
    })
    .catch(async (err) => {
      console.error("Error updating Invoice:", err);
      (await trxProvider()).rollback();
      throw new Error(`Error updating Invoice ${id}: ${err.message}`);
    });
  return invoice;
};

export default updateInvoice;
