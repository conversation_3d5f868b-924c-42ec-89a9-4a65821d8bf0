import knex from "../../../knex/knex";
import { QueryOrderBySupplierArgs } from "../../generated/graphql";
import getOrderItemsBySupplier from "../../services/orderService/getOrderItemsBySupplier";

const OrderBySupplier = async (_, args: QueryOrderBySupplierArgs) => {
  const { orderId, supplierId } = args;
  const supplier = await knex
    .select("name")
    .from("supplier")
    .where("id", supplierId)
    .orderBy("id", "desc");
  const result = await getOrderItemsBySupplier(orderId, supplier[0].name);
  return result;
};

export default OrderBySupplier;
