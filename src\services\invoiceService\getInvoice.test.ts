import knex from "../../../knex/knex";
import { queryBuilder } from "../../../knex/mock";
import { getInvoiceByOrderId } from "./getInvoice";

jest.mock("../../../knex/knex", () => {
  const { queryBuilder } = require("../../../knex/mock");
  return {
    __esModule: true,
    default: jest.fn().mockReturnValue(queryBuilder),
  };
});

describe("getInvoiceByOrderId", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("returns the invoice when a matching order_id exists", async () => {
    const fakeInvoice = { id: "inv1", order_id: "order123", total: 100 };

    queryBuilder.first.mockResolvedValueOnce(fakeInvoice);

    const result = await getInvoiceByOrderId("order123");

    expect(knex).toHaveBeenCalledWith("invoice");
    expect(queryBuilder.where).toHaveBeenCalledWith("order_id", "order123");
    expect(queryBuilder.first).toHaveBeenCalled();
    expect(result).toEqual(fakeInvoice);
  });

  it("returns undefined when no matching order_id exists", async () => {
    queryBuilder.first.mockResolvedValueOnce(undefined);

    const result = await getInvoiceByOrderId("missing-order");

    expect(result).toBeUndefined();
  });

  it("throws an error if the database query fails", async () => {
    queryBuilder.first.mockRejectedValueOnce(new Error("DB failure"));

    await expect(getInvoiceByOrderId("order-error")).rejects.toThrow(
      "DB failure"
    );
  });
});
