import { ApolloServer } from "@apollo/server";
import {
  CompleteMultipartUploadCommandOutput,
  S3Client,
} from "@aws-sdk/client-s3";
import { Upload } from "@aws-sdk/lib-storage";
import { MailService } from "@sendgrid/mail";
import Expo from "expo-server-sdk";
import { Request, Response } from "express";
import mime from "mime-types";
import { v4 as uuidv4 } from "uuid";
import { awsS3Config, env } from "../config/environment";
import { User } from "../generated/graphql";
import RestEndpoint from "./_restEndpoint";
import { postTextViaSlackWebhook } from "../services/notificationService/slackWebhook";

export default class SendInvoice extends RestEndpoint {
  private s3Client: S3Client;
  private sendgridClient: MailService;
  constructor(
    apolloServerInit: ApolloServer,
    expoClient: Expo,
    s3Client: S3Client,
    sgMail: MailService
  ) {
    super(apolloServerInit, expoClient);
    this.s3Client = s3Client;
    this.sendgridClient = sgMail;
  }

  public async handler(req: Request, res: Response) {
    const body = req.body;
    try {
      console.log(`Sending Invoice ID: ${body.invoiceId}`);
      console.log(`To Email: ${body.email ? body.email : "[Default from DB]"}`);
      console.log(`Number of Attachments: ${req.files.length}`);
      const files =
        req.files && req.files.length
          ? (req.files as Express.Multer.File[]).filter(
              (file) => file.mimetype === mime.types.pdf
            )
          : [];
      if (files.length) {
        const fileUrls = await this.uploadToS3(
          files,
          body.invoiceId,
          new Date()
        );
        console.log(fileUrls);

        const userId = !env.production ? "1" : body.userId;
        const {
          email: businessEmail,
          name: businessName,
          suppliers,
        } = await this.getSupplierNamesBusinessNameAndEmail(userId);
        const emailToSendTo =
          body.email && body.email.length ? body.email : businessEmail;
        console.log(emailToSendTo);
        await this.worker(
          emailToSendTo,
          body.invoiceId,
          suppliers[0].name,
          files
        );

        postTextViaSlackWebhook(
          userId !== "1"
            ? "*********************************************************************************"
            : "*********************************************************************************",
          `Sent Invoice ${body.invoiceId} PDF to ${businessName} (${emailToSendTo}):\n` +
            fileUrls.join("\n")
        );
        res.status(200).send();
        return;
      }
      throw {
        code: 400,
        message: `No invoice PDFs found to send for Invoice ${body.invoiceId}`,
      };
    } catch (err) {
      const errorMessage = `Failed to send invoice PDF via email: ${
        err.message || err
      }`;
      console.log(errorMessage);
      res.status(err.code || 500).send(errorMessage);
    }
  }

  protected async worker(
    businessEmail: string,
    invoiceId: string,
    supplierName: string,
    files: Express.Multer.File[]
  ) {
    const emailMessage = {
      from: "<EMAIL>",
      to: businessEmail,
      subject: `Invoice ${invoiceId} from ${supplierName}`,
      text: `Please find attached the Invoice #${invoiceId} for your recent wholesale order from ${supplierName}, generated by Attain.`,
      attachments:
        files.length > 1
          ? files.map((file, idx) => ({
              content: file.buffer.toString("base64"),
              filename: `${supplierName} Invoice-${invoiceId}-${idx}.pdf`,
              type: "application/pdf",
              disposition: "attachment",
            }))
          : [
              {
                content: files[0].buffer.toString("base64"),
                filename: `${supplierName} Invoice-${invoiceId}.pdf`,
                type: "application/pdf",
                disposition: "attachment",
              },
            ],
    };
    await this.sendgridClient.send(emailMessage);
  }

  private async uploadToS3(
    files: Express.Multer.File[],
    invoiceId: string,
    emailDate: Date = new Date()
  ) {
    return await Promise.all(
      files.map(async (file) => {
        const params = {
          Bucket: awsS3Config.bucket,
          Key: `invoices/invoice-${invoiceId}-${uuidv4()}.pdf`,
          Body: file.buffer,
          ContentType: mime.types.buffer,
          ACL: "private",
          Metadata: {
            date: emailDate.toISOString(),
            invoiceId,
            fieldName: file.fieldname,
            originalName: file.originalname,
          },
        };
        const data = await new Upload({
          client: this.s3Client,
          params,
        }).done();
        return (data as CompleteMultipartUploadCommandOutput).Location;
      })
    );
  }

  private async getSupplierNamesBusinessNameAndEmail(userId: string) {
    const variables = {
      getUsersInput: {
        ids: [userId],
      },
    };
    const query = /* GraphQL */ `
      query UsersQuery($getUsersInput: GetUsersInput) {
        users(getUsersInput: $getUsersInput) {
          name
          email
          suppliers {
            name
          }
        }
      }
    `;
    const users = (
      (await this.apolloHttpPost(query, variables)) as { users: User[] }
    ).users;
    if (!users.length) {
      throw { code: 400, message: `No user with ID ${userId} found` };
    }
    if (!users[0].suppliers.length) {
      throw {
        code: 400,
        message: `No suppliers for user with ID ${userId} found`,
      };
    }
    return users[0];
  }
}
