"use strict";

var dbm;
var type;
var seed;

/**
 * We receive the dbmigrate dependency from dbmigrate initially.
 * This enables us to not have to rely on NODE_PATH.
 */
exports.setup = function (options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

exports.up = function (db) {
  return db.runSql(`
    CREATE INDEX IF NOT EXISTS idx_invoice_supplier_date
      ON invoice (supplier_id, date_created DESC)
      WHERE archived IS FALSE;
  `).then(() => {
    return db.runSql(`
      CREATE INDEX IF NOT EXISTS idx_invoice_custom_route_int
        ON invoice ( ((config->>'custom_route')::integer) );
    `);
  });
};

exports.down = function (db) {
  return db.runSql(`
    DROP INDEX IF EXISTS idx_invoice_supplier_date;
  `).then(() => {
    return db.runSql(`
      DROP INDEX IF EXISTS idx_invoice_custom_route_int;
    `);
  });
};

exports._meta = {
  version: 1,
}; 