"use strict";

var dbm;
var type;
var seed;

/**
 * We receive the dbmigrate dependency from dbmigrate initially.
 * This enables us to not have to rely on NODE_PATH.
 */
exports.setup = function (options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

var async = require("async");

exports.up = function (db, callback) {
  async.series(
    [
      db.createTable.bind(db, "attain_user", {
        id: { type: "serial", autoIncrement: true },
        name: { type: "string", length: 255 },
        email: { type: "string", length: 255 },
        store_id: { type: "int" },
        buyer_id: { type: "string", length: 255 },
        user_name: { type: "string", length: 255 },
        address: { type: "string", length: 255 },
      }),
      db.createTable.bind(db, "cart", {
        id: { type: "serial", autoIncrement: true },
        user_id: { type: "int" },
        subtotal: { type: "numeric" },
        csv: { type: "text" },
      }),
      db.createTable.bind(db, "cart_item", {
        id: { type: "serial", autoIncrement: true },
        cart_id: { type: "int" },
        item_id: { type: "int" },
        quantity: { type: "int" },
      }),
      db.createTable.bind(db, "order_detail", {
        id: { type: "int", primaryKey: true, autoIncrement: true },
        user_id: { type: "int" },
        subtotal: { type: "numeric" },
        status: { type: "string", length: 255 },
        date_submitted: { type: "timestamp" },
        single_supplier: { type: "string", length: 255 },
        order_name: { type: "string", length: 255 },
        discount: { type: "double precision" },
      }),
      db.createTable.bind(db, "test_order", {
        item_id: { type: "int" },
        quantity: { type: "int" },
        order_id: { type: "int" },
        customer_id: { type: "int" },
      }),
      db.createTable.bind(db, "item", {
        id: { type: "int", primaryKey: true, autoIncrement: true },
        name: { type: "string", length: 255 },
        nacs_category: { type: "string", length: 255 },
        nacs_subcategory: { type: "string", length: 255 },
        unit_size: { type: "string", length: 255 },
        upc1: { type: "string", length: 255 },
        upc2: { type: "string", length: 255 },
        price: { type: "numeric" },
        image: { type: "text" },
        supplier_code: { type: "string", length: 255 },
        correct_image: { type: "boolean" },
        supplier: { type: "string", length: 255 },
        discounted_price: { type: "numeric" },
        oos: { type: "boolean" },
        upc3: { type: "string", length: 255 },
        upc4: { type: "string", length: 255 },
        outdated: { type: "boolean" },
        size: { type: "text" },
        local_item: { type: "boolean" },
        metadata: { type: "text" },
      }),
      db.createTable.bind(db, "order_item", {
        id: { type: "serial", autoIncrement: true },
        order_id: {
          type: "int",
          foreignKey: {
            name: "fk_order_item_to_order_detail",
            table: "order_detail",
            mapping: "id",
            rules: {},
          },
        },
        item_id: {
          type: "int",
          foreignKey: {
            name: "fk_item",
            table: "item",
            mapping: "id",
            rules: {},
          },
        },
        quantity: { type: "int" },
        new_supplier: { type: "text" },
        oos: { type: "boolean" },
        price_purchased_at: { type: "numeric" },
      }),
      db.createTable.bind(db, "duffl_item", {
        id: { type: "serial", autoIncrement: true },
        name: { type: "string", length: 255 },
        item_id: { type: "int" },
        supplier: { type: "string", length: 255 },
        local_item: { type: "boolean" },
        created_by: { type: "int" },
        price: { type: "text" },
        image: { type: "text" },
        upc1: { type: "text" },
        upc2: { type: "text" },
        unit_size: { type: "text" },
      }),
      db.createTable.bind(db, "account", {
        id: { type: "serial", autoIncrement: true },
        user_id: { type: "int" },
        external_id: { type: "string", length: 255 },
        is_default: { type: "boolean" },
        type: { type: "text" },
        is_active: { type: "boolean" },
      }),
      db.createTable.bind(db, "supplier", {
        id: { type: "serial", autoIncrement: true },
        name: { type: "string", length: 255 },
        logo: { type: "text" },
        spotlight_image: { type: "text" },
        need_signup: { type: "boolean" },
      }),
      db.createTable.bind(db, "supplier_times", {
        id: { type: "serial", autoIncrement: true },
        supplier_id: { type: "int" },
        cutoff_time: { type: "time" },
        cutoff_day: { type: "string", length: 255 },
        delivery_time: { type: "time" },
        delivery_day: { type: "string", length: 255 },
        days_to_delivery: { type: "int" },
        business_id: { type: "int" },
      }),
      db.createTable.bind(db, "order_status", {
        id: { type: "serial", autoIncrement: true },
        order_id: { type: "int" },
        supplier_id: { type: "int" },
        delivery_date: { type: "date" },
        delivering_date: { type: "date" },
        submission_date: { type: "date" },
      }),
      db.createTable.bind(db, "duffl_order", {
        order_id: { type: "int" },
        status: { type: "text" },
        csv: { type: "text" },
        user_id: { type: "int" },
      }),
      db.createTable.bind(db, "spotlight", {
        id: { type: "serial", autoIncrement: true },
        supplier_id: { type: "int" },
        user_id: { type: "int" },
        active: { type: "boolean" },
      }),
      db.createTable.bind(db, "duffl_item_new", {
        id: { type: "int" },
        name: { type: "string", length: 255 },
        item_id: { type: "string", length: 255 },
        supplier: { type: "string", length: 255 },
        local_item: { type: "boolean" },
        created_by: { type: "int" },
      }),
      db.createTable.bind(db, "new_pitco", {
        id: { type: "int" },
        name: { type: "string", length: 255 },
        nacs_category: { type: "string", length: 255 },
        nacs_subcategory: { type: "string", length: 255 },
        unit_size: { type: "string", length: 255 },
        upc1: { type: "string", length: 255 },
        upc2: { type: "string", length: 255 },
        price: { type: "numeric" },
        image: { type: "text" },
        supplier_code: { type: "string", length: 255 },
        correct_image: { type: "boolean" },
        supplier: { type: "string", length: 255 },
        discounted_price: { type: "numeric" },
        oos: { type: "boolean" },
        upc3: { type: "string", length: 255 },
        upc4: { type: "string", length: 255 },
      }),
      db.createTable.bind(db, "items_new_images", {
        id: { type: "int" },
        name: { type: "string", length: 255 },
        nacs_category: { type: "string", length: 255 },
        nacs_subcategory: { type: "string", length: 255 },
        unit_size: { type: "string", length: 255 },
        upc1: { type: "string", length: 255 },
        upc2: { type: "string", length: 255 },
        price: { type: "numeric" },
        image: { type: "text" },
        supplier_code: { type: "string", length: 255 },
        correct_image: { type: "boolean" },
        supplier: { type: "string", length: 255 },
        discounted_price: { type: "numeric" },
        oos: { type: "boolean" },
        upc3: { type: "string", length: 255 },
        upc4: { type: "string", length: 255 },
      }),
      db.createTable.bind(db, "new_coremark", {
        id: { type: "int" },
        name: { type: "string", length: 255 },
        nacs_category: { type: "string", length: 255 },
        nacs_subcategory: { type: "string", length: 255 },
        unit_size: { type: "string", length: 255 },
        upc1: { type: "string", length: 255 },
        upc2: { type: "string", length: 255 },
        price: { type: "numeric" },
        image: { type: "text" },
        supplier_code: { type: "string", length: 255 },
        correct_image: { type: "boolean" },
        supplier: { type: "string", length: 255 },
        discounted_price: { type: "numeric" },
        oos: { type: "boolean" },
        upc3: { type: "string", length: 255 },
        upc4: { type: "string", length: 255 },
      }),
      db.createTable.bind(db, "cart_csv", {
        id: {
          type: "int",
          sequenceGenerated: {
            precedence: "ALWAYS",
          },
        },
        cart_id: { type: "int" },
        csv: { type: "text" },
        supplier: { type: "text" },
      }),
      db.createTable.bind(db, "order_item_test", {
        id: { type: "int" },
        order_id: { type: "int" },
        item_id: { type: "int" },
        quantity: { type: "int" },
        new_supplier: { type: "text" },
        oos: { type: "boolean" },
        price_purchased_at: { type: "numeric" },
      }),
      db.createTable.bind(db, "unfi", {
        id: { type: "int" },
        name: { type: "string", length: 255 },
        nacs_category: { type: "string", length: 255 },
        nacs_subcategory: { type: "string", length: 255 },
        unit_size: { type: "string", length: 255 },
        upc1: { type: "string", length: 255 },
        upc2: { type: "string", length: 255 },
        price: { type: "numeric" },
        image: { type: "text" },
        supplier_code: { type: "string", length: 255 },
        correct_image: { type: "boolean" },
        supplier: { type: "string", length: 255 },
        discounted_price: { type: "numeric" },
        oos: { type: "boolean" },
        upc3: { type: "string", length: 255 },
        upc4: { type: "string", length: 255 },
        outdated: { type: "boolean" },
      }),
      db.createTable.bind(db, "duffl", {
        product_name: { type: "string", length: 255 },
        item_id: { type: "int" },
        store_id: { type: "text" },
        date: { type: "date" },
        quantity: { type: "text" },
      }),
      db.createTable.bind(db, "hla", {
        id: { type: "int" },
        name: { type: "string", length: 255 },
        nacs_category: { type: "string", length: 255 },
        nacs_subcategory: { type: "string", length: 255 },
        unit_size: { type: "string", length: 255 },
        upc1: { type: "string", length: 255 },
        upc2: { type: "string", length: 255 },
        upc3: { type: "string", length: 255 },
        upc4: { type: "string", length: 255 },
        price: { type: "numeric" },
        image: { type: "text" },
        supplier_code: { type: "string", length: 255 },
        correct_image: { type: "boolean" },
        supplier: { type: "string", length: 255 },
        discounted_price: { type: "numeric" },
        oos: { type: "boolean" },
        outdated: { type: "boolean" },
      }),
      db.createTable.bind(db, "deal_item", {
        id: {
          type: "int",
          sequenceGenerated: {
            precedence: "ALWAYS",
          },
          primaryKey: true,
        },
        deal_id: { type: "int" },
        item_id: { type: "int" },
      }),
      db.createTable.bind(db, "deals", {
        id: {
          type: "int",
          sequenceGenerated: {
            precedence: "ALWAYS",
          },
        },
        type: { type: "text" },
        quantity: { type: "int" },
        discount: { type: "numeric" },
      }),
    ],
    callback
  );
};

exports.down = function (db, callback) {
  async.series(
    [
      db.dropTable.bind(db, "attain_user"),
      db.dropTable.bind(db, "cart"),
      db.dropTable.bind(db, "cart_item"),
      db.dropTable.bind(db, "test_order"),
      db.dropTable.bind(db, "order_item"),
      db.dropTable.bind(db, "duffl_item"),
      db.dropTable.bind(db, "account"),
      db.dropTable.bind(db, "supplier"),
      db.dropTable.bind(db, "supplier_times"),
      db.dropTable.bind(db, "order_status"),
      db.dropTable.bind(db, "duffl_order"),
      db.dropTable.bind(db, "spotlight"),
      db.dropTable.bind(db, "duffl_item_new"),
      db.dropTable.bind(db, "new_pitco"),
      db.dropTable.bind(db, "items_new_images"),
      db.dropTable.bind(db, "new_coremark"),
      db.dropTable.bind(db, "cart_csv"),
      db.dropTable.bind(db, "order_item_test"),
      db.dropTable.bind(db, "unfi"),
      db.dropTable.bind(db, "duffl"),
      db.dropTable.bind(db, "hla"),
      db.dropTable.bind(db, "deal_item"),
      db.dropTable.bind(db, "deals"),
    ],
    () => {
      async.series(
        [
          db.dropTable.bind(db, "order_detail"), // order_item depends on order_detail
          db.dropTable.bind(db, "item"), // order_item depends on item
        ],
        callback
      );
    }
  );
};

exports._meta = {
  version: 1,
};
