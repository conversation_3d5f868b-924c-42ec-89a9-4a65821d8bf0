import * as userServiceFunctions from "./userService";
import getUsersSuppliers from "./getUsersSuppliers";

// Export all functions from userService.ts
export const {
  getUser,
  getUsers,
  populateUsersCustomPrices,
  getUserCustomPrices,
  getGroupCustomPrices,
  getGroups,
  getUsersScheduledOnDay,
  updateUserCustomPrices,
  updateGroupCustomPrices,
  updateUser,
  getUsersInGroup,
} = userServiceFunctions;


// Export getUsersSuppliers
export { getUsersSuppliers };

// Default export for backward compatibility
export default {
  ...userServiceFunctions,
  getUsersSuppliers,
};
