import { startSync as startSyncService } from "../../services/integrationService/syncService";
import { StartSyncInput } from "../../generated/graphql";
import { env } from "../../config/environment";

const startSync = async (
  _: any,
  { input }: { input: StartSyncInput }
): Promise<boolean> => {
  if (!env.production) {
    console.log("Sync is only available in production");
    return false;
  }

  return startSyncService(input.supplierId, input.types);
};

export default startSync;
