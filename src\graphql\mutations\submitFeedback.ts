import axios from "axios";
import knex from "../../../knex/knex";

const submitFeedback = async (_, args) => {
  const { userId, issues, thumbsUp } = args.submitFeedbackInput;

  if (userId === "1") {
    return "success";
  }

  const store_name: string = (await knex("attain_user").where("id", userId))[0]
    .name;

  let text = store_name + " (user " + userId + ") ";
  if (thumbsUp) {
    text += "left positive order feedback! 👍";
  } else if (issues == null || issues.length === 0) {
    text += "left negative order feedback without any reported issues";
  } else {
    text += "left negative order feedback, reported issues:\n>" + issues;
  }

  try {
    await axios.post(
      "*********************************************************************************",
      { text },
      {
        headers: {
          accept: "application/json",
          "content-type": "application/json",
        },
      }
    );
  } catch (error) {
    console.log("Error sending Slack message", error);
  }

  return "success";
};

export default submitFeedback;
