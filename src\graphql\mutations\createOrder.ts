import knex from "../../../knex/knex";
import {
  ActivityLogType,
  MutationCreateOrderArgs,
  Order,
} from "../../generated/graphql";
import { USDollarFromNumber } from "../../util/formats";
import { dsdSupplierConfigMap } from "../../constants/suppliers";
import { createOrder as createOrderFn } from "../../services/orderService/createOrder";
import dayjs from "dayjs";
import { postTextViaSlackWebhook } from "../../services/notificationService/slackWebhook";
import { createInvoiceFromOrder } from "../../services/invoiceService";
import { env } from "../../config/environment";
import getCart from "../../services/cartService/getCart";
import { clearCartItems } from "../../services/cartService/clearCart";
import { createActivityLog } from "../../services/accessService/activityLog";

const createOrder = async (
  _,
  args: MutationCreateOrderArgs
): Promise<Order> => {
  const { userId, supplierId, orderItems, deliveryDate, ...orderFields } =
    args.createOrderInput;

  let cart;
  let items = orderItems;
  // If no order items are provided, use the cart items
  if (!orderItems || orderItems.length === 0) {
    cart = await getCart(userId);
    items = cart.cartItems.map((item) => ({
      id: item.item_id,
      quantity: item.quantity,
      price_purchased_at: item.price_purchased_at,
    }));
  }

  const initialOrderDetails = await createOrderFn({
    supplierId,
    userId,
    orderItems: items,
    deliveryDate,
    order_name: orderFields.orderName,
    notes: orderFields.notes,
    config: orderFields.config,
  });

  const newOrder = await knex("order_detail")
    .where({ id: initialOrderDetails.id })
    .first();

  if (!newOrder) {
    throw new Error("Failed to create or find the order after creation.");
  }

  await createInvoiceFromOrder(supplierId, newOrder.id);

  if (cart) {
    await clearCartItems(cart.id);
  }

  await knex.transaction(async (trx) => {
    await createActivityLog(trx, {
      supplierId: parseInt(supplierId),
      userId: parseInt(userId),
      type: ActivityLogType.OrderCreated,
      metadata: {
        order_id: newOrder.id,
        order_number: newOrder.order_number,
      },
    });
  });

  const business = await knex("attain_user").where("id", userId).first();
  let slackUrl = process.env.SUBMITTED_ORDERS_SLACK_CHANNEL;
  if (dsdSupplierConfigMap[supplierId]?.orderNotificationSlackURL) {
    slackUrl = dsdSupplierConfigMap[supplierId].orderNotificationSlackURL;
  }
  if (env.production) {
    const supplierDetails = await knex("supplier")
      .where({ id: supplierId })
      .first();
    const subtotal = initialOrderDetails.subtotal;
    await postTextViaSlackWebhook(
      slackUrl,
      `${subtotal > 0 ? "Order" : "Credit Memo"} #${
        newOrder.order_number // Use order_number instead of id
      } for ${supplierDetails.name} created by ${
        business.name
      } for ${USDollarFromNumber(subtotal)}`
    );
  }

  return newOrder;
};

export default createOrder;
