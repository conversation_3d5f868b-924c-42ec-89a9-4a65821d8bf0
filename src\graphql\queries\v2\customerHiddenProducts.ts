import { QueryCustomerHiddenProductsArgs } from "../../../generated/graphql";
import { getCustomerHiddenProducts } from "../../../services/itemService";

const CustomerHiddenProducts = async (
  _,
  args: QueryCustomerHiddenProductsArgs
) => {
  const { supplierId, customerId } = args.customerHiddenProductsInput;

  const hiddenProductIds = await getCustomerHiddenProducts(
    supplierId,
    customerId
  );

  const items = hiddenProductIds.map((product) => ({
    itemId: product.item_id.toString(),
  }));

  return {
    items: items,
  };
};

export default CustomerHiddenProducts;
