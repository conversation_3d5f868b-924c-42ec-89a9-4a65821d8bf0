import { Knex } from "knex";

export async function seed(knex: Knex): Promise<void> {
  // Deletes ALL existing entries
  await knex("sync_status").del();

  // Inserts seed entries
  await knex("sync_status").insert([
    {
      supplier_id: 1,
      type: "items",
      status: "COMPLETED",
      error: null,
      start_time: new Date("2024-01-01T10:00:00Z"),
      end_time: new Date("2024-01-01T10:05:00Z"),
      duration_ms: 300000,
      last_synced: new Date("2024-01-01T10:05:00Z"),
    },
    {
      supplier_id: 1,
      type: "customers",
      status: "FAILED",
      error: "Network error",
      start_time: new Date("2024-01-01T10:02:00Z"),
      end_time: new Date("2024-01-01T10:03:00Z"),
      duration_ms: 60000,
      last_synced: new Date("2024-01-01T10:03:00Z"),
    },
    {
      supplier_id: 2,
      type: "items",
      status: "PENDING",
      error: null,
      start_time: null,
      end_time: null,
      duration_ms: null,
      last_synced: new Date("2024-01-01T09:00:00Z"),
    },
    {
      supplier_id: 2,
      type: "customers",
      status: "IN_PROGRESS",
      error: null,
      start_time: new Date("2024-01-01T11:00:00Z"),
      end_time: null,
      duration_ms: null,
      last_synced: new Date("2024-01-01T11:00:00Z"),
    },
  ]);
}
