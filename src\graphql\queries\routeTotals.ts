import { Knex } from "knex";
import knex from "../../../knex/knex";
import { QueryRouteTotalsArgs, Supplier } from "../../generated/graphql";
import { useDayJsWithTz } from "../../util/dayjsConfig";

const routeTotals = async (_, args: QueryRouteTotalsArgs) => {
  const { supplierId, numWeeks, dayOffset, currentTz } =
    args.getRouteTotalsInput;
  const shiftByDays = `${(dayOffset || 0) >= 0 ? "+" : "-"} INTERVAL '${
    dayOffset || 0
  } days'`;
  const tzToUse = currentTz || "America/New_York";
  const today = useDayJsWithTz(tzToUse).startOf("day").toDate();

  // const { name: supplier } = await knex<Supplier>("supplier")
  //   .select("name")
  //   .where("id", supplierId)
  //   .orderBy("id", "desc")
  //   .first();

  const weekRanges = knex.with('week_ranges', qb => {
    qb.select({
      idx: knex.raw('generate_series(1, ?)', [numWeeks]),
  
      start_date: knex.raw(`
        date_trunc('week', ?::timestamptz)                  
        ${shiftByDays}
        - generate_series(1, ?)*interval '7 days'
      `, [today.toISOString(), numWeeks]),
  
      end_date: knex.raw(`
        date_trunc('week', ?::timestamptz)
        ${shiftByDays}
        - generate_series(1, ?)*interval '7 days'
        + interval '6 days'
      `, [today.toISOString(), numWeeks])
    });
  })
  

  .with('invoice_items_norm', qb => {
    qb.select({
        route_id: 'r.route_id',          
        quantity: 'ii.quantity',
        created:  'i.date_created'
      })
      .from({ ii: 'invoice_item' })
      .join({ i:  'invoice'      }, 'ii.invoice_id', 'i.id')
      .join({ u:  'attain_user'  }, 'i.user_id',     'u.id')
      .joinRaw(`
        CROSS JOIN LATERAL (
          SELECT (i.config->>'custom_route')::int  AS route_id
          WHERE  i.config->>'custom_route' IS NOT NULL
      
          UNION ALL
      
          SELECT unnest(string_to_array(NULLIF(u.route_id, ''), ','))::int
          WHERE  i.config->>'custom_route' IS NULL
        ) AS r
      `)
      
      .where('i.supplier_id', supplierId)
      .whereNot('i.archived', true)
  })

  .with('qty_per_route_week', qb => {
    qb.select({
        route_id: 'route_id',
        wk_idx:   'wr.idx',
        qty:      knex.raw('SUM(quantity)::int')
      })
      .from({ ii: 'invoice_items_norm' })
      .join({ wr: 'week_ranges' }, function() {
        this.onBetween('ii.created', [knex.ref('wr.start_date'), knex.ref('wr.end_date')])
      })
      .groupBy('route_id', 'wk_idx')
  })
  .select('r.id', 'r.name')
  .select(knex.raw(`
    ARRAY(
      SELECT COALESCE(q.qty,0)::int
      FROM   generate_series(1, ? ) g(idx)           
      LEFT   JOIN qty_per_route_week q
             ON q.route_id = r.id
            AND q.wk_idx   = g.idx
      ORDER  BY g.idx DESC                          
    ) AS weekly_totals
  `, [numWeeks]))
  .from({ r: 'route' })
  .where('r.supplier_id', supplierId)
  .orderBy('r.name');

  return weekRanges;
};

export default routeTotals;
