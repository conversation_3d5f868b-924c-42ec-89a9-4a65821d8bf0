import knex from "../../../knex/knex";
import {
  GetSalesByRoutesInput,
  GetSalesByRoutesOutput,
} from "../../generated/graphql";

async function getSalesByRoutes(
  getSalesByRoutesInput: GetSalesByRoutesInput
): Promise<GetSalesByRoutesOutput> {
  const {
    supplier_id,
    filters,
    pagination = {},
    limit = 10,
  } = getSalesByRoutesInput;

  const { offset = 0, limit: paginationLimit } = pagination;
  const effectiveLimit = paginationLimit || limit;

  const supplier = await knex("supplier")
    .where("id", supplier_id)
    .select("name")
    .first();

  if (!supplier) {
    throw new Error(`Supplier with id ${supplier_id} not found`);
  }

  // Get sales data grouped by route
  const query = knex({ od: "order_detail" })
    .select(
      "route.name as route_name",
      knex.raw("COUNT(DISTINCT od.user_id) as number_of_stores"),
      knex.raw("SUM(od.subtotal) as total_sales_value")
    )
    .innerJoin("attain_user", "attain_user.id", "od.user_id")
    .innerJoin(
      knex.raw(
        `route ON route.supplier_id = ? AND (
        od.config->>'custom_route' = route.id::text OR 
        (od.config->>'custom_route' IS NULL AND route.id::text = ANY(string_to_array(attain_user.route_id, ',')))
      )`,
        [supplier_id]
      )
    )
    .where("od.single_supplier", supplier.name)
    .andWhereNot((builder) => {
      builder
        .whereILike("status", "%canceled%")
        .orWhereILike("status", "%cancelled%");
    })
    .modify(applyAdditionalFilters, filters)
    .groupBy("route.id", "route.name")
    .having(knex.raw("SUM(od.subtotal) > 0"))
    .orderBy("total_sales_value", "desc");

  const countQuery = query.clone().clearSelect().clearGroup().clearOrder();
  countQuery.count();

  const totalCountResult = await countQuery;
  const totalCount = parseInt(totalCountResult[0]?.count as string) || 0;

  const salesByRoutesResult = await query.limit(effectiveLimit).offset(offset);

  const salesByRoutes = salesByRoutesResult.map((route, index) => ({
    rank: offset + index + 1,
    route_name: route.route_name,
    number_of_stores: parseInt(route.number_of_stores),
    total_sales_value: parseFloat(route.total_sales_value),
  }));

  return { salesByRoutes, totalCount };
}

const applyAdditionalFilters = (queryBuilder, filters) => {
  const { duration, dateRange, driver, serviceType } = filters || {};

  if (duration) {
    queryBuilder.whereRaw(
      `od.date_submitted > current_date - interval '${duration}' day`
    );
  }

  if (dateRange && dateRange.length === 2) {
    queryBuilder.whereBetween("od.date_submitted", [
      dateRange[0],
      dateRange[1],
    ]);
  }

  if (driver || serviceType) {
    queryBuilder.where((builder) => {
      if (driver) {
        // First check if there's a custom_driver on the order
        builder.where((subBuilder) => {
          subBuilder.whereRaw(`od.config->>'custom_driver' = ?`, driver);
        });
        // For orders with no custom_driver, check normal routes
        builder.orWhere((subBuilder) => {
          subBuilder
            .whereRaw(`od.config->>'custom_driver' IS NULL`)
            .andWhere("route.driver", driver);
        });
      }
    });

    if (serviceType) {
      queryBuilder.whereRaw(
        "attain_user.config->>'service_type' = ?",
        serviceType
      );
    }
  }
};

export default getSalesByRoutes;
