import knex from "../../../knex/knex";
import { InvoiceItem, QueryActionItemsArgs } from "../../generated/graphql";

const actionItems = async (_, args: QueryActionItemsArgs) => {
  const { invoice_id, invoice_item_id } = args.getActionItemsInput;
  const actionItems = {
    mispick: {
      description:
        "Item is mispick (wrong flavor, size, etc). Set aside for credit requests",
    },
  };

  const invoiceItem: InvoiceItem = await knex
    .select("*")
    .from("invoice_item")
    .where("invoice_id", invoice_id)
    .where("id", invoice_item_id)
    .first();

  const { checked_in_quantity, quantity, is_mispick } = invoiceItem;

  if (!is_mispick) {
    return [];
  } else {
    return [
      {
        description: actionItems["mispick"].description,
        invoiceItem: invoiceItem,
      },
    ];
  }
};

export default actionItems;
