import knex from "../../../knex/knex";
import { SyncStatus, SyncStatusItem } from "../../generated/graphql";

/**
 * Inserts a new row or updates an existing row by ID for a given supplier/type.
 * When inserting (no recordId), returns the new record ID.
 * When updating (recordId provided), updates that specific record.
 */
export async function upsertStatus(
  supplierId: string,
  type: string,
  params: Partial<{
    status: SyncStatusItem["status"];
    errorMessage: string | null;
    startedAt: Date | null;
    completedAt: Date | null;
  }>,
  recordId?: number
): Promise<number> {
  const now = new Date();

  if (recordId) {
    await knex("sync_status")
      .where("id", recordId)
      .update({
        status: params.status ?? SyncStatus.InProgress,
        error: params.errorMessage ?? null,
        start_time: params.startedAt ?? null,
        end_time: params.completedAt ?? null,
        duration_ms:
          params.startedAt && params.completedAt
            ? params.completedAt.getTime() - params.startedAt.getTime()
            : null,
        last_synced: params.status === "COMPLETED" ? now : undefined,
        updated_at: now,
      });
    return recordId;
  }

  const [result] = await knex("sync_status")
    .insert({
      supplier_id: supplierId,
      type,
      status: params.status ?? SyncStatus.InProgress,
      error: params.errorMessage ?? null,
      start_time: params.startedAt ?? null,
      end_time: params.completedAt ?? null,
      duration_ms:
        params.startedAt && params.completedAt
          ? Math.round(
              (params.completedAt.getTime() - params.startedAt.getTime()) / 1000
            )
          : null,
      last_synced: params.status === "COMPLETED" ? now : undefined,
      updated_at: now,
    })
    .returning("id");
  return result.id;
}

export async function listStatusesBySupplier(
  supplierId: string
): Promise<SyncStatusItem[]> {
  const rows = await knex("sync_status")
    .where("supplier_id", supplierId)
    .orderBy("start_time", "desc")
    .orderBy("created_at", "desc");

  return rows.map((r) => ({
    type: r.type,
    status: r.status,
    error: r.error ?? undefined,
    lastSynced: r.last_synced?.toISOString() ?? undefined,
    startedAt: r.start_time?.toISOString() ?? undefined,
    completedAt: r.end_time?.toISOString() ?? undefined,
    durationMs: r.duration_ms ?? undefined,
  }));
}

export async function getHistoricalStatusesBySupplier(
  supplierId: string,
  type?: string
): Promise<SyncStatusItem[]> {
  // Get all historical records for a supplier, optionally filtered by type
  let query = knex("sync_status")
    .where("supplier_id", supplierId)
    .orderBy("created_at", "desc");

  if (type) {
    query = query.where("type", type);
  }

  const rows = await query;

  return rows.map((r) => ({
    type: r.type,
    status: r.status,
    error: r.error ?? undefined,
    lastSynced: r.last_synced?.toISOString() ?? undefined,
    startedAt: r.start_time?.toISOString() ?? undefined,
    completedAt: r.end_time?.toISOString() ?? undefined,
    durationMs: r.duration_ms ?? undefined,
  }));
}

export async function markInProgress(
  supplierId: string,
  type: string
): Promise<number> {
  return await upsertStatus(supplierId, type, {
    status: SyncStatus.InProgress,
    startedAt: new Date(),
    errorMessage: null,
    completedAt: null,
  });
}

export async function markCompleted(
  supplierId: string,
  type: string,
  startedAt: Date,
  recordId: number
): Promise<void> {
  const completed = new Date();
  await upsertStatus(
    supplierId,
    type,
    {
      status: SyncStatus.Completed,
      completedAt: completed,
      startedAt,
    },
    recordId
  );
}

export async function markFailed(
  supplierId: string,
  type: string,
  startedAt: Date,
  error: Error,
  recordId: number
): Promise<void> {
  const completed = new Date();
  await upsertStatus(
    supplierId,
    type,
    {
      status: SyncStatus.Failed,
      errorMessage: error.message,
      startedAt,
      completedAt: completed,
    },
    recordId
  );
}
