import fs from "fs/promises";
import path from "path";
import os from "os";
import knex from "../../knex/knex";
import { ImageProcessor } from "../../src/lib/imageProcessor";
import { awsS3Config } from "../../src/config/environment";
import { S3Client } from "@aws-sdk/client-s3";

/*
 * <PERSON><PERSON><PERSON> to process item images for C&G Snacks
 * - Fetches items from database where supplier = 'C&G Snacks'
 * - Downloads and processes images with Sharp
 * - Resizes based on aspect ratio rules
 * - Converts to WebP format with near_lossless and quality 75
 * - Saves locally for testing (S3 upload commented out initially)
 *
 * Usage: npx ts-node scripts/tools/processItemImages.ts
 */

interface ItemImage {
  id: number;
  name: string;
  image: string;
}

const OUTPUT_DIR = path.join(__dirname, "../../local_storage/thumbnails");

async function fetchCGSnacksItems(limit = 20): Promise<ItemImage[]> {
  console.log(`Fetching first ${limit} items for Planet Express...`);

  const items = await knex("item")
    .select("id", "name", "image")
    .where("supplier", "Planet Express")
    .whereNotNull("image")
    .where("image", "!=", "")
    .limit(limit);

  console.log(`Found ${items.length} ${items[0]} items with images`);
  return items;
}

// Process a single item
async function processSingleItem(item: ItemImage) {
  try {
    // Process image using the reusable library
    console.log(`Downloading and processing from: ${item.image}`);
    const s3Client = new S3Client({
      credentials: {
        accessKeyId: awsS3Config.credentials.accessKeyId || "",
        secretAccessKey: awsS3Config.credentials.secretAccessKey || "",
      },
      region: awsS3Config.region,
    });

    const fileName = `img-${item.id}-sm.png`;
    const imageProcessor = new ImageProcessor(s3Client);

    const result = await imageProcessor.processImageFromUrl(
      item.image,
      fileName,
      {
        processing: {
          targetHeight: 180,
          format: "png",
          quality: 100,
          compressionLevel: 9,
          effort: 10,
        },
        s3Upload: {
          bucket: awsS3Config.bucket,
          keyPrefix: "images/planetexpress",
        },
      }
    );

    // Update the item's img_sm field in the database with the S3 URL
    if (result.s3Url) {
      await knex("item").where("id", item.id).update({
        img_sm: result.s3Url,
        updated_at: knex.fn.now(),
      });

      console.log(
        `✅ Updated database img_sm field for item ${item.id} with URL: ${result.s3Url}`
      );
    }

    return {
      success: true,
      result: {
        itemId: item.id,
        itemName: item.name,
        localPath: result.localPath!,
        s3Url: result.s3Url,
        metadata: result.metadata,
      },
    };
  } catch (error) {
    console.error(`❌ Failed to process ${item.name}:`, error.message);
    return {
      success: false,
      result: {
        itemId: item.id,
        itemName: item.name,
        error: error.message,
      },
    };
  }
}

// Process items in parallel batches
async function processItemsInBatches(items: ItemImage[], batchSize = 5) {
  const results = {
    successful: [] as Array<{
      itemId: number;
      itemName: string;
      localPath: string;
      s3Url?: string;
      metadata?: any;
    }>,
    failed: [] as Array<{
      itemId: number;
      itemName: string;
      error: string;
    }>,
  };

  // Process items in batches to avoid overwhelming the system
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    const batchNumber = Math.floor(i / batchSize) + 1;
    const totalBatches = Math.ceil(items.length / batchSize);

    console.log(
      `\n🔄 Processing batch ${batchNumber}/${totalBatches} (${batch.length} items)`
    );

    // Process all items in this batch in parallel
    const batchPromises = batch.map((item) => processSingleItem(item));

    const batchResults = await Promise.allSettled(batchPromises);

    // Collect results from this batch
    batchResults.forEach((result, index) => {
      if (result.status === "fulfilled") {
        if (result.value.success) {
          results.successful.push(
            result.value.result as {
              itemId: number;
              itemName: string;
              localPath: string;
              s3Url?: string;
              metadata?: any;
            }
          );
        } else {
          results.failed.push(
            result.value.result as {
              itemId: number;
              itemName: string;
              error: string;
            }
          );
        }
      } else {
        // Handle promise rejection
        const item = batch[index];
        console.error(`❌ Promise rejected for ${item.name}:`, result.reason);
        results.failed.push({
          itemId: item.id,
          itemName: item.name,
          error: `Promise rejected: ${result.reason.message || result.reason}`,
        });
      }
    });

    console.log(
      `✅ Batch ${batchNumber} completed: ${
        batchResults.filter((r) => r.status === "fulfilled" && r.value.success)
          .length
      } successful, ${
        batchResults.filter(
          (r) =>
            r.status === "rejected" ||
            (r.status === "fulfilled" && !r.value.success)
        ).length
      } failed`
    );
  }

  return results;
}

async function processItemImages() {
  try {
    const items = await fetchCGSnacksItems(300);

    if (items.length === 0) {
      console.log("No items found with images for C&G Snacks");
      return;
    }

    // Determine optimal batch size based on system capabilities
    const cpuCount = os.cpus().length;
    const optimalBatchSize = Math.min(Math.max(cpuCount, 3), 8); // Between 3-8 parallel processes
    console.log(
      `Detected ${cpuCount} CPU cores, using batch size of ${optimalBatchSize}`
    );

    // Process items in parallel batches
    const results = await processItemsInBatches(items, optimalBatchSize);

    // Summary
    console.log("\n===== PROCESSING SUMMARY =====");
    console.log(`Total items: ${items.length}`);
    console.log(`Successful: ${results.successful.length}`);
    console.log(`Failed: ${results.failed.length}`);

    if (results.failed.length > 0) {
      console.log("\n===== FAILED ITEMS =====");
      results.failed.forEach((failure) => {
        console.log(
          `${failure.itemName} (ID: ${failure.itemId}): ${failure.error}`
        );
      });

      // Save failed items to JSON file for manual review
      const failedItemsPath = path.join(OUTPUT_DIR, "failed_items.json");
      const failedItemsWithDetails = results.failed.map((failure) => {
        const item = items.find((i) => i.id === failure.itemId);
        return {
          ...failure,
          originalImageUrl: item?.image,
          fullItemDetails: item,
        };
      });

      await fs.writeFile(
        failedItemsPath,
        JSON.stringify(failedItemsWithDetails, null, 2)
      );
      console.log(`\nFailed items saved to: ${failedItemsPath}`);
    }

    // Save successful items to JSON file as well
    if (results.successful.length > 0) {
      const successfulItemsPath = path.join(
        OUTPUT_DIR,
        "successful_items.json"
      );
      const successfulItemsWithDetails = results.successful.map((success) => {
        const item = items.find((i) => i.id === success.itemId);
        return {
          ...success,
          originalImageUrl: item?.image,
          fullItemDetails: item,
        };
      });

      await fs.writeFile(
        successfulItemsPath,
        JSON.stringify(successfulItemsWithDetails, null, 2)
      );
      console.log(`Successful items saved to: ${successfulItemsPath}`);
    }

    console.log(`\nLocal files saved to: ${OUTPUT_DIR}`);
  } catch (error) {
    console.error("Error in image processing script:", error);
    process.exit(1);
  } finally {
    // Close database connection
    await knex.destroy();
  }
}

// Run the script
if (require.main === module) {
  processItemImages()
    .then(() => {
      console.log("Image processing completed successfully");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Script failed:", error);
      process.exit(1);
    });
}

export { processItemImages };
