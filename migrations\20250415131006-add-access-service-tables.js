"use strict";

var dbm;
var type;
var seed;

/**
 * We receive the dbmigrate dependency from dbmigrate initially.
 * This enables us to not have to rely on NODE_PATH.
 */
exports.setup = function (options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

var async = require("async");

exports.up = function (db, callback) {
  async.series(
    [
      // Create update timestamp trigger function
      db.runSql.bind(
        db,
        `
        CREATE OR REPLACE FUNCTION update_timestamp()
        RETURNS TRIGGER AS $$
        BEGIN
          NEW.updated_at = CURRENT_TIMESTAMP;
          RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
      `
      ),

      // Create Employee table
      db.createTable.bind(db, "employees", {
        id: { type: "serial", primaryKey: true, autoIncrement: true },
        supplier_id: {
          type: "int",
          foreignKey: {
            name: "fk_employee_to_supplier",
            table: "supplier",
            mapping: "id",
            rules: {
              onDelete: "CASCADE",
              onUpdate: "RESTRICT",
            },
          },
        },
        name: { type: "string", length: 255 },
        phone: { type: "string", length: 20 },
        email: { type: "string", length: 255 },
        app_access: { type: "boolean", defaultValue: false },
        dashboard_access: { type: "boolean", defaultValue: false },
        created_at: {
          type: "timestamp",
          notNull: true,
          defaultValue: new String("CURRENT_TIMESTAMP"),
        },
        updated_at: {
          type: "timestamp",
          notNull: true,
          defaultValue: new String("CURRENT_TIMESTAMP"),
        },
        last_login: { type: "timestamp" },
        archived: { type: "boolean", defaultValue: false },
      }),

      // Create Roles table
      db.createTable.bind(db, "roles", {
        id: { type: "serial", primaryKey: true, autoIncrement: true },
        name: { type: "string", length: 50 },
        description: { type: "text" },
        created_at: {
          type: "timestamp",
          notNull: true,
          defaultValue: new String("CURRENT_TIMESTAMP"),
        },
        updated_at: {
          type: "timestamp",
          notNull: true,
          defaultValue: new String("CURRENT_TIMESTAMP"),
        },
      }),

      // Create Role_Assignment table with foreign keys
      db.createTable.bind(db, "role_assignment", {
        employee_id: {
          type: "int",
          foreignKey: {
            name: "fk_role_assignment_to_employee",
            table: "employees",
            mapping: "id",
            rules: {
              onDelete: "CASCADE",
              onUpdate: "RESTRICT",
            },
          },
        },
        role_id: {
          type: "int",
          foreignKey: {
            name: "fk_role_assignment_to_roles",
            table: "roles",
            mapping: "id",
            rules: {
              onDelete: "CASCADE",
              onUpdate: "RESTRICT",
            },
          },
        },
        assigned_at: {
          type: "timestamp",
          notNull: true,
          defaultValue: new String("CURRENT_TIMESTAMP"),
        },
      }),
      // Create Route_Assignment table with foreign keys
      db.createTable.bind(db, "route_assignment", {
        id: { type: "serial", primaryKey: true, autoIncrement: true },
        route_id: {
          type: "int",
          foreignKey: {
            name: "fk_route_assignment_to_route",
            table: "route",
            mapping: "id",
            rules: {
              onDelete: "CASCADE",
              onUpdate: "RESTRICT",
            },
          },
        },
        employee_id: {
          type: "int",
          foreignKey: {
            name: "fk_route_assignment_to_employee",
            table: "employees",
            mapping: "id",
            rules: {
              onDelete: "CASCADE",
              onUpdate: "RESTRICT",
            },
          },
        },
        created_at: {
          type: "timestamp",
          notNull: true,
          defaultValue: new String("CURRENT_TIMESTAMP"),
        },
        updated_at: {
          type: "timestamp",
          notNull: true,
          defaultValue: new String("CURRENT_TIMESTAMP"),
        },
      }),

      // Add triggers for auto-updating updated_at columns
      db.runSql.bind(
        db,
        `
        CREATE TRIGGER update_employees_timestamp
        BEFORE UPDATE ON employees
        FOR EACH ROW
        EXECUTE FUNCTION update_timestamp();
        
        CREATE TRIGGER update_roles_timestamp
        BEFORE UPDATE ON roles
        FOR EACH ROW
        EXECUTE FUNCTION update_timestamp();
        
        CREATE TRIGGER update_route_assignment_timestamp
        BEFORE UPDATE ON route_assignment
        FOR EACH ROW
        EXECUTE FUNCTION update_timestamp();
      `
      ),
    ],
    callback
  );
};

exports.down = function (db, callback) {
  async.series(
    [
      // Drop triggers
      db.runSql.bind(
        db,
        `
        DROP TRIGGER IF EXISTS update_employees_timestamp ON employees;
        DROP TRIGGER IF EXISTS update_roles_timestamp ON roles;
        DROP TRIGGER IF EXISTS update_route_assignment_timestamp ON route_assignment;
      `
      ),

      // Drop trigger function
      db.runSql.bind(db, `DROP FUNCTION IF EXISTS update_timestamp();`),

      // Drop the tables
      db.dropTable.bind(db, "role_assignment"),
      db.dropTable.bind(db, "roles"),
      db.dropTable.bind(db, "route_assignment"),
      db.dropTable.bind(db, "employees"),
    ],
    callback
  );
};

exports._meta = {
  version: 1,
};
