// 20250709153630-add-sync-status-table.js
"use strict";

var dbm;
var type;
var seed;

exports.setup = function (options, seedLink) {
  dbm  = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

exports.up = async function (db) {
  await db.createTable("sync_status", {
    id: { type: "serial", primaryKey: true },          
    supplier_id: {
      type: "int",
      notNull: true,
      foreignKey: {
        name:   "fk_sync_status_supplier",
        table:  "supplier",
        mapping:"id",
        rules:  { onDelete: "NO ACTION", onUpdate: "CASCADE" },
      },
    },
    type:            { type: "string", length: 50, notNull: true },
    status:          { type: "string", length: 20, notNull: true },
    error:   { type: "text" },
    start_time:      { type: "timestamp" },
    end_time:        { type: "timestamp" },
    duration_ms:     { type: "numeric" },
    last_synced:     { type: "timestamp", notNull: true, defaultValue: new String("CURRENT_TIMESTAMP") },
    created_at:      { type: "timestamp", notNull: true, defaultValue: new String("CURRENT_TIMESTAMP") },
    updated_at:      { type: "timestamp", notNull: true, defaultValue: new String("CURRENT_TIMESTAMP") },
    archived_at:     { type: "timestamp" },            
  });

  await db.addIndex("sync_status", "idx_sync_status_supplier_type_created_at", ["supplier_id", "created_at"]);
};

exports.down = async function (db) {
  await db.removeIndex("sync_status", "idx_sync_status_supplier_type_created_at");
  await db.dropTable("sync_status");
};

exports._meta = { version: 1 };
