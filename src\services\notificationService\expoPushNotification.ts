import {
  Expo,
  ExpoPushErrorTicket,
  ExpoPushMessage,
  ExpoPushSuccessTicket,
  ExpoPushTicket,
} from "expo-server-sdk";
import { backOff } from "exponential-backoff";

export type ExpoPushNotification = {
  sendCommonPushNotification: (
    tokens: string[],
    title: string,
    subtitle: string,
    body: string,
    data: object
  ) => Promise<void>;
  sendPushNotifications: (messages: ExpoPushMessage[]) => Promise<void>;
  verifyNotificationReceiptIds: (
    tickets: ExpoPushTicket[]
  ) => Promise<number[]>;
};

const expoPushNotification: (expoClient: Expo) => ExpoPushNotification = (
  expo: Expo
) => {
  const sendCommonPushNotification = async (
    tokens: string[],
    title: string,
    subtitle: string,
    body: string,
    data: object
  ) => {
    const messages: ExpoPushMessage[] = [];
    for (const token of tokens) {
      messages.push({
        to: token,
        sound: "default",
        title,
        subtitle,
        body,
        data,
      });
    }
    await sendPushNotifications(messages);
  };

  const sendPushNotifications = async (messages: ExpoPushMessage[]) => {
    // Break the messages into chunks of 1000 each to send at once.
    const chunks = expo.chunkPushNotifications(messages);
    const tickets: ExpoPushTicket[] = [];
    // Send the push notification chunks to the Expo push notification service concurrently.
    await Promise.all(
      chunks.map(async (chunk) => {
        try {
          const ticketChunk = await backOff(
            () => expo.sendPushNotificationsAsync(chunk),
            { retry: (e) => e.code === 429 || e.code >= 500 }
          );
          console.log(ticketChunk);
          tickets.push(...ticketChunk);
          // NOTE: If a ticket contains an error code in ticket.details.error, you
          // must handle it appropriately. The error codes are listed in the Expo
          // documentation:
          // https://docs.expo.io/push-notifications/sending-notifications/#individual-errors
        } catch (error) {
          console.error(
            "Error occurred sending push notifications via Expo:",
            error
          );
        }
      })
    );

    const problematicTicketsIdx = await verifyNotificationReceiptIds(tickets);
    problematicTicketsIdx.length &&
      console.error(
        `Could not deliver notifications to ${problematicTicketsIdx.length} tokens:`,
        problematicTicketsIdx.map(
          (idx) => (tickets[idx] as ExpoPushErrorTicket).message
        )
      );
  };

  const verifyNotificationReceiptIds = async (tickets: ExpoPushTicket[]) => {
    const receiptIds = [];
    const problematicTicketsIdx: number[] = [];
    tickets.forEach((ticket, idx) => {
      // NOTE: Not all tickets have IDs; for example, tickets for notifications
      // that could not be enqueued will have error information and no receipt ID.
      "id" in ticket
        ? receiptIds.push(ticket.id)
        : problematicTicketsIdx.push(idx);
    });

    const receiptIdChunks = expo.chunkPushNotificationReceiptIds(receiptIds);
    await Promise.all(
      receiptIdChunks.map(async (chunk) => {
        try {
          const receipts = await backOff(
            () => expo.getPushNotificationReceiptsAsync(chunk),
            { retry: (e) => e.code === 429 || e.code >= 500 }
          );
          console.log(receipts);
          // The receipts specify whether Apple or Google successfully received the
          // notification and information about an error, if one occurred.
          for (const receiptId in receipts) {
            const receipt = receipts[receiptId];
            if (receipt.status === "ok") {
              continue;
            } else if (receipt.status === "error") {
              problematicTicketsIdx.push(
                tickets.findIndex(
                  (ticket: ExpoPushSuccessTicket) => ticket.id === receiptId
                )
              );
              console.error(
                `There was an error sending a notification: ${receipt.message}`
              );
              if (receipt.details && receipt.details.error) {
                // The error codes are listed in the Expo documentation:
                // https://docs.expo.io/push-notifications/sending-notifications/#individual-errors
                // You must handle the errors appropriately.
                console.error(`The error code is ${receipt.details.error}`);
              }
            }
          }
        } catch (error) {
          console.error(
            "Error occurred fetching receiptIds for push notifications via Expo:",
            error
          );
        }
      })
    );
    return problematicTicketsIdx;
  };

  return {
    sendCommonPushNotification,
    sendPushNotifications,
    verifyNotificationReceiptIds,
  };
};

export default expoPushNotification;
