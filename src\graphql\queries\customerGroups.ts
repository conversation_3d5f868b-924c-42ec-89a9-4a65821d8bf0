import knex from "../../../knex/knex";
import { QueryCustomerGroupsArgs } from "../../generated/graphql";

const customerGroups = async (_, args: QueryCustomerGroupsArgs) => {
    const { supplierId } = args;

    const stores = await knex({ u: "attain_user" })
        .select("store_group")
        .select(knex.raw("u.config->>'active' as active"))
        .distinctOn("u.id")
        .innerJoin({ st: "supplier_times" }, "u.id", "st.business_id")
        .where("st.supplier_id", supplierId)
        .where("u.supplier_beta", true)
        .whereNot("archived", true)


    const names = stores.filter(store => store.store_group !== null).map(store => ({ name: store.store_group, active: store.active }));

    return Object.values(
        names.reduce((acc, { name, active }) => {
            if (!acc[name]) {
                acc[name] = { group: name, count: 0, active: 0 };
            }
            acc[name].count += 1;
            if (active !== 'false') {
                acc[name].active += 1;
            }
            return acc;
        }, {}));
};

export default customerGroups;
