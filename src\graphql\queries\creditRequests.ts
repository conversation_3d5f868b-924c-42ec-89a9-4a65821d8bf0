import knex from "../../../knex/knex";
import { QueryCreditRequestsArgs } from "../../generated/graphql";

const creditRequests = async (_, args: QueryCreditRequestsArgs) => {
  const { userId, supplierId, orderId } = args;
  const creditRequests = knex("credit_request")
    .select(
      "credit_request.*",
      knex.raw(
        `json_build_object('id', "attain_user".id, 'name', "attain_user".name, 'email', "attain_user".email, 'address', "attain_user".address, 'route_id', "attain_user".route_id) as "customerDetails"`
      ),
      knex.raw(
        `json_build_object(
          'id', "item".id,
          'name', "item".name,
          'unit_size', "item".unit_size,
          'image', "item".image,
          'upc1', "item".upc1) as "itemDetails"`
      )
    )
    .leftJoin("attain_user", "credit_request.user_id", "attain_user.id")
    .leftJoin("item", "credit_request.item_id", "item.id");

  if (userId) {
    creditRequests.where("user_id", userId);
  }
  if (supplierId) {
    creditRequests.where("supplier_id", supplierId);
  }
  if (orderId) {
    creditRequests.where("order_id", orderId);
  }

  const cr = await creditRequests.orderBy("credit_request.id", "desc");
  console.log(cr);

  return cr;
};

export default creditRequests;
