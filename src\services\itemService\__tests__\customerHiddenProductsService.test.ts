import knex from "../../../../knex/knex";
import {
  updateCustomerHiddenProducts,
  getCustomerHiddenProducts,
} from "../customerHiddenProductsService";

import { withTransaction } from "../../../util/transcationsHelper";

describe("customerHiddenProductsService", () => {
  const supplierId = "1";
  const customerId = "123";

  beforeEach(async () => {
    // Clean up any existing test data
    await knex("customer_hidden_product").del();

    // Insert test supplier and user if they don't exist
    await knex("supplier")
      .insert([{ id: 1, name: "Test Supplier", email: "<EMAIL>" }])
      .onConflict("id")
      .merge();

    await knex("attain_user")
      .insert([{ id: 123, name: "Test User", email: "<EMAIL>" }])
      .onConflict("id")
      .merge();
  });

  afterEach(async () => {
    await knex("customer_hidden_product").del();
  });

  afterAll(async () => {
    await knex("customer_hidden_product").del();
    await knex.destroy();
  });

  describe("updateCustomerHiddenProducts", () => {
    it("should add new products when current state is empty", async () => {
      const desiredProductIds = ["456", "789"];

      const result = await withTransaction((trx) =>
        updateCustomerHiddenProducts(
          trx,
          supplierId,
          customerId,
          desiredProductIds
        )
      );

      expect(result).toBe(true);

      // Verify in database
      const hiddenProducts = await knex("customer_hidden_product")
        .select("item_id")
        .where("supplier_id", supplierId)
        .where("user_id", customerId);

      expect(hiddenProducts.map((p) => p.item_id.toString()).sort()).toEqual([
        "456",
        "789",
      ]);
    });

    it("should add new products and remove old ones based on diff", async () => {
      // First, insert some initial hidden products
      await knex("customer_hidden_product").insert([
        { supplier_id: 1, user_id: 123, item_id: 100 },
        { supplier_id: 1, user_id: 123, item_id: 200 },
      ]);

      // Now update to a different set: keep 200, remove 100, add 300
      const desiredProductIds = ["200", "300"];

      const result = await withTransaction((trx) =>
        updateCustomerHiddenProducts(
          trx,
          supplierId,
          customerId,
          desiredProductIds
        )
      );

      expect(result).toBe(true);

      // Verify in database
      const hiddenProducts = await knex("customer_hidden_product")
        .select("item_id")
        .where("supplier_id", supplierId)
        .where("user_id", customerId);

      expect(hiddenProducts.map((p) => p.item_id.toString()).sort()).toEqual([
        "200",
        "300",
      ]);
    });

    it("should remove all products when desired state is empty", async () => {
      // First, insert some initial hidden products
      await knex("customer_hidden_product").insert([
        { supplier_id: 1, user_id: 123, item_id: 100 },
        { supplier_id: 1, user_id: 123, item_id: 200 },
      ]);

      // Now clear all hidden products
      const desiredProductIds = [];

      const result = await withTransaction((trx) =>
        updateCustomerHiddenProducts(
          trx,
          supplierId,
          customerId,
          desiredProductIds
        )
      );

      expect(result).toBe(true);

      // Verify in database
      const hiddenProducts = await knex("customer_hidden_product")
        .select("item_id")
        .where("supplier_id", supplierId)
        .where("user_id", customerId);

      expect(hiddenProducts).toEqual([]);
    });

    it("should do nothing when current and desired states are the same", async () => {
      // First, insert some initial hidden products
      await knex("customer_hidden_product").insert([
        { supplier_id: 1, user_id: 123, item_id: 100 },
        { supplier_id: 1, user_id: 123, item_id: 200 },
      ]);

      // Request the same state
      const desiredProductIds = ["100", "200"];

      const result = await withTransaction((trx) =>
        updateCustomerHiddenProducts(
          trx,
          supplierId,
          customerId,
          desiredProductIds
        )
      );

      expect(result).toBe(true);

      // Verify in database - should be unchanged
      const hiddenProducts = await knex("customer_hidden_product")
        .select("item_id")
        .where("supplier_id", supplierId)
        .where("user_id", customerId);

      expect(hiddenProducts.map((p) => p.item_id.toString()).sort()).toEqual([
        "100",
        "200",
      ]);
    });
  });

  describe("getCustomerHiddenProducts", () => {
    it("should return array of hidden product IDs", async () => {
      // Insert test data
      await knex("customer_hidden_product").insert([
        { supplier_id: 1, user_id: 123, item_id: 456 },
        { supplier_id: 1, user_id: 123, item_id: 789 },
      ]);

      const result = await getCustomerHiddenProducts(supplierId, customerId);

      expect(result.map((r) => r?.item_id).sort()).toEqual(["456", "789"]);
    });

    it("should return empty array when no hidden products", async () => {
      const result = await getCustomerHiddenProducts(supplierId, customerId);

      expect(result).toEqual([]);
    });

    it("should only return products for the specific customer and supplier", async () => {
      // Insert test data for different customers and suppliers
      await knex("customer_hidden_product").insert([
        { supplier_id: 1, user_id: 123, item_id: 456 }, // Our target
        { supplier_id: 1, user_id: 123, item_id: 789 }, // Our target
        { supplier_id: 1, user_id: 999, item_id: 111 }, // Different customer
        { supplier_id: 2, user_id: 123, item_id: 222 }, // Different supplier
      ]);

      const result = await getCustomerHiddenProducts(supplierId, customerId);

      expect(result.map((r) => r?.item_id).sort()).toEqual(["456", "789"]);
    });
  });
});
