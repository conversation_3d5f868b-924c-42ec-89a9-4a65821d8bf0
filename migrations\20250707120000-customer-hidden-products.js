"use strict";

const { version } = require("os");

let dbm;
let type;
let seed;

exports.setup = function (options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

exports.up = function (db) {
  return (
    db
      /* ─────────────────────────────────────────────────────────────
         1. Create table
         ───────────────────────────────────────────────────────────── */
      .createTable(
        "customer_hidden_product",
        {
          id: {
            type: "serial",
            primaryKey: true,
            autoIncrement: true,
            notNull: true,
          },
          supplier_id: {
            type: "int",
            notNull: true,
            foreignKey: {
              name: "fk_chp_to_supplier",
              table: "supplier",
              mapping: "id",
              rules: {
                onDelete: "CASCADE",
                onUpdate: "CASCADE",
              },
            },
          },
          user_id: {
            type: "int",
            notNull: true,
            foreignKey: {
              name: "fk_chp_to_attain_user",
              table: "attain_user",
              mapping: "id",
              rules: {
                onDelete: "NO ACTION",
                onUpdate: "NO ACTION",
              },
            },
          },
          item_id: {
            type: "int",
            notNull: true,
            foreignKey: {
              name: "fk_chp_to_item",
              table: "item",
              mapping: "id",
              rules: {
                onDelete: "NO ACTION",
                onUpdate: "NO ACTION",
              },
            },
          },
          created_by: { type: "int" }, // optional: system user
          created_at: {
            type: "timestamp",
            notNull: true,
            defaultValue: new String("CURRENT_TIMESTAMP"),
          },
        },
        {
          uniqueKeys: {
            customer_item_unique: {
              columns: ["supplier_id", "user_id", "item_id"],
            },
          },
        }
      )

      /* ─────────────────────────────────────────────────────────────
         2. Performance indexes
         ───────────────────────────────────────────────────────────── */
      .then(() =>
        db.addIndex("customer_hidden_product", "idx_chp_user_supplier_item", [
          "user_id",
          "supplier_id",
          "item_id",
        ])
      )
      .then(() =>
        db.addIndex("customer_hidden_product", "idx_chp_supplier_item_user", [
          "supplier_id",
          "item_id",
          "user_id",
        ])
      )
  );
};

exports.down = function (db) {
  return db
    .removeIndex("customer_hidden_product", "idx_chp_supplier_item_user")
    .then(() =>
      db.removeIndex("customer_hidden_product", "idx_chp_user_supplier_item")
    )
    .then(() => db.dropTable("customer_hidden_product"));
};

exports._meta = {
  version: 1,
};
