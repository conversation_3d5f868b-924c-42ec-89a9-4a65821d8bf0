"use strict";

var dbm;
var type;
var seed;

/**
 * We receive the dbmigrate dependency from dbmigrate initially.
 * This enables us to not have to rely on NODE_PATH.
 */
exports.setup = function (options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

exports.up = function (db) {
  return db.runSql("ALTER TABLE supplier ADD PRIMARY KEY (id);").then(() =>
    db
      .addColumn("invoice", "supplier_id", {
        type: "int",
        foreignKey: {
          name: "fk_invoice_to_supplier",
          table: "supplier",
          mapping: "id",
          rules: {},
        },
      })
      .then(() =>
        db
          .runSql(
            "UPDATE invoice i SET supplier_id = s.id FROM supplier s WHERE s.name = i.supplier;"
          )
          .then(() => db.removeColumn("invoice", "supplier"))
      )
  );
};

exports.down = function (db) {
  return db
    .addColumn("invoice", "supplier", { type: "string", length: 255 })
    .then(() =>
      db
        .runSql(
          "UPDATE invoice i SET supplier = s.name FROM supplier s WHERE s.id = i.supplier_id;"
        )
        .then(() =>
          db
            .removeColumn("invoice", "supplier_id")
            .then(() =>
              db.runSql("ALTER TABLE supplier DROP CONSTRAINT supplier_pkey;")
            )
        )
    );
};

exports._meta = {
  version: 1,
};
