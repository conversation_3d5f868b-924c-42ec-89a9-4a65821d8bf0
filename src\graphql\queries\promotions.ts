import { getPromotions } from "../../services/promotionService/";
import { QueryPromotionsArgs } from "../../generated/graphql";

const promotions = async (_, { input }: QueryPromotionsArgs) => {
  const { supplier_id, pagination, sortBy, filters } = input || {};

  // Call the service function
  const { promotions: promotionsData } = await getPromotions({
    supplierId: supplier_id,
    filters,
    pagination,
    sortBy: sortBy,
  });

  return promotionsData;
};

export default promotions;
