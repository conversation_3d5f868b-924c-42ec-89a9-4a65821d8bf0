import { mockKnex, queryBuilder } from "../../../knex/mock";
import SuppliersQuery from "./suppliers";

mockKnex();

describe("Suppliers query", () => {
  let Suppliers: typeof SuppliersQuery;
  beforeAll(async () => {
    ({ default: Suppliers } = await import("./suppliers"));
  });
  const defaultSupplierIds = [15, 12, 21, 22, 23, 24, 25];

  it("correctly returns default suppliers", async () => {
    await Suppliers();
    expect(queryBuilder.whereIn).toHaveBeenCalledWith("id", defaultSupplierIds);
  });
});
