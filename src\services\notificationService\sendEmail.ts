import sgMail from "@sendgrid/mail";

export const sendEmail = async (text, to, from, replyTo, subject) => {
  sgMail.setApiKey(process.env.sendgrid_api_key);

  const msg = {
    to,
    from,
    replyTo,
    subject,
    html: text,
  };

  //ES8
  (async () => {
    try {
      await sgMail.send(msg);
    } catch (error) {
      console.error(error);

      if (error.response) {
        console.error(error.response.body);
      }
    }
  })();
};

export const sendOrderDetailsEmail = async (customerName, subtotal, email) => {
  const message = `Hi, you've received a new order!<br><br>Customer: ${customerName}<br>Total: $${Number(
    subtotal
  ).toFixed(
    2
  )}<br><br>Please check your <a href="https://app.joinattain.com/orders">Attain dashboard</a> to view and process this order.`;

  const from = "<EMAIL>";
  const replyTo = "<EMAIL>";
  const subject = "New Order Received";

  await sendEmail(message, email, from, replyTo, subject);
};
