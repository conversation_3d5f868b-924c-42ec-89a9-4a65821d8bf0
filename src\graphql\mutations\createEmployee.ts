import { MutationResolvers } from "../../generated/graphql";
import createEmployee from "../../services/accessService/createEmployee";

const createEmployeeResolver: MutationResolvers["createEmployee"] = async (
  _,
  { input }
) => {
  const {
    supplierId,
    name,
    phone,
    email,
    password,
    appAccess = false,
    dashboardAccess = false,
    roleIds = [],
    routeIds = [],
  } = input;

  try {
    const employee = await createEmployee({
      supplierId,
      name,
      phone,
      email,
      password,
      appAccess,
      dashboardAccess,
      roleIds,
      routeIds,
    });

    return employee;
  } catch (error) {
    console.error("Error in createEmployee resolver:", error);
    throw error;
  }
};

export default createEmployeeResolver;
