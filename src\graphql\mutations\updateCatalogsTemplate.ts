import knex from "../../../knex/knex";
import { MutationUpdateCatalogTemplateArgs } from "../../generated/graphql";
import {
  CatalogTemplateConfig,
  updateCatalogTemplate as updateCatalogTemplateService,
} from "../../services/catalogTemplateService";

const updateCatalogTemplate = async (
  _,
  args: MutationUpdateCatalogTemplateArgs
) => {
  const { id, supplier_id, name, config } = args.input;
  const trx = await knex.transaction();

  try {
    const result = await updateCatalogTemplateService(trx, id, {
      supplier_id,
      name,
      config: config as CatalogTemplateConfig,
    });
    await trx.commit();
    return result;
  } catch (error) {
    await trx.rollback();
    throw error;
  }
};

export default updateCatalogTemplate;
