"use strict";

var dbm;
var type;
var seed;

/**
 * We receive the dbmigrate dependency from dbmigrate initially.
 * This enables us to not have to rely on NODE_PATH.
 */
exports.setup = function (options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

exports.up = function (db) {
  return db.createTable("recommendation", {
    // Create "invoice" table
    id: { type: "int", primaryKey: true, autoIncrement: true },
    item_id: { type: "int" },
    num_store: { type: "int" },
    quantity: { type: "int" },
    is_trending: { type: "boolean" },
  });
};

exports.down = function (db) {
  return db.dropTable("recommendation");
};

exports._meta = {
  version: 1,
};
