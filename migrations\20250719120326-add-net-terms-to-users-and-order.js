"use strict";

let dbm;
let type;
let seed;

exports.setup = function (options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

exports.up = function (db) {
  return db
    .addColumn("attain_user", "net_terms_days", {
      type: "int",
      default: null,
    })
    .then(() =>
      db.addIndex("attain_user", "idx_attain_user_net_terms", [
        "net_terms_days",
      ])
    )
    .then(() =>
      db.addColumn("order_detail", "net_terms_days", {
        type: "int",
        default: null,
      })
    )
    .then(() =>
      db.addIndex("order_detail", "idx_order_detail_terms_status_date", [
        "status",
        "net_terms_days",
        "date_submitted",
      ])
    );
};

exports.down = function (db) {
  return db
    .removeIndex("order_detail", "idx_order_detail_terms_status_date")
    .then(() => db.removeColumn("order_detail", "net_terms_days"))
    .then(() => db.removeIndex("attain_user", "idx_attain_user_net_terms"))
    .then(() => db.removeColumn("attain_user", "net_terms_days"));
};

exports._meta = { version: 1 };
