import knex from "../../../knex/knex";
import { env } from "../../config/environment";
import { Item, MutationUpsertItemsArgs } from "../../generated/graphql";
import { updateOrAddItems } from "../../services/integrationService/algoliaOperations";
import { processDBItemUpdates } from "../../services/integrationService/quickBooks";
import { getEmployees } from "../../services/accessService/getEmployees";
import { getSupplierConfig } from "../../services/supplierService";
import { sendNotification } from "../../services/notificationService/sendNotification";

const upsertItems = async (_, args: MutationUpsertItemsArgs) => {
  const { items: itemsToUpsert, updateCategoryCustomPrices } = args;

  // Get current items to check stock changes
  const itemIds = itemsToUpsert
    .filter((item) => item.id)
    .map((item) => item.id);
  const currentItems =
    itemIds.length > 0
      ? await knex("item")
          .whereIn("id", itemIds)
          .select("id", "qty_on_hand", "oos", "name", "supplier")
      : [];

  // Create a map for easy lookup
  const currentItemsMap = currentItems.reduce((acc, item) => {
    acc[item.id] = item;
    return acc;
  }, {});

  // Track items that are going back in stock
  const backInStockItems = [];

  const updatedItemsToUpsert = itemsToUpsert.map(({ categoryIds, ...item }) => {
    const processedItem = {
      oos: false,
      local_item: false,
      outdated: false,
      archived: false,
      ...item,
    };

    // Check if this item is going from OOS to in stock
    if (item.id && currentItemsMap[item.id]) {
      const current = currentItemsMap[item.id];
      const wasOutOfStock = current.oos || current.qty_on_hand === 0;
      const isNowInStock = !processedItem.oos && processedItem.qty_on_hand > 0;

      if (wasOutOfStock && isNowInStock) {
        processedItem.back_in_stock_date = new Date();
        backInStockItems.push({
          id: item.id,
          name: current.name,
          qty_on_hand: processedItem.qty_on_hand,
          supplier: current.supplier,
        });
      }
    }

    return processedItem;
  });

  let categoriesToUpdate = itemsToUpsert
    .filter((i) => i.id)
    .map((item) =>
      (item.categoryIds || []).map((categoryId) => ({
        category_id: categoryId,
        item_id: item.id,
      }))
    )
    .flat();

  const trxProvider = knex.transactionProvider();
  const trx = await trxProvider();

  const items = await trx("item")
    .insert(updatedItemsToUpsert)
    .onConflict("id")
    .merge()
    .returning("*")
    .then(async (updatedItems: Item[]) => {
      const newItemsMatchedCategories = itemsToUpsert
        .filter((i) => !i.id)
        .map((i) => {
          const foundItemId = (
            updatedItems.find((u) =>
              Object.keys(i)
                .filter((k) => k !== "categoryIds")
                .every((k) => {
                  return String(u[k]) === String(i[k]);
                })
            ) ?? {}
          ).id;
          return (i.categoryIds || []).map((categoryId) => ({
            category_id: categoryId,
            item_id: foundItemId,
          }));
        })
        .flat();
      categoriesToUpdate = categoriesToUpdate.concat(newItemsMatchedCategories);
      if (categoriesToUpdate.length) {
        await trx("category_item")
          .insert(categoriesToUpdate)
          .onConflict(["category_id", "item_id"])
          .merge();
      }
      await Promise.all(
        itemsToUpsert
          .filter((item) => item.id && item.categoryIds)
          .map(async (item) => {
            await trx("category_item")
              .delete()
              .where("item_id", item.id)
              .andWhere("category_id", "not in", item.categoryIds);
          })
      );

      (await trxProvider()).commit();

      // Send notifications for back in stock items after successful commit
      if (backInStockItems.length > 0) {
        // Get supplier ID from the first item's supplier name
        const supplierName = backInStockItems[0].supplier;
        const supplier = await knex("supplier")
          .where("name", supplierName)
          .first();

        const supplierConfig = await getSupplierConfig(supplier.id);
        if (supplier && supplierConfig.send_back_in_stock_notification) {
          await sendBackInStockNotifications(backInStockItems, supplier.id);
        }
      }

      return updatedItems;
    })
    .catch(async (err) => {
      console.error("Error upserting items:", err);
      (await trxProvider()).rollback();
      throw new Error(`Error Upserting Items: ${err.message}`);
    });

  // Uncomment the following lines when ready to push to QB.
  // const updatedQbItems = await processDBItemUpdates(items);
  // items.forEach((item) => {
  //   const updatedQbItem = updatedQbItems.find((qi) => qi.Name === item.name);
  //   if (updatedQbItem) {
  //     item.qb_id = updatedQbItem.Id;
  //     item.qb_sync_token = updatedQbItem.SyncToken;
  //     item.updated_at = new Date(updatedQbItem["MetaData"]["LastUpdatedTime"]);
  //   }
  // });

  if (env.production) {
    // Update Items in Algolia Index
    await updateOrAddItems(items);
  }

  return items;
};

async function sendBackInStockNotifications(items: any[], supplierId: string) {
  try {
    // Get employees for this supplier with Sales Rep or Driver roles
    const { employees } = await getEmployees({
      filters: {
        supplierId: supplierId,
        includeArchived: false,
      },
    });

    // Filter for sales reps and drivers
    const targetEmployees = employees.filter((emp) =>
      emp.roles?.some(
        (role) => role.name === "Sales Rep" || role.name === "Driver"
      )
    );

    // For each employee, we need to find their associated user ID
    // This assumes there's a relationship between employees and users
    // You may need to adjust this based on your actual database schema
    const employeeEmails = targetEmployees
      .map((emp) => emp.email)
      .filter(Boolean);

    if (employeeEmails.length === 0) {
      console.log("No sales reps or drivers found for supplier:", supplierId);
      return;
    }

    const userIds = targetEmployees.map((targetEmployee) => targetEmployee.id);

    if (userIds.length > 0) {
      // Send individual notifications for each item
      for (const item of items) {
        const notification = {
          title: "Back In Stock",
          subtitle: "",
          body: `${item.name} is back in stock!`,
          data: {
            type: "back_in_stock",
            itemId: item.id,
            itemName: item.name,
            supplierId: supplierId,
          },
        };

        await sendNotification(userIds, notification);
        console.log(
          `Sent back in stock notification for "${item.name}" to ${userIds.length} users`
        );
      }
    } else {
      console.log(
        "No users found for the sales reps/drivers of supplier:",
        supplierId
      );
    }
  } catch (error) {
    console.error("Error sending back in stock notifications:", error);
    // Don't throw - we don't want to fail the entire operation if notifications fail
  }
}

export default upsertItems;
