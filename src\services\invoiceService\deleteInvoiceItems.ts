import { GraphQLError } from "graphql";
import knex from "../../../knex/knex";
import { InvoiceItemInput } from "../../generated/graphql";
import { Knex } from "knex";
const deleteInvoiceItems = async (
  invoiceId: string | undefined | null,
  items: InvoiceItemInput[],
  notInList: boolean,
  trxProvider?: Knex.TransactionProvider | undefined | null
) => {
  if (notInList && (!invoiceId || invoiceId.length === 0)) {
    throw new GraphQLError("When specifying notInList, invoiceId is required", {
      extensions: { code: "BAD_USER_INPUT" },
    });
  }
  const trxOrQuery = trxProvider
    ? (await trxProvider())("invoice_item")
    : knex("invoice_item");
  const deleteQuery = notInList
    ? trxOrQuery
        .delete()
        .where(
          "id",
          "not in",
          items.map((item) => item.id)
        )
        .andWhere("invoice_id", invoiceId)
    : trxOrQuery.delete().where(
        "id",
        "in",
        items.map((item) => item.id)
      );
  await deleteQuery;
};

export default deleteInvoiceItems;
