import { QueryAllCustomPricesV2Args } from "../../../generated/graphql";
import { getUserCustomPrices, getUsers } from "../../../services/userService";

const AllCustomPricesV2 = async (_, args: QueryAllCustomPricesV2Args) => {
  const { supplierId } = args.allCustomPricesInput;

  const { users } = await getUsers({ supplierId, pagination: { limit: 1000 } });

  const userCustomPricesResult = [];
  for (const user of users) {
    const customPrices = await getUserCustomPrices(user);
    if (customPrices && customPrices.length > 0) {
      userCustomPricesResult.push({
        userId: user.id,
        prices: customPrices,
      });
    }
  }

  return {
    userCustomPrices: userCustomPricesResult,
    totalCount: users.length,
  };
};
export default AllCustomPricesV2;
