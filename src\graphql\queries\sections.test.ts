import SectionsQuery from "./sections";
import getItemsBySection from "../../services/itemService/getItemsBySection";

jest.mock("../../services/getItemsBySection");

describe("Sections query", () => {
  let Sections: typeof SectionsQuery;
  beforeAll(async () => {
    ({ default: Sections } = await import("./sections"));
  });
  const getSectionsInput = {
    userId: "1234",
    pagination: { offset: 2, limit: 10 },
  };

  it("correctly returns sections for a given userId", async () => {
    const result = await Sections(undefined, { getSectionsInput });
    expect(getItemsBySection).toHaveBeenCalledTimes(2);
    expect(getItemsBySection).toHaveBeenNthCalledWith(
      1,
      getSectionsInput.userId,
      "order_again",
      getSectionsInput.pagination.offset,
      getSectionsInput.pagination.limit
    );
    expect(getItemsBySection).toHaveBeenNthCalledWith(
      2,
      getSectionsInput.userId,
      "top_sellers",
      getSectionsInput.pagination.offset,
      getSectionsInput.pagination.limit
    );
    expect(result.length).toEqual(2);
    expect(result.some((i) => i.value === "order_again")).toBeTruthy();
    expect(result.some((i) => i.value === "top_sellers")).toBeTruthy();
  });
});
