import {
  CreateGoalInput,
  Employee,
  Goal,
  GoalAssignment,
  GoalPeriod,
  GoalStatus,
  GoalType,
  UpdateGoalInput,
} from "../../../generated/graphql";

const queryBuilder: any = {
  first: jest.fn(),
  returning: jest.fn(),
  select: jest.fn().mockReturnThis(),
  insert: jest.fn().mockReturnThis(),
  update: jest.fn().mockReturnThis(),
  delete: jest.fn().mockReturnThis(),
  del: jest.fn().mockReturnThis(),
  where: jest.fn().mockReturnThis(),
  whereIn: jest.fn().mockReturnThis(),
  whereNot: jest.fn().mockReturnThis(),
  whereRaw: jest.fn().mockReturnThis(),
  leftJoin: jest.fn().mockReturnThis(),
  orderBy: jest.fn().mockReturnThis(),
  offset: jest.fn().mockReturnThis(),
  limit: jest.fn().mockReturnThis(),
  sum: jest.fn().mockReturnThis(),
  count: jest.fn().mockReturnThis(),
  countDistinct: jest.fn().mockReturnThis(),
  clone: jest.fn().mockReturnThis(),
  then: jest.fn((resolve) => resolve([])),
};

const knexMock = jest.fn(() => queryBuilder);

Object.assign(knexMock, {
  transaction: jest.fn(async (callback) => {
    const trx = jest.fn(() => queryBuilder);
    Object.assign(trx, { commit: jest.fn(), rollback: jest.fn() });
    return callback(trx);
  }),
});

jest.mock("../../../../knex/knex", () => ({
  __esModule: true,
  default: knexMock,
}));

describe("Goal Service", () => {
  let goalService: typeof import("../index");

  const mockEmployee: Employee = {
    id: "201",
    name: "John Doe",
    email: "<EMAIL>",
    app_access: true,
    archived: false,
    created_at: new Date().toISOString(),
    dashboard_access: true,
    updated_at: new Date().toISOString(),
  };
  const mockAssignment: GoalAssignment = {
    id: "101",
    goal_id: "1",
    employee_id: "201",
    target_amount: 25000,
    current_progress: 0,
    percentage_complete: 0,
    employee: mockEmployee,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };
  const mockGoal: Goal = {
    id: "1",
    supplier_id: "10",
    name: "Q3 Sales",
    type: GoalType.SalesAmount,
    period: GoalPeriod.Monthly,
    target_amount: 50000,
    start_date: "2025-07-01",
    is_active: true,
    assignments: [mockAssignment],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    status: GoalStatus.Active,
    available_periods: [],
  };

  beforeAll(async () => {
    goalService = await import("../index");
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("createGoal", () => {
    it("should create a new goal and its assignments", async () => {
      const input: CreateGoalInput = {
        supplier_id: "10",
        name: "New Goal",
        type: GoalType.SalesAmount,
        period: GoalPeriod.Weekly,
        target_amount: 10000,
        start_date: "2025-08-01",
        assignments: [{ employee_id: "201", target_amount: 5000 }],
      };
      const createdGoalRecord = { ...input, id: 2 };

      queryBuilder.returning.mockResolvedValueOnce([createdGoalRecord]);
      queryBuilder.first.mockResolvedValueOnce(createdGoalRecord);
      queryBuilder.select.mockResolvedValueOnce([mockAssignment]);

      const result = await goalService.createGoal(input);

      expect(result.id).toBe("2");
      expect(queryBuilder.insert).toHaveBeenCalledTimes(2);
    });
  });

  describe("updateGoal", () => {
    it("should update goal properties and replace assignments", async () => {
      const input: UpdateGoalInput = { id: "1", name: "Updated Name" };

      queryBuilder.first.mockResolvedValueOnce({
        ...mockGoal,
        name: input.name,
      });
      queryBuilder.select.mockResolvedValueOnce([]);

      const result = await goalService.updateGoal(input);

      expect(result.name).toBe("Updated Name");
      expect(queryBuilder.update).toHaveBeenCalledTimes(1);
    });
  });

  describe("deleteGoal", () => {
    it("should delete assignments and the goal, returning true on success", async () => {
      queryBuilder.delete.mockResolvedValue(1);
      const result = await goalService.deleteGoal("1");
      expect(result).toBe(true);
    });

    it("should return false if the goal to delete is not found", async () => {
      queryBuilder.delete.mockResolvedValueOnce(1).mockResolvedValueOnce(0);
      const result = await goalService.deleteGoal("999");
      expect(result).toBe(false);
    });
  });

  describe("getGoalById", () => {
    it("should fetch a single goal with its assignments", async () => {
      queryBuilder.first.mockResolvedValueOnce(mockGoal);
      queryBuilder.select.mockResolvedValueOnce(mockGoal.assignments);

      const result = await goalService.getGoalById("1");

      expect(result.id).toBe("1");
      expect(result.assignments.length).toBe(1);
    });

    it("should throw an error if the goal is not found", async () => {
      queryBuilder.first.mockResolvedValueOnce(null);
      await expect(goalService.getGoalById("999")).rejects.toThrow(
        "Goal with id 999 not found"
      );
    });
  });

  describe("getGoalStatus", () => {
    it("should return 'Past' if the end_date is in the past", () => {
      const status = goalService.getGoalStatus(
        { ...mockGoal, end_date: "2024-01-01" },
        []
      );
      expect(status.status).toBe(GoalStatus.Past);
    });

    it("should return 'Completed' if any assignment is 100% complete", () => {
      const status = goalService.getGoalStatus(mockGoal, [
        { ...mockAssignment, percentage_complete: 100 },
      ]);
      expect(status.status).toBe(GoalStatus.Completed);
    });

    it("should return 'On Track' for active goals with >= 80% average progress", () => {
      const status = goalService.getGoalStatus(mockGoal, [
        { ...mockAssignment, percentage_complete: 85 },
      ]);
      expect(status.label).toBe("On Track");
    });
  });

  describe("getAvailablePeriods", () => {
    it("should generate a list of available weekly periods", async () => {
      const result = goalService.getAvailablePeriods({
        start_date: "2025-06-23",
        end_date: null,
        period: "weekly",
      });
      expect(result).toHaveLength(4);
      expect(result[0].value).toBe("2025-07-14");
    });
  });
});
