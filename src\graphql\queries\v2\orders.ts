import { QueryOrdersV2Args } from "../../../generated/graphql";
import { getOrders } from "../../../services/orderService/orderService";

const OrdersV2 = async (_, args: QueryOrdersV2Args) => {
  const { supplierId, filters, pagination, sortBy } = args.ordersInput;

  const result = await getOrders({
    supplierId,
    filters,
    pagination,
    sortBy,
  });
  return result;
};

export default OrdersV2;
