import { initializeApp } from "firebase/app";
import { getAuth, createUserWithEmailAndPassword } from "firebase/auth";
import { getFirestore, doc, setDoc } from "firebase/firestore";
import { parse } from "csv-parse";
import * as fs from "fs";

/*
 *  This script is for creating app accounts for users
 *  from a CSV file.
 *
 *  Useage: npx ts-node scripts/tools/createUsersFromCsv.ts <csv_file_path>
 */

const firebaseConfig = {
  apiKey: "AIzaSyDazkUMxMUnWRpp19dAH31_TXJ3xrQL3RE",
  authDomain: "attain-23279.firebaseapp.com",
  projectId: "attain-23279",
  storageBucket: "attain-23279.appspot.com",
  messagingSenderId: "************",
  appId: "1:************:web:a92cd9a5294dc755f38010",
  measurementId: "G-H6QGK63XJL",
};

const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

const createAccount = async (userId, login, password) => {
  const email = login + "@joinattain.com";
  try {
    const userCredential = await createUserWithEmailAndPassword(
      auth,
      email,
      password
    );

    await setDoc(doc(db, "users", userCredential.user.uid), {
      userId: userId.toString(),
    });
  } catch (error) {
    console.error(
      "User Creation Firebase Error",
      error.code,
      error.message,
      email,
      userId
    );
    return [error.code, error.message];
  }
};

const createUsersFromCsv = async () => {
  const csvFilePath = process.argv[2]; // Get CSV file path from command line argument
  if (!csvFilePath) {
    console.error("Please provide a CSV file path as an argument");
    process.exit(1);
  }

  const parser = parse({
    delimiter: ",",
    columns: true, // Assumes first line contains headers
    skip_empty_lines: true,
  });

  const records = [];

  // Read and parse the CSV file
  fs.createReadStream(csvFilePath)
    .pipe(parser)
    .on("data", (record) => records.push(record))
    .on("end", async () => {
      console.log(`Processing ${records.length} accounts...`);

      for (const record of records) {
        const { login, userID } = record;
        if (!login || !userID) {
          console.error("Invalid record:", record);
          continue;
        }

        console.log(`Creating account for ${login} (ID: ${userID})`);
        await createAccount(userID, login, "cgsnacks");
      }

      console.log("Finished processing all accounts");
      process.exit(0);
    })
    .on("error", (error) => {
      console.error("Error processing CSV:", error);
      process.exit(1);
    });
};
