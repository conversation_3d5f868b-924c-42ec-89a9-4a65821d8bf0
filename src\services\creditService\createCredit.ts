import { <PERSON><PERSON> } from "knex";
import {
  CreateCreditInput,
  CreditStatus,
  Invoice,
  Credit,
  CreditItemInput,
} from "../../generated/graphql";
import { getItem } from "../itemService/itemService";
import { getOrders } from "../orderService/orderService";
import { CreditRow, CreditItemRow, InsertableCreditItemRow } from "./db.types";
import getCredits from "./getCredits";

/**
 * Validates the credit items against the invoice items.
 * @param invoice - The invoice to validate the credit items against.
 * @param credit_items - The credit items to validate.
 * @returns The validated credit items.
 */
export function validateCreditItems(
  invoice: Invoice,
  credit_items: CreateCreditInput["credit_items"]
): CreditItemInput[] {
  if (!invoice) {
    throw new Error(`Invoice not found`);
  }

  if (!invoice.invoiceItems || invoice.invoiceItems.length === 0) {
    throw new Error(`No items found in invoice ${invoice.id}`);
  }

  // Validate each credit item
  const validatedItems: CreditItemInput[] = [];
  for (const creditItem of credit_items) {
    const invoiceItem = invoice.invoiceItems.find(
      (item) => item.item_id.toString() === creditItem.item_id.toString()
    );
    if (!invoiceItem) {
      throw new Error(
        `Item ${creditItem.item_id} not found in invoice ${invoice.id}`
      );
    }
    // TODO: we should consider all credits on that invoice. Right now you can add
    // two credits for the same item and exceed the quantity in the invoice.
    if (creditItem.quantity > invoiceItem.quantity) {
      throw new Error(
        `Credit quantity (${creditItem.quantity}) cannot exceed invoice quantity (${invoiceItem.quantity}) for item ${creditItem.item_id}`
      );
    }
    validatedItems.push(creditItem);
  }

  return validatedItems;
}

/**
 * Populates the input credit items with the invoice items data.
 * @param validatedItems - The validated credit items.
 * @param invoice - The invoice to populate the credit items with.
 * @returns The populated credit items.
 */
export async function populateCreditItems(
  validatedItems: CreditItemInput[],
  invoice: Invoice
): Promise<InsertableCreditItemRow[]> {
  return Promise.all(
    validatedItems.map(async (item) => {
      const invoiceItem = invoice.invoiceItems.find(
        (i) => i.item_id.toString() === item.item_id.toString()
      );
      const itemDetails = await getItem(item.item_id.toString());
      return {
        item_id: item.item_id,
        quantity: item.quantity,
        reason: item.reason,
        note: item.note || null,
        unit_price: invoiceItem?.price || 0,
        total_price: invoiceItem?.price * item.quantity,
        invoice_item_id: parseInt(invoiceItem?.id),
        item_snapshot: itemDetails,
      };
    })
  );
}

/**
 * Calculates the total of the credit items.
 * TODO: Move this to a financial utility file.
 * @param credit_items - The credit items to calculate the total of.
 * @returns The total of the credit items.
 */
export function calculateCreditTotal(
  credit_items: InsertableCreditItemRow[]
): number {
  return credit_items.reduce((sum, item) => sum + item.total_price, 0);
}

/**
 * Creates a credit record and its items.
 * @param createCreditInput - The input to create the credit with.
 * @returns The created credit.
 */
async function createCredit(
  trx: Knex.Transaction,
  createCreditInput: CreateCreditInput
): Promise<Credit> {
  const {
    invoice_id,
    user_id,
    supplier_id,
    credit_items,
    status = CreditStatus.Pending,
    images = [],
  } = createCreditInput;

  // Get the order and invoice information
  const orders = await getOrders({
    filters: {
      invoiceIds: [invoice_id.toString()],
    },
    supplierId: supplier_id.toString(),
  });

  const order = orders?.orders[0];
  if (!order) {
    throw new Error(`Order not found for invoice ${invoice_id}`);
  }

  const invoice = order.invoice as Invoice;
  if (!invoice) {
    throw new Error(`Invoice not found with ID ${invoice_id}`);
  }

  // Validate credit items against invoice items
  const validatedItems = validateCreditItems(invoice, credit_items);
  const populatedItems = await populateCreditItems(validatedItems, invoice);

  // Get the current highest credit number for this supplier
  const maxCreditNumber = await trx<CreditRow>("credit")
    .where("supplier_id", supplier_id)
    .orderBy("credit_number", "desc")
    .first()
    .then((row) => row?.credit_number || 0);

  const nextCreditNumber = maxCreditNumber ? maxCreditNumber + 1 : 1;
  const total = calculateCreditTotal(populatedItems);
  const now = new Date();

  // Insert the credit record
  const [credit] = await trx<CreditRow>("credit")
    .insert({
      total,
      invoice_id: invoice_id,
      order_id: parseInt(order.id),
      order_number: order.order_number,
      user_id: user_id,
      supplier_id: supplier_id,
      credit_number: nextCreditNumber,
      status: status as CreditStatus,
      created_at: now,
      updated_at: now,
      cash_amount: 0,
      archived: false,
    })
    .returning("*")
    .catch((e) => {
      console.error(e);
      throw new Error(
        `Failed to create credit record for invoice ${invoice_id}`
      );
    });

  // Insert all credit items
  await trx<CreditItemRow>("credit_item")
    .insert(
      populatedItems.map((item) => ({
        credit_id: credit.id,
        ...item,
      }))
    )
    .returning("*")
    .catch(() => {
      console.error(`Failed to create credit items for credit ${credit.id}`);
      throw new Error(`Failed to create credit items for credit ${credit.id}`);
    });

  // Insert credit images if provided
  if (images.length > 0) {
    await trx("credit_image")
      .insert(
        images.map((image_url) => ({
          credit_id: credit.id,
          url: image_url,
          created_at: now,
        }))
      )
      .catch((e) => {
        console.error(
          `Failed to create credit images for credit ${credit.id}`,
          e
        );
        throw new Error(
          `Failed to create credit images for credit ${credit.id}`
        );
      });
  }

  const createdCredit = await getCredits(
    {
      filters: {
        ids: [credit.id],
      },
      supplier_id: supplier_id,
    },
    trx
  );

  if (!createdCredit?.credits?.[0]) {
    console.error(`Failed to fetch created credit ${credit.id}`);
    throw new Error(`Failed to fetch created credit ${credit.id}`);
  }

  return createdCredit.credits[0];
}

export default createCredit;
