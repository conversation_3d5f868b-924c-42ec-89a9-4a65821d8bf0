import knex from "../../../knex/knex";
import { MutationUpdateUserArgs, User } from "../../generated/graphql";
import { updateUser as updateUserFn } from "../../services/userService/userService";

const updateUser = async (_, args: MutationUpdateUserArgs) => {
  const { user } = args;

  const txn = await knex.transaction();
  const updatedUser: User = await updateUserFn(txn, user);

  try {
    txn.commit();
  } catch (err) {
    txn.rollback();
    console.log("Error updating Users:", err);
    throw new Error(`Error updating Users: ${err.message}`);
  }

  //const userSuppliers = await getUsersSuppliers(users);
  //if (usersToUpdate[0].approved === true) {
  //  const notification = {
  //    title: "Account Approved",
  //    subtitle: ``,
  //    body: `Your account has been approved by ${userSuppliers[0].suppliers[0].name} and is now active. Start ordering now!`,
  //    data: {},
  //  };
  //  await sendNotification([usersToUpdate[0].id], notification);/
  //  const approvalRequestTextMessage = `Your account has been approved by ${userSuppliers[0].suppliers[0].name} and is now active. Start ordering now!`;
  //  await sendTextMessage(approvalRequestTextMessage, users[0].phone_number)
  //    .then((message) => {
  //      console.log(`Approval Request Message sent with SID: ${message.sid}`);
  //    })
  //    .catch((error) => {
  //      console.error(`Error sending approval request message: ${error}`);
  //    });
  //}
  //
  return updatedUser;
};

export default updateUser;
