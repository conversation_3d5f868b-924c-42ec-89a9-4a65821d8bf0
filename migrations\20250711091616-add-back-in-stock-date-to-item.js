"use strict";

let dbm;
let type;
let seed;

exports.setup = function (options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

/**
 * Migration: Add back_in_stock_date column to item table
 *
 * This migration adds a timestamp column to track when items go back in stock
 * for back-in-stock notification functionality.
 */
exports.up = function (db) {
  console.log("🚀 Adding back_in_stock_date column to item table...");

  return db
    .addColumn("item", "back_in_stock_date", {
      type: "timestamp",
      notNull: false,
    })
    .then(() =>
      db.addIndex("item", "idx_item_back_in_stock_date", ["back_in_stock_date"])
    )
    .then(() => {
      console.log("✅ back_in_stock_date column and index added successfully!");
    });
};

exports.down = function (db) {
  console.log("🔄 Rolling back back_in_stock_date migration...");
  return db
    .removeIndex("item", "idx_item_back_in_stock_date")
    .then(() => db.removeColumn("item", "back_in_stock_date"))
    .then(() => {
      console.log("✅ back_in_stock_date migration rollback completed!");
    });
};

exports._meta = {
  version: 1,
};
