import { QueryItemsV2Args } from "../../../generated/graphql";
import { getItems } from "../../../services/itemService/itemService";
import { populateItemsUOMs } from "../../../services/itemService/uomService";

const ItemsV2 = async (_, args: QueryItemsV2Args) => {
  const { supplierId, userId, filters, pagination, sortBy } = args.itemsInput;

  const result = await getItems({
    supplierId,
    userId,
    filters,
    pagination,
    sortBy,
  });

  // Populate UOM data for items
  if (result.items.length > 0 && supplierId) {
    await populateItemsUOMs(result.items, supplierId);
  }

  return result;
};

export default ItemsV2;
