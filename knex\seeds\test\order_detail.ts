import { Knex } from "knex";

export async function seed(knex: Knex): Promise<void> {
  // Deletes ALL existing entries
  await knex("order_detail").del();

  // Inserts seed entries
  await knex("order_detail").insert([
    {
      user_id: 1,
      subtotal: 12.34,
      status: "test",
      date_submitted: "2023-01-01 00:00:00",
      order_name: "order 1",
      discount: 2.5555,
    },
    {
      user_id: 2,
      subtotal: 22.22,
      status: "test",
      date_submitted: "2023-06-30 00:00:00",
      order_name: "order dos",
    },
  ]);
}
