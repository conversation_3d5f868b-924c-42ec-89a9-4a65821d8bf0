import type { Knex } from "knex";
import dotenv from "dotenv";
dotenv.config();

const config: { [key: string]: Knex.Config } = {
  test: {
    client: "pg",
    connection: {
      host: "localhost",
      port: 5432,
      database: "testdb",
      charset: "utf8",
      user: "testuser",
      password: "testpass",
    },
    seeds: {
      directory: __dirname + "/knex/seeds/test",
    },
  },

  development: {
    client: "pg",
    connection: {
      host: "localhost",
      port: 5432,
      database: "test",
      charset: "utf8",
    },
    seeds: {
      directory: __dirname + "/knex/seeds/dev",
    },
  },

  staging: {
    client: "pg",
    connection: {
      connectionString: process.env.DATABASE_URL,
      ssl: {
        rejectUnauthorized: false,
      },
    },
    pool: {
      min: 2,
      max: 10,
    },
    seeds: {
      directory: __dirname + "/knex/seeds/prod",
    },
  },

  production: {
    client: "pg",
    connection: {
      connectionString: process.env.DATABASE_URL,
      ssl: {
        rejectUnauthorized: false,
      },
    },
    pool: {
      min: 2,
      max: 10,
    },
    seeds: {
      directory: __dirname + "/knex/seeds/prod",
    },
  },
};

export default config;
