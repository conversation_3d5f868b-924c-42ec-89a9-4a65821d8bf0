import knex from "../../../knex/knex";
import { Recommendation } from "../../generated/graphql";

export async function getRecommendedItemsForStore(
  storeId: number | string,
  limit: number = null,
  hiddenProductIds: string[] = []
): Promise<Recommendation[]> {
  const recommendationsQuery = knex
    .select("*")
    .from("recommendation")
    .where("user_id", storeId)
    .whereNotIn("item_id", hiddenProductIds)
    .orderBy([
      { column: "num_store", order: "desc" },
      { column: "quantity", order: "desc" },
    ]);
  const recommendations = await (limit
    ? recommendationsQuery.limit(limit)
    : recommendationsQuery);

  return recommendations;
}
