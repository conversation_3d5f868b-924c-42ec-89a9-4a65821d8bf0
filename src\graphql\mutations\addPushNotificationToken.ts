import knex from "../../../knex/knex";
import {
  MutationAddPushNotificationTokenArgs,
  PushNotificationToken,
} from "../../generated/graphql";

const addPushNotificationToken = async (
  _,
  args: MutationAddPushNotificationTokenArgs
) => {
  const { user_id: userId, token } = args.addPushNotificationTokenInput;
  const returnedTokens: PushNotificationToken[] = await knex(
    "push_notification_tokens"
  )
    .insert({ user_id: userId, token })
    .onConflict("token")
    .merge()
    .returning("*");
  console.log(
    `Push Notification added with token '${token}' for user ${userId}`
  );
  return returnedTokens[0];
};

export default addPushNotificationToken;
