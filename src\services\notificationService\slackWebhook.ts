import axios from "axios";
import mime from "mime-types";
export const postTextViaSlackWebhook = async (
  webhookUrl: string,
  text: string
) => {
  try {
    const res = await axios.post(
      webhookUrl,
      { text },
      {
        headers: {
          accept: mime.types.json,
          "content-type": mime.types.json,
        },
      }
    );
    return res;
  } catch (error) {
    console.error(`Error posting to Slack: ${error}`);
    return null;
  }
};
