export interface CatalogTemplateDisplayOptions {
  show_sku: boolean;
  show_case_size: boolean;
  show_upc: boolean;
  show_stock: boolean;
}

export interface CatalogTemplateConfig {
  category: string;
  sort_by: "PRODUCT_NAME" | "CATEGORY" | "SKU";
  title: string;
  subtitle: string;
  company_info: string;
  pricing_enabled: boolean;
  display_options: CatalogTemplateDisplayOptions;
}

export interface CatalogTemplateRow {
  id: string;
  supplier_id: string;
  name: string;
  config: string | CatalogTemplateConfig;
  pdf_path?: string;
  created_by: number | null;
  created_at: Date;
  updated_at: Date;
  archived_at: Date | null;
}

export type InsertableCatalogRow = Omit<
  CatalogTemplateRow,
  "id" | "created_at" | "updated_at" | "archived_at" | "created_by"
> & { archived_at?: Date | null };
