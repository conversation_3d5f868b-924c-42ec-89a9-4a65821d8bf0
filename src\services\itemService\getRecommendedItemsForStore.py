import pandas as pd
from sklearn.metrics.pairwise import cosine_similarity
import psycopg2
from urllib.parse import urlparse
from datetime import datetime, timedelta


p = urlparse('postgres://u3d7j5ntplut92:<EMAIL>:5432/d5q5ie21b3ce27')

pg_connection_dict = {
            'dbname': p.path[1:],
            'user': p.username,
            'password': p.password,
            'port': p.port,
            'host': p.hostname
        }

conn = psycopg2.connect(**pg_connection_dict)

def save_recommendations(recommendations):
    cursor = conn.cursor()
    insert_query = "INSERT INTO recommendation (item_id, num_store, quantity, is_trending, user_id) VALUES (%s, %s, %s, %s, %s)"
    cursor.executemany(insert_query, recommendations)
    conn.commit()
    cursor.close()

def delete_previous_recommendations():
    cursor = conn.cursor()
    cursor.execute('DELETE FROM RECOMMENDATION')
    conn.commit()
    cursor.close()
# Data Loading and Preprocessing Functions
def load_data():
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM ITEM")
    item = cursor.fetchall()
    item_col = [elt[0] for elt in cursor.description]
    items_df = pd.DataFrame(item, columns=item_col)

    cursor.execute("SELECT * FROM ORDER_DETAIL")
    order_detail = cursor.fetchall()
    order_detail_col = [elt[0] for elt in cursor.description]
    order_detail_df = pd.DataFrame(order_detail, columns=order_detail_col)

    cursor.execute("SELECT * FROM ORDER_ITEM")
    order_item = cursor.fetchall()
    order_item_col = [elt[0] for elt in cursor.description]
    order_item_df = pd.DataFrame(order_item, columns=order_item_col)

    cursor.execute("SELECT * FROM ATTAIN_USER")
    attain_user = cursor.fetchall()
    attain_user_col = [elt[0] for elt in cursor.description]
    attain_user_df = pd.DataFrame(attain_user, columns=attain_user_col)

    cursor.close()
    

    return items_df, order_detail_df, order_item_df, attain_user_df

def create_user_item_matrix(order_detail_df, order_item_df):
    merged_order_data = pd.merge(order_detail_df, order_item_df, left_on="id", right_on="order_id")
    user_item_matrix = merged_order_data.pivot_table(index='user_id', columns='item_id', values='quantity', aggfunc='sum', fill_value=0)
    return user_item_matrix

def optimize_user_item_matrix(user_item_matrix, min_interactions=5):
    user_ids = user_item_matrix.index[user_item_matrix.sum(axis=1) > min_interactions]
    item_ids = user_item_matrix.columns[user_item_matrix.sum(axis=0) > min_interactions]
    optimized_matrix = user_item_matrix.loc[user_ids, item_ids]
    return optimized_matrix

# Collaborative Filtering Functions
def compute_item_similarity(user_item_matrix):
    item_similarity = cosine_similarity(user_item_matrix.T)
    item_similarity_df = pd.DataFrame(item_similarity, index=user_item_matrix.columns, columns=user_item_matrix.columns)
    return item_similarity_df

def compute_user_similarity(user_item_matrix):
    user_similarity = cosine_similarity(user_item_matrix)
    user_similarity_df = pd.DataFrame(user_similarity, index=user_item_matrix.index, columns=user_item_matrix.index)
    return user_similarity_df

# Recommendation Functions
def recommend_for_store(store_id, user_item_matrix, item_similarity_df, items_df, num_recommendations=5):
    store_data = user_item_matrix.loc[store_id]
    bought_items = store_data[store_data > 0].index.tolist()
    item_scores = item_similarity_df[bought_items].mean(axis=1).sort_values(ascending=False)
    recommended_items = item_scores[~item_scores.index.isin(bought_items)]
    top_recommended_items = recommended_items.head(num_recommendations).index.tolist()
    top_item_ids= items_df[items_df['id'].isin(top_recommended_items)]['id'].tolist()
    return top_item_ids

def similar_stores_to_optimized_v2(store_id, user_similarity_df, optimized_matrix, num_similar_stores=5):
    similar_stores = user_similarity_df[store_id].sort_values(ascending=False)
    similar_stores = similar_stores[similar_stores.index.isin(optimized_matrix.index)]
    top_similar_stores = similar_stores.drop(store_id).head(num_similar_stores).index.tolist()
    return top_similar_stores

def generate_rationales_for_recommendations_v2(store_id, recommended_items, user_item_matrix, items_df, similar_store_ids):
    item_stats = []
    for item_id in recommended_items:
        num_similar_stores_bought = len(user_item_matrix.loc[similar_store_ids][user_item_matrix[item_id] > 0])
        total_qty_bought_by_similar_stores = user_item_matrix.loc[similar_store_ids, item_id].sum()
        is_trending = total_qty_bought_by_similar_stores > 5
        item_stats.append(
            (item_id,
             num_similar_stores_bought,
             total_qty_bought_by_similar_stores,
             False))
    return item_stats

def store_recommendations_main_optimized_v3(items_df, order_detail_df, order_item_df, attain_user_df, num_recommendations=5, num_similar_stores=5):
    user_item_matrix = create_user_item_matrix(order_detail_df, order_item_df)
    optimized_matrix = optimize_user_item_matrix(user_item_matrix)
    item_similarity_df = compute_item_similarity(optimized_matrix)
    user_similarity_df = compute_user_similarity(optimized_matrix)

    attain_user_ids = attain_user_df['id'].to_numpy()
    recommended_items_to_insert = []

    for user_id in attain_user_ids:
        modified_id = user_id
        if modified_id not in optimized_matrix.index:
            modified_id = 1
        recommended_items_list = recommend_for_store(modified_id, optimized_matrix, item_similarity_df, items_df, num_recommendations)
        similar_store_ids = similar_stores_to_optimized_v2(modified_id, user_similarity_df, optimized_matrix, num_similar_stores)
        rationales_list = generate_rationales_for_recommendations_v2(modified_id, recommended_items_list, optimized_matrix, items_df, similar_store_ids)
        temp = [(int(item[0]), int(item[1]), int(item[2]), bool(item[3]), int(user_id)) for item in rationales_list]
        recommended_items_to_insert.extend(temp)

    save_recommendations(recommended_items_to_insert)




# Load the datasets (assuming you've loaded them into Pandas DataFrames)
# items_df, order_detail_df, order_item_df, users_df


def trending_items(items_df, order_detail_df, order_item_df, attain_user_df):
    # Convert date columns to datetime format
    order_detail_df['date_submitted'] = pd.to_datetime(order_detail_df['date_submitted'])
    attain_user_df['created_at'] = pd.to_datetime(attain_user_df['created_at'])

    # Define time frames: last 30 days and the 30 days before that
    end_date = datetime.now()
    start_date_last_30 = end_date - timedelta(days=30)
    start_date_prev_30 = start_date_last_30 - timedelta(days=30)

    # Filter out new users who joined in the last 30 days
    existing_users = attain_user_df[attain_user_df['created_at'] < start_date_last_30]['id'].tolist()

    # Filter order details for existing users only
    order_detail_filtered = order_detail_df[order_detail_df['user_id'].isin(existing_users)]

    # Filter data for last 30 days and previous 30 days
    orders_last_30 = order_detail_filtered[(order_detail_filtered['date_submitted'] > start_date_last_30) & (order_detail_filtered['date_submitted'] <= end_date)]
    orders_prev_30 = order_detail_filtered[(order_detail_filtered['date_submitted'] > start_date_prev_30) & (order_detail_filtered['date_submitted'] <= start_date_last_30)]

    # Merge with order_item to get item-level details
    order_item_last_30 = pd.merge(orders_last_30, order_item_df, left_on='id', right_on='order_id')
    order_item_prev_30 = pd.merge(orders_prev_30, order_item_df, left_on='id', right_on='order_id')

    # Group by item and user to get the sum of quantity
    item_user_last_30 = order_item_last_30.groupby(['item_id', 'user_id'])['quantity'].sum().reset_index()
    item_user_prev_30 = order_item_prev_30.groupby(['item_id', 'user_id'])['quantity'].sum().reset_index()

    # Identify trending items with adjusted criteria
    trending_items = []
    for item_id in item_user_last_30['item_id'].unique():
        users_last_30 = item_user_last_30[item_user_last_30['item_id'] == item_id]
        users_prev_30 = item_user_prev_30[item_user_prev_30['item_id'] == item_id]
        
        if len(users_last_30['user_id'].unique()) < 2:
            continue
        
        trending_up = True
        count = 0
        total_increase = 0
 
        for user_id in users_last_30['user_id'].unique():
            qty_last_30 = users_last_30[users_last_30['user_id'] == user_id]['quantity'].sum()
            qty_prev_30 = users_prev_30[users_prev_30['user_id'] == user_id]['quantity'].sum() if user_id in users_prev_30['user_id'].values else 0
            count += qty_last_30
            if qty_last_30 <= qty_prev_30:  # Adjusted criteria
                trending_up = False
                break
            total_increase += (qty_last_30 - qty_prev_30)
        if trending_up:
            trending_items.append((item_id, len(users_last_30), count, True, total_increase))

    sorted_trending_items = sorted(trending_items, key=lambda x: x[4], reverse=True)
    temp = [(int(item[0]), int(item[1]), int(item[2]), bool(item[3]), None) for item in sorted_trending_items]

    save_recommendations(temp)


    # Your trending items are now stored in trending_item_names



def getRecommendations():
    delete_previous_recommendations()
    items_df, order_detail_df, order_item_df, attain_user_df = load_data()
    store_recommendations_main_optimized_v3(items_df, order_detail_df, order_item_df, attain_user_df, num_recommendations=15, num_similar_stores=5)
    trending_items(items_df, order_detail_df, order_item_df, attain_user_df)
    conn.close()

getRecommendations()




