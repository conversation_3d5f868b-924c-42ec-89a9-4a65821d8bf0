import { mockKnex, queryBuilder } from "../../../knex/mock";
import OrderStatusesQuery from "./orderStatuses";

mockKnex();

describe("Order Statuses query", () => {
  let OrderStatuses: typeof OrderStatusesQuery;
  beforeAll(async () => {
    ({ default: OrderStatuses } = await import("./orderStatuses"));
  });

  it("correctly retrieves and formats dates for an order's status", async () => {
    const date = new Date();
    const orderStatus = {
      name: "supplier",
      submission_date: date,
      delivering_date: date,
      delivery_date: date,
    };
    queryBuilder.innerJoin.mockResolvedValueOnce([orderStatus]);
    const orderId = "1234";

    const results = await OrderStatuses(undefined, { orderId });

    expect(queryBuilder.where).toHaveBeenCalledWith("o.order_id", orderId);
    expect(results).toEqual([
      {
        name: orderStatus.name,
        submission_date: new Date(date).getTime() + 3600 * 8 * 1000,
        delivering_date: new Date(date).getTime() + 3600 * 8 * 1000,
        delivery_date: new Date(date).getTime() + 3600 * 8 * 1000,
      },
    ]);
  });

  it("correctly formats missing dates for an order's status", async () => {
    const orderStatus = {
      name: "supplier",
    };
    queryBuilder.innerJoin.mockResolvedValueOnce([orderStatus]);
    const orderId = "1234";

    const results = await OrderStatuses(undefined, { orderId });

    expect(queryBuilder.where).toHaveBeenCalledWith("o.order_id", orderId);
    expect(results).toEqual([
      {
        name: orderStatus.name,
        submission_date: null,
        delivering_date: null,
        delivery_date: null,
      },
    ]);
  });
});
