import knex from "../../../knex/knex";
import { PushNotificationToken } from "../../generated/graphql";

const getPushNotificationTokens = async (user_ids?: string[]) => {
  if (!user_ids || user_ids.length === 0) {
    return (await knex("push_notification_tokens").select(
      "*"
    )) as PushNotificationToken[];
  } else {
    return (await knex("push_notification_tokens")
      .select("*")
      .whereIn("user_id", user_ids)) as PushNotificationToken[];
  }
};

export default getPushNotificationTokens;
