import HealthCheck from "./healthCheck";

describe("healthCheck REST endpoint", () => {
  const mockReq = {
    body: "",
  };
  const mockRes = {
    send: jest.fn().mockReturnThis(),
    status: jest.fn().mockReturnThis(),
  };
  const healthCheckApi: HealthCheck = new HealthCheck(
    jest.fn() as never,
    jest.fn() as never
  );
  const endpointApolloHttpPostSpy: jest.SpyInstance = jest.spyOn(
    healthCheckApi as never,
    "apolloHttpPost"
  );

  beforeEach(() => {
    endpointApolloHttpPostSpy.mockClear();
  });

  it("sends successful OK message", async () => {
    await healthCheckApi.handler(mockReq as never, mockRes as never);

    expect(mockRes.status).toHaveBeenCalledWith(200);
    expect(mockRes.send).toHaveBeenCalledWith("Server OK");
  });
});
