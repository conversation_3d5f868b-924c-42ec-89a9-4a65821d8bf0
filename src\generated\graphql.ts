import { GraphQLResolveInfo, GraphQLScalarType, GraphQLScalarTypeConfig } from 'graphql';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type RequireFields<T, K extends keyof T> = Omit<T, K> & { [P in K]-?: NonNullable<T[P]> };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: string;
  String: string;
  Boolean: boolean;
  Int: number;
  Float: number;
  Date: any;
  DateTime: any;
  JSON: any;
  JSONObject: any;
  Time: any;
};

export type Account = {
  __typename?: 'Account';
  balanceBankAccount?: Maybe<BalanceBankAccount>;
  balanceCreditCard?: Maybe<BalanceCreditCard>;
  id?: Maybe<Scalars['String']>;
  is_default?: Maybe<Scalars['Boolean']>;
  type?: Maybe<Scalars['String']>;
};

export type ActionItem = {
  __typename?: 'ActionItem';
  description?: Maybe<Scalars['String']>;
  invoiceItem?: Maybe<InvoiceItem>;
};

export type ActivityLog = {
  __typename?: 'ActivityLog';
  created_at: Scalars['DateTime'];
  employee_id?: Maybe<Scalars['Int']>;
  id: Scalars['Int'];
  location?: Maybe<Scalars['String']>;
  log_number: Scalars['Int'];
  metadata?: Maybe<Scalars['JSONObject']>;
  supplier_id: Scalars['Int'];
  type: Scalars['String'];
  user_id?: Maybe<Scalars['Int']>;
};

export type ActivityLogFilters = {
  employeeId?: InputMaybe<Scalars['Int']>;
  location?: InputMaybe<Scalars['String']>;
  type?: InputMaybe<Scalars['String']>;
  userId?: InputMaybe<Scalars['Int']>;
};

export type ActivityLogInput = {
  filters?: InputMaybe<ActivityLogFilters>;
  pagination?: InputMaybe<PaginationInput>;
  sortBy?: InputMaybe<SortBy>;
  supplierId: Scalars['Int'];
};

export type ActivityLogOutput = {
  __typename?: 'ActivityLogOutput';
  activityLog?: Maybe<Array<Maybe<ActivityLog>>>;
  totalCount?: Maybe<Scalars['Int']>;
};

export enum ActivityLogType {
  CreditCreated = 'CREDIT_CREATED',
  OrderCreated = 'ORDER_CREATED',
  OrderSubmitted = 'ORDER_SUBMITTED'
}

export type AddInvoiceInput = {
  config?: InputMaybe<Scalars['JSONObject']>;
  credit?: InputMaybe<Scalars['Float']>;
  date_created?: InputMaybe<Scalars['DateTime']>;
  date_received?: InputMaybe<Scalars['DateTime']>;
  discount?: InputMaybe<Scalars['Float']>;
  invoice_id?: InputMaybe<Scalars['String']>;
  invoice_items?: InputMaybe<Array<InvoiceItemInput>>;
  notes?: InputMaybe<Scalars['String']>;
  order_id?: InputMaybe<Scalars['ID']>;
  order_number?: InputMaybe<Scalars['Int']>;
  paid?: InputMaybe<Scalars['Float']>;
  payment_method?: InputMaybe<Scalars['String']>;
  return_items?: InputMaybe<Scalars['Boolean']>;
  subtotal: Scalars['Float'];
  supplier_id: Scalars['ID'];
  total?: InputMaybe<Scalars['Float']>;
  updated_at?: InputMaybe<Scalars['DateTime']>;
  user_id?: InputMaybe<Scalars['ID']>;
};

export type AddInvoiceItemsInput = {
  invoice_id?: InputMaybe<Scalars['ID']>;
  invoice_items?: InputMaybe<Array<InvoiceItemInput>>;
};

export type AddItemToCartInput = {
  cartId: Scalars['ID'];
  itemId: Scalars['ID'];
  itemUomId?: InputMaybe<Scalars['ID']>;
  quantity: Scalars['Int'];
};

export type AddPushNotificationTokenInput = {
  token: Scalars['String'];
  user_id: Scalars['ID'];
};

export type AffectedItem = {
  __typename?: 'AffectedItem';
  discount_amount: Scalars['Float'];
  item_id: Scalars['ID'];
  promotion_id: Scalars['ID'];
};

export type AllCustomPricesInputV2 = {
  supplierId: Scalars['ID'];
};

export type AllCustomPricesOutputV2 = BaseOutput & {
  __typename?: 'AllCustomPricesOutputV2';
  totalCount: Scalars['Int'];
  userCustomPrices?: Maybe<Array<Maybe<UserCustomPrices>>>;
};

export type BalanceBankAccount = {
  __typename?: 'BalanceBankAccount';
  accountName?: Maybe<Scalars['String']>;
  accountNumberMask?: Maybe<Scalars['String']>;
  institutionName?: Maybe<Scalars['String']>;
};

export type BalanceCreditCard = {
  __typename?: 'BalanceCreditCard';
  brand?: Maybe<Scalars['String']>;
  expiredMonth?: Maybe<Scalars['String']>;
  expiredYear?: Maybe<Scalars['String']>;
  last4?: Maybe<Scalars['String']>;
};

export type BalanceLink = {
  __typename?: 'BalanceLink';
  link?: Maybe<Scalars['String']>;
};

export type BaseOutput = {
  totalCount: Scalars['Int'];
};

export type BasicCategory = {
  __typename?: 'BasicCategory';
  image?: Maybe<Scalars['String']>;
  name?: Maybe<Scalars['String']>;
  value?: Maybe<Scalars['String']>;
};

export type BrandSpotlights = {
  __typename?: 'BrandSpotlights';
  id: Scalars['ID'];
  name: Scalars['String'];
  spotlight_image?: Maybe<Scalars['String']>;
};

export type CaptureTransactionInput = {
  amount?: InputMaybe<Scalars['Float']>;
  transactionId?: InputMaybe<Scalars['String']>;
};

export type Cart = {
  __typename?: 'Cart';
  cartItems?: Maybe<Array<Maybe<CartItem>>>;
  created_at?: Maybe<Scalars['DateTime']>;
  id: Scalars['ID'];
  is_open?: Maybe<Scalars['Boolean']>;
  subCarts?: Maybe<Array<Maybe<SubCart>>>;
  subtotal?: Maybe<Scalars['Float']>;
  total_quantity?: Maybe<Scalars['Int']>;
  updated_at?: Maybe<Scalars['DateTime']>;
  userId: Scalars['ID'];
};

export type CartItem = {
  __typename?: 'CartItem';
  archived?: Maybe<Scalars['Boolean']>;
  cart_id: Scalars['ID'];
  cog_price?: Maybe<Scalars['Float']>;
  created_at?: Maybe<Scalars['DateTime']>;
  crv?: Maybe<Scalars['String']>;
  custom_price?: Maybe<Scalars['Float']>;
  discounted_price?: Maybe<Scalars['Float']>;
  id: Scalars['ID'];
  image?: Maybe<Scalars['String']>;
  item_id: Scalars['ID'];
  item_uom_id?: Maybe<Scalars['ID']>;
  metadata?: Maybe<Scalars['String']>;
  nacs_category?: Maybe<Scalars['String']>;
  nacs_subcategory?: Maybe<Scalars['String']>;
  name: Scalars['String'];
  notes?: Maybe<Scalars['String']>;
  oos?: Maybe<Scalars['Boolean']>;
  price?: Maybe<Scalars['Float']>;
  price_purchased_at?: Maybe<Scalars['Float']>;
  qb_id?: Maybe<Scalars['String']>;
  qoh?: Maybe<Scalars['Int']>;
  quantity?: Maybe<Scalars['Int']>;
  size?: Maybe<Scalars['String']>;
  supplier?: Maybe<Scalars['String']>;
  supplier_code?: Maybe<Scalars['String']>;
  unit_size?: Maybe<Scalars['String']>;
  uoms?: Maybe<ItemUom>;
  upc1?: Maybe<Scalars['ID']>;
  upc2?: Maybe<Scalars['ID']>;
  updated_at?: Maybe<Scalars['DateTime']>;
};

export type CatalogDisplayOptionsInput = {
  show_case_size: Scalars['Boolean'];
  show_sku: Scalars['Boolean'];
  show_stock: Scalars['Boolean'];
  show_upc: Scalars['Boolean'];
};

export type CatalogTemplate = {
  __typename?: 'CatalogTemplate';
  config: Scalars['JSON'];
  created_at: Scalars['DateTime'];
  id: Scalars['ID'];
  name: Scalars['String'];
  pdf_path?: Maybe<Scalars['String']>;
  supplier_id: Scalars['ID'];
  updated_at: Scalars['DateTime'];
};

export type CatalogTemplateConfigInput = {
  category?: InputMaybe<Scalars['String']>;
  company_info: Scalars['String'];
  display_options: CatalogDisplayOptionsInput;
  pricing_enabled: Scalars['Boolean'];
  sort_by: CatalogTemplateSort;
  subtitle?: InputMaybe<Scalars['String']>;
  title: Scalars['String'];
};

export enum CatalogTemplateSort {
  Category = 'CATEGORY',
  ProductName = 'PRODUCT_NAME',
  Sku = 'SKU'
}

export type Category = {
  __typename?: 'Category';
  id: Scalars['ID'];
  image?: Maybe<Scalars['String']>;
  items?: Maybe<Array<Maybe<Item>>>;
  name: Scalars['String'];
  ordering?: Maybe<Scalars['Int']>;
  supplier_id: Scalars['ID'];
};

export type CategoryInput = {
  id?: InputMaybe<Scalars['ID']>;
  image?: InputMaybe<Scalars['String']>;
  items?: InputMaybe<Array<InputMaybe<ItemInput>>>;
  name: Scalars['String'];
  ordering?: InputMaybe<Scalars['Int']>;
  supplier_id: Scalars['ID'];
};

export type CreateActivityLogInput = {
  employeeId?: InputMaybe<Scalars['Int']>;
  location?: InputMaybe<Scalars['String']>;
  metadata?: InputMaybe<Scalars['JSONObject']>;
  supplierId: Scalars['Int'];
  type: Scalars['String'];
  userId?: InputMaybe<Scalars['Int']>;
};

export type CreateBalanceTransactionInput = {
  amount: Scalars['Float'];
  title: Scalars['String'];
  userId: Scalars['ID'];
};

export type CreateCartInput = {
  userId: Scalars['ID'];
};

export type CreateCatalogTemplateInput = {
  config: CatalogTemplateConfigInput;
  name: Scalars['String'];
  supplier_id: Scalars['ID'];
};

export type CreateCreditInput = {
  credit_items: Array<CreditItemInput>;
  images?: InputMaybe<Array<Scalars['String']>>;
  invoice_id: Scalars['Int'];
  status?: InputMaybe<Scalars['String']>;
  supplier_id: Scalars['Int'];
  user_id: Scalars['Int'];
};

export type CreateEmployeeInput = {
  appAccess?: InputMaybe<Scalars['Boolean']>;
  dashboardAccess?: InputMaybe<Scalars['Boolean']>;
  email: Scalars['String'];
  name: Scalars['String'];
  password: Scalars['String'];
  phone: Scalars['String'];
  roleIds?: InputMaybe<Array<Scalars['ID']>>;
  routeIds?: InputMaybe<Array<Scalars['ID']>>;
  supplierId: Scalars['ID'];
};

export type CreateGoalAssignmentInput = {
  employee_id?: InputMaybe<Scalars['ID']>;
  target_amount: Scalars['Float'];
};

export type CreateGoalInput = {
  assignments: Array<CreateGoalAssignmentInput>;
  end_date?: InputMaybe<Scalars['Date']>;
  name: Scalars['String'];
  period: GoalPeriod;
  start_date: Scalars['Date'];
  supplier_id: Scalars['ID'];
  target_amount: Scalars['Float'];
  type: GoalType;
};

export type CreateOrderInput = {
  config?: InputMaybe<Scalars['JSONObject']>;
  deliveryDate?: InputMaybe<Scalars['Date']>;
  notes?: InputMaybe<Scalars['String']>;
  orderItems?: InputMaybe<Array<InputMaybe<UpdateOrderItemInput>>>;
  orderName?: InputMaybe<Scalars['String']>;
  supplierId: Scalars['ID'];
  userId: Scalars['ID'];
};

export type CreatePromotionInput = {
  active?: InputMaybe<Scalars['Boolean']>;
  applies_to_all_items: Scalars['Boolean'];
  applies_to_all_users: Scalars['Boolean'];
  buy_quantity?: InputMaybe<Scalars['Int']>;
  discount_amount?: InputMaybe<Scalars['Float']>;
  discount_percentage?: InputMaybe<Scalars['Float']>;
  end_date: Scalars['DateTime'];
  free_quantity?: InputMaybe<Scalars['Int']>;
  items?: InputMaybe<Array<PromotionItemInput>>;
  max_uses_per_customer?: InputMaybe<Scalars['Int']>;
  min_order_amount?: InputMaybe<Scalars['Float']>;
  name: Scalars['String'];
  promotion_type: Scalars['ID'];
  start_date: Scalars['DateTime'];
  supplier_id: Scalars['ID'];
  total_usage_limit?: InputMaybe<Scalars['Int']>;
  users?: InputMaybe<Array<PromotionUserInput>>;
};

export type CreateSupplierConfigInput = {
  show_order_tabs?: InputMaybe<Scalars['Boolean']>;
  supplierId: Scalars['ID'];
};

export type CreateSupplierInput = {
  address?: InputMaybe<Scalars['String']>;
  config?: InputMaybe<Scalars['JSONObject']>;
  email: Scalars['String'];
  logo?: InputMaybe<Scalars['String']>;
  minimum?: InputMaybe<Scalars['Int']>;
  name: Scalars['String'];
  need_signup?: InputMaybe<Scalars['Boolean']>;
  password: Scalars['String'];
  phone_number?: InputMaybe<Scalars['String']>;
  spotlight_image?: InputMaybe<Scalars['String']>;
};

export type CreateTable = {
  cartID: Scalars['ID'];
  itemID: Scalars['ID'];
  quantity: Scalars['Int'];
};

export type CreateUserInput = {
  address?: InputMaybe<Scalars['String']>;
  config?: InputMaybe<Scalars['JSONObject']>;
  contact_email?: InputMaybe<Scalars['String']>;
  created_at?: InputMaybe<Scalars['DateTime']>;
  created_by?: InputMaybe<Scalars['String']>;
  custom_prices?: InputMaybe<Array<InputMaybe<CustomPriceWithoutUserIdInput>>>;
  custom_uom_prices?: InputMaybe<Array<InputMaybe<CustomUomPriceInput>>>;
  delivery_window?: InputMaybe<DeliveryWindowInput>;
  ein?: InputMaybe<Scalars['String']>;
  hidden_products?: InputMaybe<Array<Scalars['ID']>>;
  name?: InputMaybe<Scalars['String']>;
  net_terms_days?: InputMaybe<Scalars['Int']>;
  password?: InputMaybe<Scalars['String']>;
  phone_number?: InputMaybe<Scalars['String']>;
  qb_id?: InputMaybe<Scalars['String']>;
  route_id?: InputMaybe<Scalars['ID']>;
  store_group?: InputMaybe<Scalars['String']>;
  suppliers: Array<Scalars['ID']>;
  updated_at?: InputMaybe<Scalars['DateTime']>;
  user_name?: InputMaybe<Scalars['String']>;
};

export type Credit = {
  __typename?: 'Credit';
  archived?: Maybe<Scalars['Boolean']>;
  cash_amount?: Maybe<Scalars['Float']>;
  created_at?: Maybe<Scalars['DateTime']>;
  creditItems?: Maybe<Array<Maybe<CreditItem>>>;
  credit_number?: Maybe<Scalars['Int']>;
  customerDetails?: Maybe<User>;
  id?: Maybe<Scalars['Int']>;
  images?: Maybe<Array<Maybe<Scalars['String']>>>;
  invoiceDetails?: Maybe<Invoice>;
  invoice_id?: Maybe<Scalars['Int']>;
  order_id?: Maybe<Scalars['Int']>;
  order_number?: Maybe<Scalars['Int']>;
  status: CreditStatus;
  supplier_id?: Maybe<Scalars['Int']>;
  total?: Maybe<Scalars['Float']>;
  updated_at?: Maybe<Scalars['DateTime']>;
  user_id?: Maybe<Scalars['Int']>;
};

export type CreditItem = {
  __typename?: 'CreditItem';
  credit_id?: Maybe<Scalars['Int']>;
  id?: Maybe<Scalars['Int']>;
  invoice_item_id?: Maybe<Scalars['Int']>;
  item_id?: Maybe<Scalars['Int']>;
  item_snapshot?: Maybe<Item>;
  note?: Maybe<Scalars['String']>;
  quantity?: Maybe<Scalars['Int']>;
  reason?: Maybe<Scalars['String']>;
  total_price?: Maybe<Scalars['Float']>;
  unit_price?: Maybe<Scalars['Float']>;
};

export type CreditItemInput = {
  item_id?: InputMaybe<Scalars['Int']>;
  note?: InputMaybe<Scalars['String']>;
  quantity?: InputMaybe<Scalars['Int']>;
  reason?: InputMaybe<CreditReason>;
};

export enum CreditReason {
  Damaged = 'DAMAGED',
  Expired = 'EXPIRED',
  Mispick = 'MISPICK',
  Missing = 'MISSING',
  Other = 'OTHER',
  Overage = 'OVERAGE',
  Return = 'RETURN'
}

export type CreditRequest = {
  __typename?: 'CreditRequest';
  customerDetails?: Maybe<User>;
  damaged?: Maybe<Scalars['Boolean']>;
  expired?: Maybe<Scalars['Boolean']>;
  id: Scalars['ID'];
  image?: Maybe<Scalars['String']>;
  itemDetails?: Maybe<Item>;
  item_id: Scalars['ID'];
  mispick?: Maybe<Scalars['Boolean']>;
  order_id: Scalars['ID'];
  price_purchased_at?: Maybe<Scalars['Float']>;
  quantity: Scalars['Int'];
  status?: Maybe<Scalars['String']>;
  supplier_id: Scalars['ID'];
  user_id: Scalars['ID'];
};

export type CreditRequestInput = {
  isDamaged?: InputMaybe<Scalars['Boolean']>;
  isExpired?: InputMaybe<Scalars['Boolean']>;
  isMispick?: InputMaybe<Scalars['Boolean']>;
  itemId: Scalars['ID'];
  pricePurchasedAt?: InputMaybe<Scalars['Float']>;
  quantity: Scalars['Int'];
};

export enum CreditStatus {
  Approved = 'APPROVED',
  Cancelled = 'CANCELLED',
  Pending = 'PENDING',
  Processed = 'PROCESSED',
  Rejected = 'REJECTED'
}

export type CustomPrice = {
  __typename?: 'CustomPrice';
  item_id: Scalars['ID'];
  price: Scalars['Float'];
  user_id: Scalars['ID'];
};

export type CustomPriceInput = {
  item_id: Scalars['ID'];
  price: Scalars['Float'];
  user_id: Scalars['ID'];
};

export type CustomPriceWithoutUserId = {
  __typename?: 'CustomPriceWithoutUserId';
  item_id: Scalars['ID'];
  price: Scalars['Float'];
};

export type CustomPriceWithoutUserIdInput = {
  item_id: Scalars['ID'];
  price: Scalars['Float'];
};

export type CustomUomPrice = {
  __typename?: 'CustomUOMPrice';
  item_uom_id?: Maybe<Scalars['ID']>;
  price: Scalars['Float'];
  uom_id?: Maybe<Scalars['ID']>;
  uom_name: Scalars['String'];
  user_id: Scalars['ID'];
};

export type CustomUomPriceInput = {
  item_uom_id: Scalars['ID'];
  price: Scalars['Float'];
  user_id: Scalars['ID'];
};

export type CustomUomPricesByItem = {
  __typename?: 'CustomUOMPricesByItem';
  item_id: Scalars['ID'];
  uom_prices: Array<CustomUomPrice>;
};

export type CustomerGroup = {
  __typename?: 'CustomerGroup';
  active?: Maybe<Scalars['Int']>;
  count?: Maybe<Scalars['Int']>;
  group?: Maybe<Scalars['String']>;
};

export type CustomerHiddenProduct = {
  __typename?: 'CustomerHiddenProduct';
  itemId: Scalars['ID'];
  supplierId?: Maybe<Scalars['ID']>;
  userId?: Maybe<Scalars['ID']>;
};

export type CustomerHiddenProductsInput = {
  customerId?: InputMaybe<Scalars['ID']>;
  supplierId: Scalars['ID'];
};

export type CustomerHiddenProductsOutput = {
  __typename?: 'CustomerHiddenProductsOutput';
  items: Array<Maybe<CustomerHiddenProduct>>;
};

export type CutoffTime = {
  __typename?: 'CutoffTime';
  cutoffDay?: Maybe<Scalars['String']>;
  cutoffTime?: Maybe<Scalars['String']>;
  daysToDelivery?: Maybe<Scalars['Int']>;
  deliveryDay?: Maybe<Scalars['String']>;
  deliveryTime?: Maybe<Scalars['String']>;
  id?: Maybe<Scalars['String']>;
  supplier: Scalars['String'];
  supplierInfo?: Maybe<Supplier>;
};

export type DashboardMetricBestSeller = {
  __typename?: 'DashboardMetricBestSeller';
  category?: Maybe<Category>;
  image?: Maybe<Scalars['String']>;
  name?: Maybe<Scalars['String']>;
  quantity?: Maybe<Scalars['Int']>;
};

export type DashboardMetricBrandBreakdown = {
  __typename?: 'DashboardMetricBrandBreakdown';
  brand?: Maybe<Scalars['String']>;
  breakdown?: Maybe<Scalars['Float']>;
  quantity?: Maybe<Scalars['Int']>;
};

export enum DashboardMetricType {
  ActiveBuyers = 'ACTIVE_BUYERS',
  AvgOrderValue = 'AVG_ORDER_VALUE',
  BestSellers = 'BEST_SELLERS',
  BrandBreakdown = 'BRAND_BREAKDOWN',
  CasesOrdered = 'CASES_ORDERED',
  CreditTotal = 'CREDIT_TOTAL',
  FirstOrders = 'FIRST_ORDERS',
  GmvTotal = 'GMV_TOTAL',
  NetRevenue = 'NET_REVENUE',
  NumCanceledOrders = 'NUM_CANCELED_ORDERS',
  NumConfirmedOrders = 'NUM_CONFIRMED_ORDERS',
  NumInvoices = 'NUM_INVOICES',
  NumOrders = 'NUM_ORDERS',
  NumUnapprovedCustomers = 'NUM_UNAPPROVED_CUSTOMERS',
  NumUnconfirmedOrders = 'NUM_UNCONFIRMED_ORDERS',
  NumUsers = 'NUM_USERS',
  ProfitTotal = 'PROFIT_TOTAL',
  SalesByRoutes = 'SALES_BY_ROUTES'
}

export type DashboardMetrics = {
  __typename?: 'DashboardMetrics';
  ACTIVE_BUYERS?: Maybe<Scalars['Int']>;
  AVG_ORDER_VALUE?: Maybe<Scalars['Float']>;
  BEST_SELLERS?: Maybe<Array<Maybe<DashboardMetricBestSeller>>>;
  BRAND_BREAKDOWN?: Maybe<Array<Maybe<DashboardMetricBrandBreakdown>>>;
  CASES_ORDERED?: Maybe<Scalars['Int']>;
  CREDIT_TOTAL?: Maybe<Scalars['Float']>;
  FIRST_ORDERS?: Maybe<Scalars['Int']>;
  GMV_TOTAL?: Maybe<Scalars['Float']>;
  NET_REVENUE?: Maybe<Scalars['Float']>;
  NUM_CANCELED_ORDERS?: Maybe<Scalars['Int']>;
  NUM_CONFIRMED_ORDERS?: Maybe<Scalars['Int']>;
  NUM_INVOICES?: Maybe<Scalars['Int']>;
  NUM_ORDERS?: Maybe<Scalars['Int']>;
  NUM_UNAPPROVED_CUSTOMERS?: Maybe<Scalars['Int']>;
  NUM_UNCONFIRMED_ORDERS?: Maybe<Scalars['Int']>;
  NUM_USERS?: Maybe<Scalars['Int']>;
  PROFIT_TOTAL?: Maybe<Scalars['Float']>;
  SALES_BY_ROUTES?: Maybe<Array<Maybe<SalesByRoute>>>;
};

export type Deal = {
  __typename?: 'Deal';
  deal_id?: Maybe<Scalars['ID']>;
  discount?: Maybe<Scalars['Float']>;
  item_id?: Maybe<Scalars['ID']>;
  quantity?: Maybe<Scalars['Int']>;
  type?: Maybe<Scalars['String']>;
};

export type DeleteCreditInput = {
  id: Scalars['Int'];
};

export type DeliveryWindow = {
  __typename?: 'DeliveryWindow';
  days_of_week?: Maybe<Array<Maybe<Scalars['String']>>>;
  end_time?: Maybe<Scalars['String']>;
  start_time?: Maybe<Scalars['String']>;
};

export type DeliveryWindowInput = {
  days_of_week?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  end_time?: InputMaybe<Scalars['String']>;
  start_time?: InputMaybe<Scalars['String']>;
};

export type Employee = {
  __typename?: 'Employee';
  app_access: Scalars['Boolean'];
  archived: Scalars['Boolean'];
  created_at: Scalars['DateTime'];
  dashboard_access: Scalars['Boolean'];
  email: Scalars['String'];
  id: Scalars['ID'];
  last_login?: Maybe<Scalars['DateTime']>;
  name: Scalars['String'];
  phone?: Maybe<Scalars['String']>;
  roles?: Maybe<Array<Role>>;
  routes?: Maybe<Array<Route>>;
  updated_at: Scalars['DateTime'];
};

export type EmployeesFiltersV2 = {
  appAccess?: InputMaybe<Scalars['Boolean']>;
  createdAfter?: InputMaybe<Scalars['DateTime']>;
  createdBefore?: InputMaybe<Scalars['DateTime']>;
  dashboardAccess?: InputMaybe<Scalars['Boolean']>;
  email?: InputMaybe<Scalars['String']>;
  ids?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  includeArchived?: InputMaybe<Scalars['Boolean']>;
  lastLoginAfter?: InputMaybe<Scalars['DateTime']>;
  lastLoginBefore?: InputMaybe<Scalars['DateTime']>;
  name?: InputMaybe<Scalars['String']>;
  phone?: InputMaybe<Scalars['String']>;
  query?: InputMaybe<Scalars['String']>;
  roleIds?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
};

export type EmployeesInputV2 = {
  filters?: InputMaybe<EmployeesFiltersV2>;
  pagination?: InputMaybe<PaginationInput>;
  sortBy?: InputMaybe<SortBy>;
  supplierId: Scalars['ID'];
};

export type EmployeesOutputV2 = BaseOutput & {
  __typename?: 'EmployeesOutputV2';
  employees: Array<Employee>;
  totalCount: Scalars['Int'];
};

export type GetActionItemsInput = {
  invoice_id?: InputMaybe<Scalars['ID']>;
  invoice_item_id?: InputMaybe<Scalars['ID']>;
};

export type GetBestSellersFilters = {
  category?: InputMaybe<Scalars['ID']>;
  dateRange?: InputMaybe<Array<InputMaybe<Scalars['DateTime']>>>;
  driver?: InputMaybe<Scalars['String']>;
  duration?: InputMaybe<Scalars['Int']>;
  routeIds?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  serviceType?: InputMaybe<Scalars['String']>;
};

export type GetBestSellersInput = {
  filters?: InputMaybe<GetBestSellersFilters>;
  limit?: InputMaybe<Scalars['Int']>;
  pagination?: InputMaybe<PaginationInput>;
  supplier_id: Scalars['ID'];
};

export type GetBestSellersOutput = {
  __typename?: 'GetBestSellersOutput';
  bestSellers: Array<Maybe<DashboardMetricBestSeller>>;
  totalCount: Scalars['Int'];
};

export type GetCartsInput = {
  ids?: InputMaybe<Array<Scalars['ID']>>;
  userId?: InputMaybe<Scalars['ID']>;
  userIds?: InputMaybe<Array<Scalars['ID']>>;
};

export type GetCatalogTemplatesInput = {
  id?: InputMaybe<Scalars['ID']>;
  supplier_id: Scalars['ID'];
};

export type GetCategoriesBySupplierInput = {
  fillItemsData?: InputMaybe<Scalars['Boolean']>;
  supplierId?: InputMaybe<Scalars['ID']>;
};

export type GetCategoriesInput = {
  pagination?: InputMaybe<PaginationInput>;
  supplierId?: InputMaybe<Scalars['ID']>;
  userId?: InputMaybe<Scalars['ID']>;
};

export type GetCreditsFilters = {
  archived?: InputMaybe<Scalars['Boolean']>;
  createdAtRange?: InputMaybe<Array<InputMaybe<Scalars['DateTime']>>>;
  ids?: InputMaybe<Array<InputMaybe<Scalars['Int']>>>;
  invoice_ids?: InputMaybe<Array<InputMaybe<Scalars['Int']>>>;
  statuses?: InputMaybe<Array<InputMaybe<CreditStatus>>>;
  user_ids?: InputMaybe<Array<InputMaybe<Scalars['Int']>>>;
};

export type GetCreditsInput = {
  filters?: InputMaybe<GetCreditsFilters>;
  pagination?: InputMaybe<PaginationInput>;
  sortBy?: InputMaybe<SortBy>;
  supplier_id: Scalars['Int'];
};

export type GetCutoffTimesInput = {
  businessId: Scalars['ID'];
};

export type GetFavoritesInput = {
  employeeId?: InputMaybe<Scalars['Int']>;
  pagination?: InputMaybe<PaginationInput>;
  userId: Scalars['Int'];
};

export type GetInvoiceItemMatchInput = {
  invoiceId: Scalars['ID'];
  upc: Scalars['String'];
};

export type GetInvoicesBySupplierFilters = {
  dateRange?: InputMaybe<Array<Scalars['DateTime']>>;
  deliveryDate?: InputMaybe<Scalars['DateTime']>;
  driver?: InputMaybe<Scalars['String']>;
  lastPaidDate?: InputMaybe<Scalars['Date']>;
  paidStatus?: InputMaybe<Scalars['String']>;
  payment_status?: InputMaybe<Scalars['String']>;
  query?: InputMaybe<Scalars['String']>;
  routeIds?: InputMaybe<Array<Scalars['String']>>;
  signed?: InputMaybe<Scalars['Boolean']>;
  status?: InputMaybe<Scalars['String']>;
  userId?: InputMaybe<Scalars['String']>;
};

export type GetInvoicesBySupplierInput = {
  filters?: InputMaybe<GetInvoicesBySupplierFilters>;
  pagination?: InputMaybe<PaginationInput>;
  sortBy?: InputMaybe<SortBy>;
  supplierId: Scalars['ID'];
};

export type GetInvoicesInput = {
  ids?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  onlyWithCredits?: InputMaybe<Scalars['Boolean']>;
  orderIds?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  pagination?: InputMaybe<PaginationInput>;
  query?: InputMaybe<Scalars['String']>;
  supplierId?: InputMaybe<Scalars['ID']>;
};

export type GetItemsByFilterInput = {
  category?: InputMaybe<Scalars['String']>;
  pagination?: InputMaybe<PaginationInput>;
  tag?: InputMaybe<Scalars['String']>;
  userId?: InputMaybe<Scalars['ID']>;
};

export type GetItemsBySupplierInput = {
  brand?: InputMaybe<Scalars['String']>;
  category?: InputMaybe<Scalars['String']>;
  ids?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  pagination?: InputMaybe<PaginationInput>;
  query?: InputMaybe<Scalars['String']>;
  section?: InputMaybe<Scalars['String']>;
  supplierId?: InputMaybe<Scalars['ID']>;
  upc?: InputMaybe<Scalars['ID']>;
  userId?: InputMaybe<Scalars['ID']>;
};

export type GetItemsInput = {
  ids?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  pagination?: InputMaybe<PaginationInput>;
  upcs?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  userId?: InputMaybe<Scalars['ID']>;
};

export type GetOrderItemTotalsInput = {
  currentTz?: InputMaybe<Scalars['String']>;
  dayOffset?: InputMaybe<Scalars['Int']>;
  numWeeks: Scalars['Int'];
  routeId?: InputMaybe<Scalars['ID']>;
  supplierId: Scalars['ID'];
};

export type GetOrdersBySupplierFilters = {
  deliveryDate?: InputMaybe<Scalars['Date']>;
  deliveryDateRange?: InputMaybe<Array<Scalars['Date']>>;
  driver?: InputMaybe<Scalars['String']>;
  ids?: InputMaybe<Array<Scalars['ID']>>;
  query?: InputMaybe<Scalars['String']>;
  routeIds?: InputMaybe<Array<Scalars['String']>>;
  signed?: InputMaybe<Scalars['Boolean']>;
  status?: InputMaybe<Scalars['String']>;
  userId?: InputMaybe<Scalars['String']>;
  userIds?: InputMaybe<Array<Scalars['String']>>;
};

export type GetOrdersBySupplierInput = {
  filters?: InputMaybe<GetOrdersBySupplierFilters>;
  pagination?: InputMaybe<PaginationInput>;
  sortBy?: InputMaybe<SortBy>;
  supplierId: Scalars['ID'];
};

export type GetOrdersInput = {
  ids?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  pagination?: InputMaybe<PaginationInput>;
  query?: InputMaybe<Scalars['String']>;
  status?: InputMaybe<Scalars['String']>;
  supplierId?: InputMaybe<Scalars['ID']>;
  userId?: InputMaybe<Scalars['ID']>;
};

export type GetPromotionsInput = {
  filters?: InputMaybe<PromotionFilters>;
  pagination?: InputMaybe<PaginationInput>;
  sortBy?: InputMaybe<SortBy>;
  supplier_id: Scalars['ID'];
};

export type GetPushNotificationTokensInput = {
  user_ids?: InputMaybe<Array<Scalars['ID']>>;
};

export type GetRecommendationsInput = {
  limit?: InputMaybe<Scalars['Int']>;
  userId: Scalars['ID'];
};

export type GetRouteTotalsInput = {
  currentTz?: InputMaybe<Scalars['String']>;
  dayOffset?: InputMaybe<Scalars['Int']>;
  numWeeks: Scalars['Int'];
  supplierId: Scalars['ID'];
};

export type GetRoutesBySupplierInput = {
  supplierId: Scalars['ID'];
};

export type GetRoutesInput = {
  ids: Array<Scalars['ID']>;
};

export type GetSalesByRoutesFilters = {
  dateRange?: InputMaybe<Array<InputMaybe<Scalars['DateTime']>>>;
  driver?: InputMaybe<Scalars['String']>;
  duration?: InputMaybe<Scalars['Int']>;
  serviceType?: InputMaybe<Scalars['String']>;
};

export type GetSalesByRoutesInput = {
  filters?: InputMaybe<GetSalesByRoutesFilters>;
  limit?: InputMaybe<Scalars['Int']>;
  pagination?: InputMaybe<PaginationInput>;
  supplier_id: Scalars['ID'];
};

export type GetSalesByRoutesOutput = {
  __typename?: 'GetSalesByRoutesOutput';
  salesByRoutes: Array<Maybe<SalesByRoute>>;
  totalCount: Scalars['Int'];
};

export type GetSectionsInput = {
  pagination?: InputMaybe<PaginationInput>;
  sections?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  userId?: InputMaybe<Scalars['ID']>;
};

export type GetSuppliersInput = {
  ids?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  pagination?: InputMaybe<PaginationInput>;
};

export type GetTagsInput = {
  category?: InputMaybe<Scalars['String']>;
  pagination?: InputMaybe<PaginationInput>;
};

export type GetUsersByFilterInput = {
  filters?: InputMaybe<GetUsersFilters>;
  includeCustomPrices?: InputMaybe<Scalars['Boolean']>;
  pagination?: InputMaybe<PaginationInput>;
  sortBy?: InputMaybe<SortBy>;
  supplierId?: InputMaybe<Scalars['ID']>;
};

export type GetUsersFilters = {
  active?: InputMaybe<Scalars['String']>;
  driver?: InputMaybe<Scalars['String']>;
  hasOpenCart?: InputMaybe<Scalars['Boolean']>;
  hasUnpaidBalance?: InputMaybe<Scalars['Boolean']>;
  route?: InputMaybe<Scalars['String']>;
  searchTerm?: InputMaybe<Scalars['String']>;
};

export type GetUsersInput = {
  ids?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  includeCustomPrices?: InputMaybe<Scalars['Boolean']>;
  pagination?: InputMaybe<PaginationInput>;
  search?: InputMaybe<Scalars['String']>;
  supplierId?: InputMaybe<Scalars['ID']>;
};

export type Goal = {
  __typename?: 'Goal';
  assignments: Array<GoalAssignment>;
  available_periods: Array<GoalPeriodOption>;
  created_at: Scalars['DateTime'];
  end_date?: Maybe<Scalars['Date']>;
  id: Scalars['ID'];
  is_active: Scalars['Boolean'];
  name: Scalars['String'];
  period: GoalPeriod;
  start_date: Scalars['Date'];
  status: GoalStatus;
  supplier_id: Scalars['ID'];
  target_amount: Scalars['Float'];
  type: GoalType;
  updated_at: Scalars['DateTime'];
};

export type GoalAssignment = {
  __typename?: 'GoalAssignment';
  created_at: Scalars['DateTime'];
  current_progress: Scalars['Float'];
  employee?: Maybe<Employee>;
  employee_id?: Maybe<Scalars['ID']>;
  goal_id: Scalars['ID'];
  id: Scalars['ID'];
  percentage_complete: Scalars['Float'];
  target_amount: Scalars['Float'];
  updated_at: Scalars['DateTime'];
};

export enum GoalPeriod {
  Daily = 'DAILY',
  Monthly = 'MONTHLY',
  Weekly = 'WEEKLY'
}

export type GoalPeriodOption = {
  __typename?: 'GoalPeriodOption';
  label: Scalars['String'];
  value: Scalars['String'];
};

export type GoalPeriodSelection = {
  goal_id: Scalars['ID'];
  period_start: Scalars['Date'];
};

export enum GoalStatus {
  Active = 'ACTIVE',
  Completed = 'COMPLETED',
  Past = 'PAST'
}

export enum GoalType {
  SalesAmount = 'SALES_AMOUNT',
  StoresSoldTo = 'STORES_SOLD_TO'
}

export type GoalsFilters = {
  employee_id?: InputMaybe<Scalars['ID']>;
  is_active?: InputMaybe<Scalars['Boolean']>;
  period?: InputMaybe<GoalPeriod>;
  status?: InputMaybe<GoalStatus>;
  supplier_id: Scalars['ID'];
  type?: InputMaybe<GoalType>;
};

export type GoalsInput = {
  filters: GoalsFilters;
  goal_period_selections?: InputMaybe<Array<GoalPeriodSelection>>;
  pagination?: InputMaybe<PaginationInput>;
  sortBy?: InputMaybe<SortBy>;
};

export type GoalsResponse = {
  __typename?: 'GoalsResponse';
  goals: Array<Goal>;
  totalCount: Scalars['Int'];
};

export type GroupPrices = {
  __typename?: 'GroupPrices';
  group: Scalars['String'];
  prices?: Maybe<Array<Maybe<CustomPriceWithoutUserId>>>;
};

export type GroupPricesInputV2 = {
  groupName?: InputMaybe<Scalars['String']>;
  supplierId: Scalars['ID'];
};

export type GroupPricesOutputV2 = BaseOutput & {
  __typename?: 'GroupPricesOutputV2';
  groupPrices?: Maybe<Array<Maybe<GroupPrices>>>;
  totalCount: Scalars['Int'];
};

export type Invoice = {
  __typename?: 'Invoice';
  archived?: Maybe<Scalars['Boolean']>;
  config?: Maybe<Scalars['JSONObject']>;
  credit?: Maybe<Scalars['Float']>;
  customerDetails?: Maybe<User>;
  date_created?: Maybe<Scalars['DateTime']>;
  date_received?: Maybe<Scalars['Float']>;
  discount?: Maybe<Scalars['Float']>;
  id?: Maybe<Scalars['ID']>;
  invoiceItems?: Maybe<Array<Maybe<InvoiceItem>>>;
  invoice_id?: Maybe<Scalars['String']>;
  notes?: Maybe<Scalars['String']>;
  orderDetails?: Maybe<Order>;
  order_id?: Maybe<Scalars['ID']>;
  order_number?: Maybe<Scalars['Int']>;
  paid?: Maybe<Scalars['Float']>;
  payment_method?: Maybe<Scalars['String']>;
  payment_status?: Maybe<Scalars['String']>;
  qb_id?: Maybe<Scalars['String']>;
  signature?: Maybe<Scalars['String']>;
  signature_name?: Maybe<Scalars['String']>;
  subtotal?: Maybe<Scalars['Float']>;
  supplier_id: Scalars['ID'];
  total?: Maybe<Scalars['Float']>;
  updated_at?: Maybe<Scalars['DateTime']>;
};

export type InvoiceItem = {
  __typename?: 'InvoiceItem';
  checked_in?: Maybe<Scalars['Boolean']>;
  checked_in_quantity?: Maybe<Scalars['Int']>;
  cog_price?: Maybe<Scalars['Float']>;
  id: Scalars['ID'];
  invoice_id: Scalars['ID'];
  is_mispick?: Maybe<Scalars['Boolean']>;
  item_id?: Maybe<Scalars['ID']>;
  item_uom_id?: Maybe<Scalars['ID']>;
  metadata?: Maybe<Scalars['String']>;
  name: Scalars['String'];
  price?: Maybe<Scalars['Float']>;
  qb_id?: Maybe<Scalars['String']>;
  quantity?: Maybe<Scalars['Int']>;
  size?: Maybe<Scalars['String']>;
  unit_size?: Maybe<Scalars['String']>;
  uoms?: Maybe<ItemUom>;
  upc1?: Maybe<Scalars['ID']>;
  upc2?: Maybe<Scalars['ID']>;
  upc3?: Maybe<Scalars['ID']>;
  upc4?: Maybe<Scalars['ID']>;
};

export type InvoiceItemInput = {
  checked_in?: InputMaybe<Scalars['Boolean']>;
  checked_in_quantity?: InputMaybe<Scalars['Int']>;
  cog_price?: InputMaybe<Scalars['Float']>;
  id?: InputMaybe<Scalars['ID']>;
  invoice_id?: InputMaybe<Scalars['ID']>;
  is_mispick?: InputMaybe<Scalars['Boolean']>;
  itemUomId?: InputMaybe<Scalars['ID']>;
  item_id?: InputMaybe<Scalars['ID']>;
  name: Scalars['String'];
  price?: InputMaybe<Scalars['Float']>;
  qb_id?: InputMaybe<Scalars['String']>;
  quantity?: InputMaybe<Scalars['Int']>;
  size?: InputMaybe<Scalars['String']>;
  unit_size?: InputMaybe<Scalars['String']>;
  upc1?: InputMaybe<Scalars['ID']>;
  upc2?: InputMaybe<Scalars['ID']>;
  upc3?: InputMaybe<Scalars['ID']>;
  upc4?: InputMaybe<Scalars['ID']>;
};

export type InvoiceItemMatch = {
  __typename?: 'InvoiceItemMatch';
  itemMatches?: Maybe<Array<Maybe<InvoiceItem>>>;
  matchStatus?: Maybe<MatchStatus>;
};

export type InvoiceWithStatus = {
  __typename?: 'InvoiceWithStatus';
  invoice?: Maybe<Invoice>;
  processing?: Maybe<Scalars['Boolean']>;
};

export type InvoicesBySupplier = {
  __typename?: 'InvoicesBySupplier';
  invoices: Array<Invoice>;
  paymentTotals: Scalars['JSONObject'];
  totalCases: Scalars['Int'];
  totalCount: Scalars['Int'];
  totalCredit: Scalars['Float'];
  totalProfit: Scalars['Float'];
};

export type Item = {
  __typename?: 'Item';
  archived?: Maybe<Scalars['Boolean']>;
  avg_cases_per_week?: Maybe<Scalars['Float']>;
  back_in_stock_date?: Maybe<Scalars['DateTime']>;
  cog_price?: Maybe<Scalars['Float']>;
  created_at?: Maybe<Scalars['DateTime']>;
  crv?: Maybe<Scalars['String']>;
  discounted_price?: Maybe<Scalars['Float']>;
  id: Scalars['ID'];
  image?: Maybe<Scalars['String']>;
  img_sm?: Maybe<Scalars['String']>;
  isFavorited?: Maybe<Scalars['Boolean']>;
  item_uom_id?: Maybe<Scalars['ID']>;
  last_ordered_date?: Maybe<Scalars['Float']>;
  local_item?: Maybe<Scalars['Boolean']>;
  metadata?: Maybe<Scalars['String']>;
  min_sale_price?: Maybe<Scalars['Float']>;
  moq?: Maybe<Scalars['Int']>;
  nacs_category?: Maybe<Scalars['String']>;
  nacs_subcategory?: Maybe<Scalars['String']>;
  name: Scalars['String'];
  oos?: Maybe<Scalars['Boolean']>;
  outdated?: Maybe<Scalars['Boolean']>;
  price?: Maybe<Scalars['Float']>;
  promotions?: Maybe<Array<Maybe<Promotion>>>;
  qb_id?: Maybe<Scalars['String']>;
  qb_sync_token?: Maybe<Scalars['String']>;
  qoh?: Maybe<Scalars['Int']>;
  qty_on_hand?: Maybe<Scalars['Int']>;
  related_items?: Maybe<Array<Maybe<Item>>>;
  size?: Maybe<Scalars['String']>;
  supplier?: Maybe<Scalars['String']>;
  supplier_code?: Maybe<Scalars['String']>;
  supplier_info?: Maybe<Supplier>;
  tags?: Maybe<Array<Maybe<ItemTag>>>;
  unit_size?: Maybe<Scalars['String']>;
  uoms?: Maybe<Array<Maybe<ItemUom>>>;
  upc1?: Maybe<Scalars['ID']>;
  upc2?: Maybe<Scalars['ID']>;
  updated_at?: Maybe<Scalars['DateTime']>;
};

export type ItemAvailable = {
  __typename?: 'ItemAvailable';
  item_id?: Maybe<Scalars['ID']>;
  mapped?: Maybe<Scalars['Boolean']>;
  name?: Maybe<Scalars['String']>;
  quantity?: Maybe<Scalars['Int']>;
  supplier?: Maybe<Scalars['String']>;
};

export type ItemAvailableInput = {
  name?: InputMaybe<Scalars['String']>;
  price?: InputMaybe<Scalars['Float']>;
  quantity?: InputMaybe<Scalars['Int']>;
  supplier?: InputMaybe<Scalars['String']>;
  unit_size?: InputMaybe<Scalars['Int']>;
  upc?: InputMaybe<Scalars['String']>;
};

export type ItemInput = {
  back_in_stock_date?: InputMaybe<Scalars['DateTime']>;
  categoryIds?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  cog_price?: InputMaybe<Scalars['Float']>;
  crv?: InputMaybe<Scalars['String']>;
  discounted_price?: InputMaybe<Scalars['Float']>;
  id?: InputMaybe<Scalars['ID']>;
  image?: InputMaybe<Scalars['String']>;
  img_sm?: InputMaybe<Scalars['String']>;
  metadata?: InputMaybe<Scalars['String']>;
  min_sale_price?: InputMaybe<Scalars['Float']>;
  moq?: InputMaybe<Scalars['Int']>;
  nacs_category?: InputMaybe<Scalars['String']>;
  nacs_subcategory?: InputMaybe<Scalars['String']>;
  name?: InputMaybe<Scalars['String']>;
  oos?: InputMaybe<Scalars['Boolean']>;
  price?: InputMaybe<Scalars['Float']>;
  qb_id?: InputMaybe<Scalars['String']>;
  qb_sync_token?: InputMaybe<Scalars['String']>;
  qoh?: InputMaybe<Scalars['Int']>;
  qty_on_hand?: InputMaybe<Scalars['Int']>;
  size?: InputMaybe<Scalars['String']>;
  supplier?: InputMaybe<Scalars['String']>;
  supplier_code?: InputMaybe<Scalars['String']>;
  unit_size?: InputMaybe<Scalars['String']>;
  upc1?: InputMaybe<Scalars['ID']>;
  upc2?: InputMaybe<Scalars['ID']>;
  updated_at?: InputMaybe<Scalars['DateTime']>;
};

export enum ItemTag {
  Archived = 'ARCHIVED',
  New = 'NEW',
  Oos = 'OOS',
  Sale = 'SALE'
}

export type ItemUom = {
  __typename?: 'ItemUOM';
  archived?: Maybe<Scalars['Boolean']>;
  id: Scalars['ID'];
  item_id: Scalars['ID'];
  item_uom_id: Scalars['ID'];
  name: Scalars['String'];
  price?: Maybe<Scalars['Float']>;
  quantity: Scalars['Int'];
  supplier_id: Scalars['ID'];
  uom_id: Scalars['ID'];
  upc?: Maybe<Scalars['String']>;
};

export type ItemsFiltersV2 = {
  archived?: InputMaybe<Scalars['Boolean']>;
  brand?: InputMaybe<Scalars['String']>;
  category?: InputMaybe<Scalars['String']>;
  ids?: InputMaybe<Array<Scalars['ID']>>;
  name?: InputMaybe<Scalars['String']>;
  query?: InputMaybe<Scalars['String']>;
  section?: InputMaybe<Scalars['String']>;
  upc?: InputMaybe<Scalars['String']>;
};

export type ItemsInputV2 = {
  filters?: InputMaybe<ItemsFiltersV2>;
  pagination?: InputMaybe<PaginationInput>;
  sortBy?: InputMaybe<SortBy>;
  supplierId: Scalars['ID'];
  userId?: InputMaybe<Scalars['ID']>;
};

export type ItemsOutputV2 = BaseOutput & {
  __typename?: 'ItemsOutputV2';
  items: Array<Maybe<Item>>;
  totalCount: Scalars['Int'];
};

export enum MatchStatus {
  Multiple = 'multiple',
  None = 'none',
  Single = 'single',
  Suggested = 'suggested'
}

export type Mutation = {
  __typename?: 'Mutation';
  addInvoice?: Maybe<Invoice>;
  addInvoiceItems?: Maybe<Array<Maybe<InvoiceItem>>>;
  addPushNotificationToken?: Maybe<PushNotificationToken>;
  applyPromotion: Order;
  approveUser?: Maybe<User>;
  captureTransaction?: Maybe<Scalars['String']>;
  createAccountFromBalance?: Maybe<Scalars['String']>;
  createBalanceTransaction?: Maybe<Scalars['String']>;
  createCart?: Maybe<Cart>;
  createCatalogTemplate: CatalogTemplate;
  createCredit?: Maybe<Credit>;
  createEmployee?: Maybe<Employee>;
  createGoal: Goal;
  createInvoice?: Maybe<Invoice>;
  createOrder?: Maybe<Order>;
  createPromotion: Promotion;
  createSupplier?: Maybe<Supplier>;
  createSupplierConfig: SupplierConfig;
  createUser?: Maybe<Array<Maybe<Scalars['String']>>>;
  deleteCatalogTemplate: Scalars['Boolean'];
  deleteCategory?: Maybe<Scalars['ID']>;
  deleteCredit?: Maybe<Scalars['ID']>;
  deleteGoal: Scalars['Boolean'];
  deleteItems?: Maybe<Array<Maybe<Scalars['ID']>>>;
  deleteRoute?: Maybe<Scalars['ID']>;
  deleteUsers?: Maybe<Array<Maybe<Scalars['ID']>>>;
  reconcileInvoiceWithItem?: Maybe<InvoiceItem>;
  reorderCategories: Scalars['Boolean'];
  sendReceivingReport?: Maybe<Scalars['String']>;
  startSync: Scalars['Boolean'];
  submitCreditRequests?: Maybe<Scalars['String']>;
  submitFeedback?: Maybe<Scalars['String']>;
  submitOrder?: Maybe<Order>;
  toggleFavorite: ToggleFavoriteResponse;
  updateCatalogTemplate: CatalogTemplate;
  updateCategoryOrder?: Maybe<Category>;
  updateCredit?: Maybe<Credit>;
  updateCreditRequestStatus?: Maybe<CreditRequest>;
  updateDefaultInAccount?: Maybe<Array<Maybe<Account>>>;
  updateEmployee?: Maybe<Employee>;
  updateGoal: Goal;
  updateInvoice?: Maybe<Invoice>;
  updateItemInCart?: Maybe<Cart>;
  updateOrder?: Maybe<Scalars['String']>;
  updatePromotion: Promotion;
  updateSupplierConfig: SupplierConfig;
  updateUser?: Maybe<User>;
  updateUserSuppliers?: Maybe<Array<Maybe<CutoffTime>>>;
  upsertAllCustomPrices: Scalars['Boolean'];
  upsertCategory?: Maybe<Category>;
  upsertCustomPrices?: Maybe<Array<Maybe<CustomPrice>>>;
  upsertItems?: Maybe<Array<Maybe<Item>>>;
  upsertRoute?: Maybe<Route>;
  upsertUOM?: Maybe<Array<Maybe<Uom>>>;
  upsertUOMAllCustomPrices: Scalars['Boolean'];
};


export type MutationAddInvoiceArgs = {
  addInvoiceInput: AddInvoiceInput;
};


export type MutationAddInvoiceItemsArgs = {
  addInvoiceItemsInput: AddInvoiceItemsInput;
};


export type MutationAddPushNotificationTokenArgs = {
  addPushNotificationTokenInput?: InputMaybe<AddPushNotificationTokenInput>;
};


export type MutationApplyPromotionArgs = {
  order_id: Scalars['ID'];
  promotion_id: Scalars['ID'];
  supplier_id: Scalars['ID'];
};


export type MutationApproveUserArgs = {
  id: Scalars['ID'];
};


export type MutationCaptureTransactionArgs = {
  captureTransactionInput?: InputMaybe<CaptureTransactionInput>;
};


export type MutationCreateAccountFromBalanceArgs = {
  payload: Scalars['String'];
};


export type MutationCreateBalanceTransactionArgs = {
  createBalanceTransactionInput: CreateBalanceTransactionInput;
};


export type MutationCreateCartArgs = {
  cart: CreateCartInput;
};


export type MutationCreateCatalogTemplateArgs = {
  input: CreateCatalogTemplateInput;
};


export type MutationCreateCreditArgs = {
  createCreditInput: CreateCreditInput;
};


export type MutationCreateEmployeeArgs = {
  input: CreateEmployeeInput;
};


export type MutationCreateGoalArgs = {
  createGoalInput: CreateGoalInput;
};


export type MutationCreateInvoiceArgs = {
  orderId: Scalars['ID'];
  supplierId: Scalars['ID'];
};


export type MutationCreateOrderArgs = {
  createOrderInput?: InputMaybe<CreateOrderInput>;
};


export type MutationCreatePromotionArgs = {
  input: CreatePromotionInput;
};


export type MutationCreateSupplierArgs = {
  input: CreateSupplierInput;
};


export type MutationCreateSupplierConfigArgs = {
  input: CreateSupplierConfigInput;
};


export type MutationCreateUserArgs = {
  createUserInput?: InputMaybe<CreateUserInput>;
};


export type MutationDeleteCatalogTemplateArgs = {
  id: Scalars['ID'];
};


export type MutationDeleteCategoryArgs = {
  id: Scalars['ID'];
};


export type MutationDeleteCreditArgs = {
  deleteCreditInput: DeleteCreditInput;
};


export type MutationDeleteGoalArgs = {
  id: Scalars['ID'];
};


export type MutationDeleteItemsArgs = {
  itemIds: Array<Scalars['ID']>;
};


export type MutationDeleteRouteArgs = {
  id: Scalars['ID'];
};


export type MutationDeleteUsersArgs = {
  userIds: Array<Scalars['ID']>;
};


export type MutationReconcileInvoiceWithItemArgs = {
  reconcileInvoiceWithItemInput?: InputMaybe<ReconcileInvoiceWithItemInput>;
};


export type MutationReorderCategoriesArgs = {
  input: ReorderCategoriesInput;
};


export type MutationSendReceivingReportArgs = {
  invoiceId?: InputMaybe<Scalars['ID']>;
  userId?: InputMaybe<Scalars['ID']>;
};


export type MutationStartSyncArgs = {
  input: StartSyncInput;
};


export type MutationSubmitCreditRequestsArgs = {
  submitCreditRequestsInput?: InputMaybe<SubmitCreditRequestsInput>;
};


export type MutationSubmitFeedbackArgs = {
  submitFeedbackInput: SubmitFeedbackInput;
};


export type MutationSubmitOrderArgs = {
  submitOrderInput: SubmitOrderInput;
};


export type MutationToggleFavoriteArgs = {
  input: ToggleFavoriteInput;
};


export type MutationUpdateCatalogTemplateArgs = {
  input: UpdateCatalogTemplateInput;
};


export type MutationUpdateCategoryOrderArgs = {
  categoryId: Scalars['ID'];
  newOrder: Scalars['Int'];
  supplierId: Scalars['ID'];
};


export type MutationUpdateCreditArgs = {
  updateCreditInput: UpdateCreditInput;
};


export type MutationUpdateCreditRequestStatusArgs = {
  id: Scalars['ID'];
  status: Scalars['String'];
};


export type MutationUpdateDefaultInAccountArgs = {
  updateDefaultInAccountInput?: InputMaybe<UpdateDefaultInAccountInput>;
};


export type MutationUpdateEmployeeArgs = {
  input: UpdateEmployeeInput;
};


export type MutationUpdateGoalArgs = {
  updateGoalInput: UpdateGoalInput;
};


export type MutationUpdateInvoiceArgs = {
  updateInvoiceInput: UpdateInvoiceInput;
};


export type MutationUpdateItemInCartArgs = {
  updateItemInCartInput: UpdateItemInCartInput;
};


export type MutationUpdateOrderArgs = {
  updateOrderInput?: InputMaybe<UpdateOrderInput>;
};


export type MutationUpdatePromotionArgs = {
  input: UpdatePromotionInput;
};


export type MutationUpdateSupplierConfigArgs = {
  input: UpdateSupplierConfigInput;
};


export type MutationUpdateUserArgs = {
  user: UserInput;
};


export type MutationUpdateUserSuppliersArgs = {
  updateUserSuppliersInput?: InputMaybe<UpdateUserSuppliersInput>;
};


export type MutationUpsertAllCustomPricesArgs = {
  itemId: Scalars['ID'];
  overrideExistingPrices?: InputMaybe<Scalars['Boolean']>;
  price: Scalars['Float'];
  supplierId: Scalars['ID'];
  userId: Scalars['ID'];
};


export type MutationUpsertCategoryArgs = {
  categoryInput: CategoryInput;
  skipItems?: InputMaybe<Scalars['Boolean']>;
};


export type MutationUpsertCustomPricesArgs = {
  customPrices: Array<CustomPriceInput>;
};


export type MutationUpsertItemsArgs = {
  items: Array<ItemInput>;
  updateCategoryCustomPrices?: InputMaybe<Scalars['Boolean']>;
};


export type MutationUpsertRouteArgs = {
  routeInput: RouteInput;
};


export type MutationUpsertUomArgs = {
  input: UpsertUomInput;
};


export type MutationUpsertUomAllCustomPricesArgs = {
  itemId: Scalars['ID'];
  overrideExistingPrices?: InputMaybe<Scalars['Boolean']>;
  price: Scalars['Float'];
  supplierId: Scalars['ID'];
  userId: Scalars['ID'];
};

export type Order = {
  __typename?: 'Order';
  config?: Maybe<Scalars['JSONObject']>;
  customerDetails?: Maybe<User>;
  date_submitted?: Maybe<Scalars['Float']>;
  delivery_date?: Maybe<Scalars['Date']>;
  discount?: Maybe<Scalars['Float']>;
  id: Scalars['ID'];
  invoice?: Maybe<Invoice>;
  notes?: Maybe<Scalars['String']>;
  orderItems?: Maybe<Array<Maybe<CartItem>>>;
  orderName?: Maybe<Scalars['String']>;
  order_number?: Maybe<Scalars['Int']>;
  promotions?: Maybe<Array<Maybe<PromotionUsage>>>;
  sales_rep?: Maybe<SalesRep>;
  status?: Maybe<Scalars['String']>;
  subtotal: Scalars['Float'];
  supplier?: Maybe<Scalars['String']>;
  supplier_logo?: Maybe<Scalars['String']>;
  totalQuantity?: Maybe<Scalars['Int']>;
};

export type OrderBySupplier = {
  __typename?: 'OrderBySupplier';
  orderItems?: Maybe<Array<Maybe<CartItem>>>;
};

export type OrderItemInput = {
  item_id: Scalars['ID'];
  price: Scalars['Float'];
  quantity: Scalars['Int'];
};

export type OrderItemTotal = {
  __typename?: 'OrderItemTotal';
  id: Scalars['ID'];
  nacs_category?: Maybe<Scalars['String']>;
  name: Scalars['String'];
  next_day_presale_totals: Scalars['Int'];
  weekly_totals: Array<Scalars['Int']>;
};

export type OrderStatus = {
  __typename?: 'OrderStatus';
  delivering_date?: Maybe<Scalars['Float']>;
  delivery_date?: Maybe<Scalars['Float']>;
  id?: Maybe<Scalars['ID']>;
  name?: Maybe<Scalars['String']>;
  order_id?: Maybe<Scalars['ID']>;
  submission_date?: Maybe<Scalars['Float']>;
  supplier_id?: Maybe<Scalars['ID']>;
};

export enum Ordering {
  Asc = 'ASC',
  Desc = 'DESC'
}

export type OrdersBySupplier = {
  __typename?: 'OrdersBySupplier';
  orders: Array<Order>;
  totalCount: Scalars['Int'];
};

export type OrdersFiltersV2 = {
  deliveryDate?: InputMaybe<Scalars['Date']>;
  deliveryDateRange?: InputMaybe<Array<Scalars['Date']>>;
  driver?: InputMaybe<Scalars['String']>;
  employee_ids?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  ids?: InputMaybe<Array<Scalars['ID']>>;
  lastPaidDate?: InputMaybe<Scalars['Date']>;
  paidStatus?: InputMaybe<Scalars['String']>;
  perUserLimit?: InputMaybe<Scalars['Int']>;
  query?: InputMaybe<Scalars['String']>;
  routeIds?: InputMaybe<Array<Scalars['String']>>;
  signed?: InputMaybe<Scalars['Boolean']>;
  status?: InputMaybe<Scalars['String']>;
  userId?: InputMaybe<Scalars['String']>;
  userIds?: InputMaybe<Array<Scalars['String']>>;
};

export type OrdersInputV2 = {
  filters?: InputMaybe<OrdersFiltersV2>;
  pagination?: InputMaybe<PaginationInput>;
  sortBy?: InputMaybe<SortBy>;
  supplierId: Scalars['ID'];
};

export type OrdersOutputV2 = BaseOutput & {
  __typename?: 'OrdersOutputV2';
  orders: Array<Maybe<Order>>;
  totalCount: Scalars['Int'];
};

export type PaginationInput = {
  limit?: InputMaybe<Scalars['Int']>;
  offset?: InputMaybe<Scalars['Int']>;
};

export type Promotion = {
  __typename?: 'Promotion';
  active: Scalars['Boolean'];
  applies_to_all_items: Scalars['Boolean'];
  applies_to_all_users: Scalars['Boolean'];
  archived: Scalars['Boolean'];
  buy_quantity?: Maybe<Scalars['Int']>;
  created_at: Scalars['DateTime'];
  discount_amount?: Maybe<Scalars['Float']>;
  discount_percentage?: Maybe<Scalars['Float']>;
  end_date: Scalars['DateTime'];
  free_quantity?: Maybe<Scalars['Int']>;
  id: Scalars['ID'];
  items?: Maybe<Array<Item>>;
  max_uses_per_customer?: Maybe<Scalars['Int']>;
  min_order_amount?: Maybe<Scalars['Float']>;
  name: Scalars['String'];
  promotion_type: PromotionType;
  start_date: Scalars['DateTime'];
  supplier_id: Scalars['ID'];
  total_usage_limit?: Maybe<Scalars['Int']>;
  updated_at: Scalars['DateTime'];
  usage_count: Scalars['Int'];
  users?: Maybe<Array<User>>;
};

export type PromotionApplication = {
  __typename?: 'PromotionApplication';
  affected_items: Array<AffectedItem>;
  applied_promotions: Array<Promotion>;
  total_discount: Scalars['Float'];
};

export enum PromotionCode {
  BuyXGetDiscount = 'BUY_X_GET_DISCOUNT',
  BuyXGetPrice = 'BUY_X_GET_PRICE',
  BuyXGetYFree = 'BUY_X_GET_Y_FREE',
  FixedDiscount = 'FIXED_DISCOUNT',
  PercentageOff = 'PERCENTAGE_OFF'
}

export type PromotionFilters = {
  active?: InputMaybe<Scalars['Boolean']>;
  dateRange?: InputMaybe<Array<Scalars['DateTime']>>;
  ids?: InputMaybe<Array<Scalars['ID']>>;
  includeArchived?: InputMaybe<Scalars['Boolean']>;
  itemIds?: InputMaybe<Array<Scalars['ID']>>;
  name?: InputMaybe<Scalars['String']>;
  query?: InputMaybe<Scalars['String']>;
  type?: InputMaybe<Scalars['ID']>;
  userIds?: InputMaybe<Array<Scalars['ID']>>;
};

export type PromotionItemInput = {
  item_id: Scalars['ID'];
};

export type PromotionType = {
  __typename?: 'PromotionType';
  active: Scalars['Boolean'];
  code: PromotionCode;
  created_at: Scalars['DateTime'];
  description?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  name: Scalars['String'];
  updated_at: Scalars['DateTime'];
};

export type PromotionUsage = {
  __typename?: 'PromotionUsage';
  created_at: Scalars['DateTime'];
  id: Scalars['ID'];
  order_id: Scalars['ID'];
  promotion: Promotion;
  promotion_id: Scalars['ID'];
  used_at: Scalars['DateTime'];
  user_id: Scalars['ID'];
};

export type PromotionUserInput = {
  user_id: Scalars['ID'];
};

export type PushNotificationToken = {
  __typename?: 'PushNotificationToken';
  token: Scalars['String'];
  user_id: Scalars['ID'];
};

export type Query = {
  __typename?: 'Query';
  accounts?: Maybe<Array<Maybe<Account>>>;
  actionItems?: Maybe<Array<Maybe<ActionItem>>>;
  activityLog: ActivityLogOutput;
  allCustomPricesV2: AllCustomPricesOutputV2;
  balanceLink?: Maybe<BalanceLink>;
  brandSections: Array<Maybe<Supplier>>;
  brandSpotlights?: Maybe<Array<Maybe<BrandSpotlights>>>;
  calculatePromotions: PromotionApplication;
  carts: Array<Maybe<Cart>>;
  categories: Array<Maybe<BasicCategory>>;
  categoriesBySupplier?: Maybe<Array<Maybe<Category>>>;
  creditRequests?: Maybe<Array<Maybe<CreditRequest>>>;
  credits: Array<Maybe<Credit>>;
  customUOMPrices: Array<Maybe<CustomUomPricesByItem>>;
  customerGroups?: Maybe<Array<Maybe<CustomerGroup>>>;
  customerHiddenProducts: CustomerHiddenProductsOutput;
  cutoffTimes: Array<Maybe<CutoffTime>>;
  dashboardMetrics?: Maybe<DashboardMetrics>;
  employeesV2: EmployeesOutputV2;
  expectedOrders: Array<Maybe<Order>>;
  getBestSellers: GetBestSellersOutput;
  getCatalogTemplates: Array<Maybe<CatalogTemplate>>;
  getFavorites: Array<Item>;
  getSyncingStatus: SyncStatusResponse;
  goal?: Maybe<Goal>;
  goalPeriods: Array<GoalPeriodOption>;
  goals: GoalsResponse;
  groupPricesV2: GroupPricesOutputV2;
  invoice?: Maybe<InvoiceWithStatus>;
  invoiceItemMatch?: Maybe<InvoiceItemMatch>;
  invoiceItems: Array<Maybe<InvoiceItem>>;
  invoices?: Maybe<Array<Maybe<Invoice>>>;
  invoicesBySupplier: InvoicesBySupplier;
  itemUOMs: Array<Maybe<ItemUom>>;
  items: Array<Maybe<Item>>;
  itemsAvailableDuffl?: Maybe<Array<Maybe<ItemAvailable>>>;
  itemsByFilter: Array<Maybe<Item>>;
  itemsBySupplier: Array<Maybe<Item>>;
  itemsV2: ItemsOutputV2;
  orderBySupplier?: Maybe<OrderBySupplier>;
  orderItemTotals: Array<OrderItemTotal>;
  orderStatuses?: Maybe<Array<Maybe<OrderStatus>>>;
  orders: Array<Maybe<Order>>;
  ordersBySupplier: OrdersBySupplier;
  ordersV2: OrdersOutputV2;
  promotion?: Maybe<Promotion>;
  promotionTypes: Array<PromotionType>;
  promotions: Array<Promotion>;
  pushNotificationTokens?: Maybe<Array<Maybe<PushNotificationToken>>>;
  recommendations: Array<Maybe<Recommendation>>;
  routeTotals: Array<RouteTotal>;
  routes?: Maybe<Array<Maybe<Route>>>;
  routesBySupplier?: Maybe<Array<Maybe<Route>>>;
  routesV2: Array<Maybe<Route>>;
  sections: Array<Maybe<Section>>;
  spotlights?: Maybe<Array<Maybe<Supplier>>>;
  supplierConfig: SupplierConfig;
  suppliers: Array<Maybe<Supplier>>;
  tags: Array<Maybe<Tag>>;
  uoms: Array<Maybe<Uom>>;
  uploadedOrderCsv?: Maybe<UploadedOrderCsv>;
  users: Array<Maybe<User>>;
  usersByFilter?: Maybe<UsersByFilter>;
  usersOnTodaysRoutes: Array<Maybe<User>>;
  usersV2: UsersOutputV2;
};


export type QueryAccountsArgs = {
  businessId?: InputMaybe<Scalars['ID']>;
};


export type QueryActionItemsArgs = {
  getActionItemsInput?: InputMaybe<GetActionItemsInput>;
};


export type QueryActivityLogArgs = {
  activityLogInput: ActivityLogInput;
};


export type QueryAllCustomPricesV2Args = {
  allCustomPricesInput: AllCustomPricesInputV2;
};


export type QueryBalanceLinkArgs = {
  businessId?: InputMaybe<Scalars['ID']>;
};


export type QueryBrandSectionsArgs = {
  getSectionsInput?: InputMaybe<GetSectionsInput>;
};


export type QueryBrandSpotlightsArgs = {
  userId?: InputMaybe<Scalars['ID']>;
};


export type QueryCalculatePromotionsArgs = {
  items: Array<OrderItemInput>;
  supplier_id: Scalars['ID'];
  user_id: Scalars['ID'];
};


export type QueryCartsArgs = {
  getCartsInput: GetCartsInput;
};


export type QueryCategoriesArgs = {
  getCategoriesInput?: InputMaybe<GetCategoriesInput>;
};


export type QueryCategoriesBySupplierArgs = {
  getCategoriesBySupplierInput?: InputMaybe<GetCategoriesBySupplierInput>;
};


export type QueryCreditRequestsArgs = {
  orderId?: InputMaybe<Scalars['ID']>;
  supplierId?: InputMaybe<Scalars['ID']>;
  userId?: InputMaybe<Scalars['ID']>;
};


export type QueryCreditsArgs = {
  getCreditsInput?: InputMaybe<GetCreditsInput>;
};


export type QueryCustomUomPricesArgs = {
  itemId: Scalars['ID'];
  supplierId: Scalars['ID'];
  userId: Scalars['ID'];
};


export type QueryCustomerGroupsArgs = {
  supplierId?: InputMaybe<Scalars['ID']>;
};


export type QueryCustomerHiddenProductsArgs = {
  customerHiddenProductsInput: CustomerHiddenProductsInput;
};


export type QueryCutoffTimesArgs = {
  getCutoffTimesInput?: InputMaybe<GetCutoffTimesInput>;
};


export type QueryDashboardMetricsArgs = {
  dateRange?: InputMaybe<Array<InputMaybe<Scalars['DateTime']>>>;
  driver?: InputMaybe<Scalars['String']>;
  duration?: InputMaybe<Scalars['Int']>;
  routeIds?: InputMaybe<Array<Scalars['ID']>>;
  serviceType?: InputMaybe<Scalars['String']>;
  supplier_id: Scalars['ID'];
};


export type QueryEmployeesV2Args = {
  employeesInput: EmployeesInputV2;
};


export type QueryExpectedOrdersArgs = {
  date?: InputMaybe<Scalars['Date']>;
  supplierId: Scalars['ID'];
};


export type QueryGetBestSellersArgs = {
  getBestSellersInput: GetBestSellersInput;
};


export type QueryGetCatalogTemplatesArgs = {
  getCatalogTemplatesInput: GetCatalogTemplatesInput;
};


export type QueryGetFavoritesArgs = {
  input: GetFavoritesInput;
};


export type QueryGetSyncingStatusArgs = {
  supplierId: Scalars['ID'];
};


export type QueryGoalArgs = {
  id: Scalars['ID'];
};


export type QueryGoalPeriodsArgs = {
  goalId: Scalars['ID'];
};


export type QueryGoalsArgs = {
  goalsInput: GoalsInput;
};


export type QueryGroupPricesV2Args = {
  groupPricesInput: GroupPricesInputV2;
};


export type QueryInvoiceArgs = {
  orderId: Scalars['ID'];
};


export type QueryInvoiceItemMatchArgs = {
  getInvoiceItemMatchInput?: InputMaybe<GetInvoiceItemMatchInput>;
};


export type QueryInvoiceItemsArgs = {
  invoiceId?: InputMaybe<Scalars['ID']>;
  orderId?: InputMaybe<Scalars['ID']>;
};


export type QueryInvoicesArgs = {
  getInvoicesInput?: InputMaybe<GetInvoicesInput>;
};


export type QueryInvoicesBySupplierArgs = {
  getInvoicesBySupplierInput?: InputMaybe<GetInvoicesBySupplierInput>;
};


export type QueryItemUoMsArgs = {
  itemId: Scalars['ID'];
  supplierId: Scalars['ID'];
};


export type QueryItemsArgs = {
  getItemsInput?: InputMaybe<GetItemsInput>;
};


export type QueryItemsAvailableDufflArgs = {
  businessId?: InputMaybe<Scalars['ID']>;
  items?: InputMaybe<Array<InputMaybe<ItemAvailableInput>>>;
  message?: InputMaybe<Scalars['String']>;
  supplier?: InputMaybe<Scalars['String']>;
};


export type QueryItemsByFilterArgs = {
  getItemsByFilterInput?: InputMaybe<GetItemsByFilterInput>;
};


export type QueryItemsBySupplierArgs = {
  getItemsBySupplierInput?: InputMaybe<GetItemsBySupplierInput>;
};


export type QueryItemsV2Args = {
  itemsInput: ItemsInputV2;
};


export type QueryOrderBySupplierArgs = {
  orderId?: InputMaybe<Scalars['ID']>;
  supplierId?: InputMaybe<Scalars['ID']>;
};


export type QueryOrderItemTotalsArgs = {
  getOrderItemTotalsInput?: InputMaybe<GetOrderItemTotalsInput>;
};


export type QueryOrderStatusesArgs = {
  orderId?: InputMaybe<Scalars['ID']>;
};


export type QueryOrdersArgs = {
  getOrdersInput?: InputMaybe<GetOrdersInput>;
};


export type QueryOrdersBySupplierArgs = {
  getOrdersBySupplierInput?: InputMaybe<GetOrdersBySupplierInput>;
};


export type QueryOrdersV2Args = {
  ordersInput: OrdersInputV2;
};


export type QueryPromotionArgs = {
  id: Scalars['ID'];
  supplierId: Scalars['ID'];
};


export type QueryPromotionsArgs = {
  input?: InputMaybe<GetPromotionsInput>;
};


export type QueryPushNotificationTokensArgs = {
  getPushNotificationTokensInput?: InputMaybe<GetPushNotificationTokensInput>;
};


export type QueryRecommendationsArgs = {
  getRecommendationsInput?: InputMaybe<GetRecommendationsInput>;
};


export type QueryRouteTotalsArgs = {
  getRouteTotalsInput?: InputMaybe<GetRouteTotalsInput>;
};


export type QueryRoutesArgs = {
  getRoutesInput?: InputMaybe<GetRoutesInput>;
};


export type QueryRoutesBySupplierArgs = {
  getRoutesBySupplierInput?: InputMaybe<GetRoutesBySupplierInput>;
};


export type QueryRoutesV2Args = {
  routesInput: RoutesInputV2;
};


export type QuerySectionsArgs = {
  getSectionsInput?: InputMaybe<GetSectionsInput>;
};


export type QuerySpotlightsArgs = {
  userId?: InputMaybe<Scalars['ID']>;
};


export type QuerySupplierConfigArgs = {
  supplierId: Scalars['ID'];
};


export type QuerySuppliersArgs = {
  getSuppliersInput?: InputMaybe<GetSuppliersInput>;
};


export type QueryTagsArgs = {
  getTagsInput?: InputMaybe<GetTagsInput>;
};


export type QueryUomsArgs = {
  supplierId: Scalars['ID'];
};


export type QueryUploadedOrderCsvArgs = {
  embedId?: InputMaybe<Scalars['ID']>;
  sheetId?: InputMaybe<Scalars['ID']>;
};


export type QueryUsersArgs = {
  getUsersInput?: InputMaybe<GetUsersInput>;
};


export type QueryUsersByFilterArgs = {
  getUsersByFilterInput?: InputMaybe<GetUsersByFilterInput>;
};


export type QueryUsersOnTodaysRoutesArgs = {
  supplierId: Scalars['ID'];
};


export type QueryUsersV2Args = {
  usersInput: UsersInputV2;
};

export type Recommendation = {
  __typename?: 'Recommendation';
  id: Scalars['ID'];
  is_trending?: Maybe<Scalars['Boolean']>;
  item?: Maybe<Item>;
  item_id: Scalars['ID'];
  num_store?: Maybe<Scalars['Int']>;
  quantity?: Maybe<Scalars['Int']>;
  user_id: Scalars['ID'];
};

export type ReconcileInvoiceWithItemInput = {
  checked_in?: InputMaybe<Scalars['Boolean']>;
  invoice_id?: InputMaybe<Scalars['ID']>;
  invoice_item_id?: InputMaybe<Scalars['ID']>;
  is_mispick?: InputMaybe<Scalars['Boolean']>;
  quantity?: InputMaybe<Scalars['Int']>;
};

export type ReorderCategoriesInput = {
  categoryOrders: Array<Scalars['ID']>;
  supplierId: Scalars['ID'];
};

export type Role = {
  __typename?: 'Role';
  description?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  name: Scalars['String'];
};

export type Route = {
  __typename?: 'Route';
  color: Scalars['String'];
  config?: Maybe<Scalars['JSONObject']>;
  day_of_week: Scalars['String'];
  driver?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  name: Scalars['String'];
  supplier_id: Scalars['ID'];
};

export type RouteInput = {
  color?: InputMaybe<Scalars['String']>;
  config?: InputMaybe<Scalars['JSONObject']>;
  day_of_week?: InputMaybe<Scalars['String']>;
  driver?: InputMaybe<Scalars['String']>;
  id?: InputMaybe<Scalars['ID']>;
  name?: InputMaybe<Scalars['String']>;
  supplier_id?: InputMaybe<Scalars['ID']>;
};

export type RouteTotal = {
  __typename?: 'RouteTotal';
  id: Scalars['ID'];
  name: Scalars['String'];
  weekly_totals: Array<Scalars['Int']>;
};

export type RoutesFiltersV2 = {
  routeId?: InputMaybe<Scalars['ID']>;
};

export type RoutesInputV2 = {
  filters?: InputMaybe<RoutesFiltersV2>;
  supplierId: Scalars['ID'];
};

export type SalesByRoute = {
  __typename?: 'SalesByRoute';
  number_of_stores: Scalars['Int'];
  rank: Scalars['Int'];
  route_name: Scalars['String'];
  total_sales_value: Scalars['Float'];
};

export type SalesRep = {
  __typename?: 'SalesRep';
  email?: Maybe<Scalars['String']>;
  id?: Maybe<Scalars['Int']>;
  name: Scalars['String'];
  source: Scalars['String'];
};

export type Section = {
  __typename?: 'Section';
  image?: Maybe<Scalars['String']>;
  items?: Maybe<Array<Maybe<Item>>>;
  name?: Maybe<Scalars['String']>;
  value?: Maybe<Scalars['String']>;
};

export type SortBy = {
  field: Scalars['String'];
  ordering: Ordering;
};

export type StartSyncInput = {
  supplierId: Scalars['ID'];
  types: Array<SyncType>;
};

export type SubCart = {
  __typename?: 'SubCart';
  cartItems?: Maybe<Array<Maybe<CartItem>>>;
  discount?: Maybe<Scalars['Float']>;
  minimum?: Maybe<Scalars['Int']>;
  supplier?: Maybe<Scalars['String']>;
};

export type SubmitCreditRequestsInput = {
  creditRequests?: InputMaybe<Array<InputMaybe<CreditRequestInput>>>;
  orderId?: InputMaybe<Scalars['ID']>;
  supplier: Scalars['String'];
  userId: Scalars['ID'];
};

export type SubmitFeedbackInput = {
  issues?: InputMaybe<Scalars['String']>;
  thumbsUp: Scalars['Boolean'];
  userId: Scalars['ID'];
};

export type SubmitOrderInput = {
  cartId: Scalars['ID'];
  config?: InputMaybe<Scalars['JSONObject']>;
  deliveryDate?: InputMaybe<Scalars['Date']>;
  discount?: InputMaybe<Scalars['Float']>;
  isCredit?: InputMaybe<Scalars['Boolean']>;
  notes?: InputMaybe<Scalars['String']>;
  supplier?: InputMaybe<Scalars['String']>;
  userId: Scalars['ID'];
};

export type Supplier = {
  __typename?: 'Supplier';
  address?: Maybe<Scalars['String']>;
  config?: Maybe<Scalars['JSONObject']>;
  email?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  itemsPreview?: Maybe<Array<Maybe<Item>>>;
  logo?: Maybe<Scalars['String']>;
  minimum?: Maybe<Scalars['Int']>;
  name: Scalars['String'];
  need_signup?: Maybe<Scalars['Boolean']>;
  orderCount?: Maybe<Scalars['Int']>;
  phone_number?: Maybe<Scalars['String']>;
  qb_realm_id?: Maybe<Scalars['String']>;
  spotlight_image?: Maybe<Scalars['String']>;
};

export type SupplierConfig = {
  __typename?: 'SupplierConfig';
  allow_image_upload?: Maybe<Scalars['Boolean']>;
  auto_set_delivery_date?: Maybe<Scalars['Boolean']>;
  default_dashboard_duration?: Maybe<Scalars['String']>;
  enable_bounced_check_tracking?: Maybe<Scalars['Boolean']>;
  enable_catalog_sharing?: Maybe<Scalars['Boolean']>;
  enable_minimum_pricing?: Maybe<Scalars['Boolean']>;
  exclude_canceled_orders?: Maybe<Scalars['Boolean']>;
  filter_orders_by_sales_rep?: Maybe<Scalars['Boolean']>;
  invoice_scale_width?: Maybe<Scalars['Float']>;
  is_dsd?: Maybe<Scalars['Boolean']>;
  pavilions_address?: Maybe<Scalars['String']>;
  pavilions_display_name?: Maybe<Scalars['String']>;
  promotions_enabled?: Maybe<Scalars['Boolean']>;
  qb_credit_memo_date_filter?: Maybe<Scalars['String']>;
  requires_delivery_date?: Maybe<Scalars['Boolean']>;
  send_back_in_stock_notification?: Maybe<Scalars['Boolean']>;
  send_daily_back_in_stock_notification?: Maybe<Scalars['Boolean']>;
  send_order_notifications?: Maybe<Scalars['Boolean']>;
  show_goals_tracking_feature?: Maybe<Scalars['Boolean']>;
  show_open_carts?: Maybe<Scalars['Boolean']>;
  show_order_tabs?: Maybe<Scalars['Boolean']>;
  show_profit_info?: Maybe<Scalars['Boolean']>;
  skip_qb_sync?: Maybe<Scalars['Boolean']>;
  sort_invoice_items?: Maybe<Scalars['Boolean']>;
  spotlight_ids?: Maybe<Array<Maybe<Scalars['Int']>>>;
  use_alltime_avg_calculation?: Maybe<Scalars['Boolean']>;
  use_custom_driver_field?: Maybe<Scalars['Boolean']>;
};

export enum SyncStatus {
  Completed = 'COMPLETED',
  Failed = 'FAILED',
  InProgress = 'IN_PROGRESS'
}

export type SyncStatusItem = {
  __typename?: 'SyncStatusItem';
  createdAt?: Maybe<Scalars['String']>;
  durationMs?: Maybe<Scalars['Int']>;
  endTime?: Maybe<Scalars['String']>;
  error?: Maybe<Scalars['String']>;
  lastSynced?: Maybe<Scalars['String']>;
  startTime?: Maybe<Scalars['String']>;
  status: Scalars['String'];
  type: SyncType;
  updatedAt?: Maybe<Scalars['String']>;
};

export type SyncStatusResponse = {
  __typename?: 'SyncStatusResponse';
  statuses: Array<SyncStatusItem>;
  supplierId: Scalars['ID'];
};

export enum SyncType {
  Customers = 'CUSTOMERS',
  Invoices = 'INVOICES',
  Items = 'ITEMS'
}

export type Tag = {
  __typename?: 'Tag';
  name?: Maybe<Scalars['String']>;
  value?: Maybe<Scalars['String']>;
};

export type ToggleFavoriteInput = {
  employeeId?: InputMaybe<Scalars['Int']>;
  itemId: Scalars['Int'];
  userId: Scalars['Int'];
};

export type ToggleFavoriteResponse = {
  __typename?: 'ToggleFavoriteResponse';
  isFavorited: Scalars['Boolean'];
  message?: Maybe<Scalars['String']>;
  success: Scalars['Boolean'];
};

export type Uom = {
  __typename?: 'UOM';
  archived?: Maybe<Scalars['Boolean']>;
  id: Scalars['ID'];
  name: Scalars['String'];
  supplier_id: Scalars['ID'];
};

export type UomInput = {
  name: Scalars['String'];
  price?: InputMaybe<Scalars['Float']>;
  quantity: Scalars['Int'];
  upc?: InputMaybe<Scalars['String']>;
};

export type UpdateCatalogTemplateInput = {
  config?: InputMaybe<CatalogTemplateConfigInput>;
  id: Scalars['ID'];
  name?: InputMaybe<Scalars['String']>;
  supplier_id: Scalars['ID'];
};

export type UpdateCreditInput = {
  archived?: InputMaybe<Scalars['Boolean']>;
  credit_items?: InputMaybe<Array<InputMaybe<CreditItemInput>>>;
  id: Scalars['Int'];
  images?: InputMaybe<Array<Scalars['String']>>;
  invoice_id?: InputMaybe<Scalars['Int']>;
  status?: InputMaybe<CreditStatus>;
  supplier_id: Scalars['Int'];
  user_id?: InputMaybe<Scalars['Int']>;
};

export type UpdateDefaultInAccountInput = {
  accountId: Scalars['ID'];
  isDefault?: InputMaybe<Scalars['Boolean']>;
  userId: Scalars['ID'];
};

export type UpdateEmployeeInput = {
  appAccess?: InputMaybe<Scalars['Boolean']>;
  archived?: InputMaybe<Scalars['Boolean']>;
  dashboardAccess?: InputMaybe<Scalars['Boolean']>;
  email?: InputMaybe<Scalars['String']>;
  id: Scalars['ID'];
  lastLogin?: InputMaybe<Scalars['DateTime']>;
  name?: InputMaybe<Scalars['String']>;
  password?: InputMaybe<Scalars['String']>;
  phone?: InputMaybe<Scalars['String']>;
  roleIds?: InputMaybe<Array<Scalars['ID']>>;
  routeIds?: InputMaybe<Array<Scalars['ID']>>;
  supplierId: Scalars['ID'];
};

export type UpdateGoalAssignmentInput = {
  employee_id?: InputMaybe<Scalars['ID']>;
  id?: InputMaybe<Scalars['ID']>;
  target_amount: Scalars['Float'];
};

export type UpdateGoalInput = {
  assignments?: InputMaybe<Array<UpdateGoalAssignmentInput>>;
  end_date?: InputMaybe<Scalars['Date']>;
  id: Scalars['ID'];
  is_active?: InputMaybe<Scalars['Boolean']>;
  name?: InputMaybe<Scalars['String']>;
  period?: InputMaybe<GoalPeriod>;
  start_date?: InputMaybe<Scalars['Date']>;
  target_amount?: InputMaybe<Scalars['Float']>;
  type?: InputMaybe<GoalType>;
};

export type UpdateInvoiceInput = {
  archived?: InputMaybe<Scalars['Boolean']>;
  config?: InputMaybe<Scalars['JSONObject']>;
  credit?: InputMaybe<Scalars['Float']>;
  date_created?: InputMaybe<Scalars['DateTime']>;
  discount?: InputMaybe<Scalars['Float']>;
  id: Scalars['ID'];
  invoice_id?: InputMaybe<Scalars['String']>;
  invoice_items?: InputMaybe<Array<InvoiceItemInput>>;
  notes?: InputMaybe<Scalars['String']>;
  order_id?: InputMaybe<Scalars['ID']>;
  order_number?: InputMaybe<Scalars['Int']>;
  paid?: InputMaybe<Scalars['Float']>;
  payment_method?: InputMaybe<Scalars['String']>;
  payment_status?: InputMaybe<Scalars['String']>;
  qb_id?: InputMaybe<Scalars['String']>;
  return_items?: InputMaybe<Scalars['Boolean']>;
  signature?: InputMaybe<Scalars['String']>;
  signature_name?: InputMaybe<Scalars['String']>;
  subtotal?: InputMaybe<Scalars['Float']>;
  supplier_id?: InputMaybe<Scalars['ID']>;
  total?: InputMaybe<Scalars['Float']>;
  updated_at?: InputMaybe<Scalars['DateTime']>;
  user_id?: InputMaybe<Scalars['ID']>;
};

export type UpdateItemInCartInput = {
  cartId: Scalars['ID'];
  customPrice?: InputMaybe<Scalars['Float']>;
  itemId: Scalars['ID'];
  itemUomId?: InputMaybe<Scalars['ID']>;
  notes?: InputMaybe<Scalars['String']>;
  quantity: Scalars['Int'];
  supplierId: Scalars['ID'];
  userId: Scalars['ID'];
};

export type UpdateOrderInput = {
  config?: InputMaybe<Scalars['JSONObject']>;
  deliveryDate?: InputMaybe<Scalars['Date']>;
  invoice?: InputMaybe<UpdateInvoiceInput>;
  netTermsDays?: InputMaybe<Scalars['Int']>;
  notes?: InputMaybe<Scalars['String']>;
  orderId: Scalars['ID'];
  orderItems?: InputMaybe<Array<InputMaybe<UpdateOrderItemInput>>>;
  orderName?: InputMaybe<Scalars['String']>;
  status?: InputMaybe<Scalars['String']>;
  subtotal?: InputMaybe<Scalars['Float']>;
  supplierId?: InputMaybe<Scalars['ID']>;
  userId: Scalars['ID'];
};

export type UpdateOrderItemInput = {
  id: Scalars['ID'];
  itemUomId?: InputMaybe<Scalars['ID']>;
  notes?: InputMaybe<Scalars['String']>;
  price_purchased_at: Scalars['Float'];
  quantity: Scalars['Int'];
};

export type UpdatePromotionInput = {
  active?: InputMaybe<Scalars['Boolean']>;
  applies_to_all_items?: InputMaybe<Scalars['Boolean']>;
  applies_to_all_users?: InputMaybe<Scalars['Boolean']>;
  archived?: InputMaybe<Scalars['Boolean']>;
  buy_quantity?: InputMaybe<Scalars['Int']>;
  discount_amount?: InputMaybe<Scalars['Float']>;
  discount_percentage?: InputMaybe<Scalars['Float']>;
  end_date?: InputMaybe<Scalars['DateTime']>;
  free_quantity?: InputMaybe<Scalars['Int']>;
  id: Scalars['ID'];
  items?: InputMaybe<Array<PromotionItemInput>>;
  max_uses_per_customer?: InputMaybe<Scalars['Int']>;
  min_order_amount?: InputMaybe<Scalars['Float']>;
  name?: InputMaybe<Scalars['String']>;
  promotion_type?: InputMaybe<Scalars['ID']>;
  start_date?: InputMaybe<Scalars['DateTime']>;
  supplier_id: Scalars['ID'];
  total_usage_limit?: InputMaybe<Scalars['Int']>;
  users?: InputMaybe<Array<PromotionUserInput>>;
};

export type UpdateSupplierConfigInput = {
  show_order_tabs?: InputMaybe<Scalars['Boolean']>;
  supplierId: Scalars['ID'];
};

export type UpdateUserSuppliersInput = {
  suppliers?: InputMaybe<Array<InputMaybe<UserSupplierInput>>>;
};

export type UploadedOrderCsv = {
  __typename?: 'UploadedOrderCsv';
  download_url?: Maybe<Scalars['String']>;
};

export type UpsertUomInput = {
  item_id: Scalars['ID'];
  supplier_id: Scalars['ID'];
  uoms: Array<UomInput>;
};

export type User = {
  __typename?: 'User';
  address?: Maybe<Scalars['String']>;
  approved?: Maybe<Scalars['Boolean']>;
  archived?: Maybe<Scalars['Boolean']>;
  config?: Maybe<Scalars['JSONObject']>;
  contact_email?: Maybe<Scalars['String']>;
  created_at?: Maybe<Scalars['DateTime']>;
  created_by?: Maybe<Scalars['String']>;
  custom_prices?: Maybe<Array<Maybe<CustomPriceWithoutUserId>>>;
  custom_uom_prices?: Maybe<Array<Maybe<CustomUomPricesByItem>>>;
  delivery_window?: Maybe<DeliveryWindow>;
  driver?: Maybe<Scalars['Boolean']>;
  ein?: Maybe<Scalars['String']>;
  email?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  last_order_date?: Maybe<Scalars['DateTime']>;
  name?: Maybe<Scalars['String']>;
  net_terms_days?: Maybe<Scalars['Int']>;
  open_cart?: Maybe<Cart>;
  phone_number?: Maybe<Scalars['String']>;
  qb_id?: Maybe<Scalars['String']>;
  route_id?: Maybe<Scalars['ID']>;
  store_group?: Maybe<Scalars['String']>;
  supplier_beta?: Maybe<Scalars['Boolean']>;
  suppliers?: Maybe<Array<Maybe<Supplier>>>;
  unpaid_balance?: Maybe<Scalars['Float']>;
  updated_at?: Maybe<Scalars['DateTime']>;
  user_name?: Maybe<Scalars['String']>;
};

export type UserCustomPrices = {
  __typename?: 'UserCustomPrices';
  prices?: Maybe<Array<Maybe<CustomPriceWithoutUserId>>>;
  userId: Scalars['ID'];
};

export type UserInput = {
  address?: InputMaybe<Scalars['String']>;
  approved?: InputMaybe<Scalars['Boolean']>;
  config?: InputMaybe<Scalars['JSONObject']>;
  contact_email?: InputMaybe<Scalars['String']>;
  custom_prices?: InputMaybe<Array<InputMaybe<CustomPriceWithoutUserIdInput>>>;
  custom_uom_prices?: InputMaybe<Array<InputMaybe<CustomUomPriceInput>>>;
  delivery_window?: InputMaybe<DeliveryWindowInput>;
  email?: InputMaybe<Scalars['String']>;
  hidden_products?: InputMaybe<Array<Scalars['ID']>>;
  id: Scalars['ID'];
  name?: InputMaybe<Scalars['String']>;
  net_terms_days?: InputMaybe<Scalars['Int']>;
  phone_number?: InputMaybe<Scalars['String']>;
  qb_id?: InputMaybe<Scalars['String']>;
  route_id?: InputMaybe<Scalars['ID']>;
  store_group?: InputMaybe<Scalars['String']>;
  supplier_beta?: InputMaybe<Scalars['Boolean']>;
  updated_at?: InputMaybe<Scalars['DateTime']>;
  user_name?: InputMaybe<Scalars['String']>;
};

export type UserSupplierInput = {
  cutoffDay?: InputMaybe<Scalars['String']>;
  cutoffTime?: InputMaybe<Scalars['String']>;
  daysToDelivery?: InputMaybe<Scalars['Int']>;
  deliveryDay?: InputMaybe<Scalars['String']>;
  deliveryTime?: InputMaybe<Scalars['String']>;
  id?: InputMaybe<Scalars['String']>;
};

export type UsersByFilter = {
  __typename?: 'UsersByFilter';
  totalCount?: Maybe<Scalars['Float']>;
  users?: Maybe<Array<Maybe<User>>>;
};

export type UsersFiltersV2 = {
  active?: InputMaybe<Scalars['Boolean']>;
  drivers?: InputMaybe<Array<Scalars['String']>>;
  ids?: InputMaybe<Array<Scalars['ID']>>;
  includeUnassigned?: InputMaybe<Scalars['Boolean']>;
  routeIds?: InputMaybe<Array<Scalars['ID']>>;
  searchTerm?: InputMaybe<Scalars['String']>;
};

export type UsersInputV2 = {
  filters?: InputMaybe<UsersFiltersV2>;
  includeCustomPrices?: InputMaybe<Scalars['Boolean']>;
  pagination?: InputMaybe<PaginationInput>;
  sortBy?: InputMaybe<SortBy>;
  supplierId?: InputMaybe<Scalars['ID']>;
};

export type UsersOutputV2 = BaseOutput & {
  __typename?: 'UsersOutputV2';
  totalCount: Scalars['Int'];
  users: Array<Maybe<User>>;
};



export type ResolverTypeWrapper<T> = Promise<T> | T;


export type ResolverWithResolve<TResult, TParent, TContext, TArgs> = {
  resolve: ResolverFn<TResult, TParent, TContext, TArgs>;
};
export type Resolver<TResult, TParent = {}, TContext = {}, TArgs = {}> = ResolverFn<TResult, TParent, TContext, TArgs> | ResolverWithResolve<TResult, TParent, TContext, TArgs>;

export type ResolverFn<TResult, TParent, TContext, TArgs> = (
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => Promise<TResult> | TResult;

export type SubscriptionSubscribeFn<TResult, TParent, TContext, TArgs> = (
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => AsyncIterable<TResult> | Promise<AsyncIterable<TResult>>;

export type SubscriptionResolveFn<TResult, TParent, TContext, TArgs> = (
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => TResult | Promise<TResult>;

export interface SubscriptionSubscriberObject<TResult, TKey extends string, TParent, TContext, TArgs> {
  subscribe: SubscriptionSubscribeFn<{ [key in TKey]: TResult }, TParent, TContext, TArgs>;
  resolve?: SubscriptionResolveFn<TResult, { [key in TKey]: TResult }, TContext, TArgs>;
}

export interface SubscriptionResolverObject<TResult, TParent, TContext, TArgs> {
  subscribe: SubscriptionSubscribeFn<any, TParent, TContext, TArgs>;
  resolve: SubscriptionResolveFn<TResult, any, TContext, TArgs>;
}

export type SubscriptionObject<TResult, TKey extends string, TParent, TContext, TArgs> =
  | SubscriptionSubscriberObject<TResult, TKey, TParent, TContext, TArgs>
  | SubscriptionResolverObject<TResult, TParent, TContext, TArgs>;

export type SubscriptionResolver<TResult, TKey extends string, TParent = {}, TContext = {}, TArgs = {}> =
  | ((...args: any[]) => SubscriptionObject<TResult, TKey, TParent, TContext, TArgs>)
  | SubscriptionObject<TResult, TKey, TParent, TContext, TArgs>;

export type TypeResolveFn<TTypes, TParent = {}, TContext = {}> = (
  parent: TParent,
  context: TContext,
  info: GraphQLResolveInfo
) => Maybe<TTypes> | Promise<Maybe<TTypes>>;

export type IsTypeOfResolverFn<T = {}, TContext = {}> = (obj: T, context: TContext, info: GraphQLResolveInfo) => boolean | Promise<boolean>;

export type NextResolverFn<T> = () => Promise<T>;

export type DirectiveResolverFn<TResult = {}, TParent = {}, TContext = {}, TArgs = {}> = (
  next: NextResolverFn<TResult>,
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => TResult | Promise<TResult>;

/** Mapping between all available schema types and the resolvers types */
export type ResolversTypes = {
  Account: ResolverTypeWrapper<Account>;
  ActionItem: ResolverTypeWrapper<ActionItem>;
  ActivityLog: ResolverTypeWrapper<ActivityLog>;
  ActivityLogFilters: ActivityLogFilters;
  ActivityLogInput: ActivityLogInput;
  ActivityLogOutput: ResolverTypeWrapper<ActivityLogOutput>;
  ActivityLogType: ActivityLogType;
  AddInvoiceInput: AddInvoiceInput;
  AddInvoiceItemsInput: AddInvoiceItemsInput;
  AddItemToCartInput: AddItemToCartInput;
  AddPushNotificationTokenInput: AddPushNotificationTokenInput;
  AffectedItem: ResolverTypeWrapper<AffectedItem>;
  AllCustomPricesInputV2: AllCustomPricesInputV2;
  AllCustomPricesOutputV2: ResolverTypeWrapper<AllCustomPricesOutputV2>;
  BalanceBankAccount: ResolverTypeWrapper<BalanceBankAccount>;
  BalanceCreditCard: ResolverTypeWrapper<BalanceCreditCard>;
  BalanceLink: ResolverTypeWrapper<BalanceLink>;
  BaseOutput: ResolversTypes['AllCustomPricesOutputV2'] | ResolversTypes['EmployeesOutputV2'] | ResolversTypes['GroupPricesOutputV2'] | ResolversTypes['ItemsOutputV2'] | ResolversTypes['OrdersOutputV2'] | ResolversTypes['UsersOutputV2'];
  BasicCategory: ResolverTypeWrapper<BasicCategory>;
  Boolean: ResolverTypeWrapper<Scalars['Boolean']>;
  BrandSpotlights: ResolverTypeWrapper<BrandSpotlights>;
  CaptureTransactionInput: CaptureTransactionInput;
  Cart: ResolverTypeWrapper<Cart>;
  CartItem: ResolverTypeWrapper<CartItem>;
  CatalogDisplayOptionsInput: CatalogDisplayOptionsInput;
  CatalogTemplate: ResolverTypeWrapper<CatalogTemplate>;
  CatalogTemplateConfigInput: CatalogTemplateConfigInput;
  CatalogTemplateSort: CatalogTemplateSort;
  Category: ResolverTypeWrapper<Category>;
  CategoryInput: CategoryInput;
  CreateActivityLogInput: CreateActivityLogInput;
  CreateBalanceTransactionInput: CreateBalanceTransactionInput;
  CreateCartInput: CreateCartInput;
  CreateCatalogTemplateInput: CreateCatalogTemplateInput;
  CreateCreditInput: CreateCreditInput;
  CreateEmployeeInput: CreateEmployeeInput;
  CreateGoalAssignmentInput: CreateGoalAssignmentInput;
  CreateGoalInput: CreateGoalInput;
  CreateOrderInput: CreateOrderInput;
  CreatePromotionInput: CreatePromotionInput;
  CreateSupplierConfigInput: CreateSupplierConfigInput;
  CreateSupplierInput: CreateSupplierInput;
  CreateTable: CreateTable;
  CreateUserInput: CreateUserInput;
  Credit: ResolverTypeWrapper<Credit>;
  CreditItem: ResolverTypeWrapper<CreditItem>;
  CreditItemInput: CreditItemInput;
  CreditReason: CreditReason;
  CreditRequest: ResolverTypeWrapper<CreditRequest>;
  CreditRequestInput: CreditRequestInput;
  CreditStatus: CreditStatus;
  CustomPrice: ResolverTypeWrapper<CustomPrice>;
  CustomPriceInput: CustomPriceInput;
  CustomPriceWithoutUserId: ResolverTypeWrapper<CustomPriceWithoutUserId>;
  CustomPriceWithoutUserIdInput: CustomPriceWithoutUserIdInput;
  CustomUOMPrice: ResolverTypeWrapper<CustomUomPrice>;
  CustomUOMPriceInput: CustomUomPriceInput;
  CustomUOMPricesByItem: ResolverTypeWrapper<CustomUomPricesByItem>;
  CustomerGroup: ResolverTypeWrapper<CustomerGroup>;
  CustomerHiddenProduct: ResolverTypeWrapper<CustomerHiddenProduct>;
  CustomerHiddenProductsInput: CustomerHiddenProductsInput;
  CustomerHiddenProductsOutput: ResolverTypeWrapper<CustomerHiddenProductsOutput>;
  CutoffTime: ResolverTypeWrapper<CutoffTime>;
  DashboardMetricBestSeller: ResolverTypeWrapper<DashboardMetricBestSeller>;
  DashboardMetricBrandBreakdown: ResolverTypeWrapper<DashboardMetricBrandBreakdown>;
  DashboardMetricType: DashboardMetricType;
  DashboardMetrics: ResolverTypeWrapper<DashboardMetrics>;
  Date: ResolverTypeWrapper<Scalars['Date']>;
  DateTime: ResolverTypeWrapper<Scalars['DateTime']>;
  Deal: ResolverTypeWrapper<Deal>;
  DeleteCreditInput: DeleteCreditInput;
  DeliveryWindow: ResolverTypeWrapper<DeliveryWindow>;
  DeliveryWindowInput: DeliveryWindowInput;
  Employee: ResolverTypeWrapper<Employee>;
  EmployeesFiltersV2: EmployeesFiltersV2;
  EmployeesInputV2: EmployeesInputV2;
  EmployeesOutputV2: ResolverTypeWrapper<EmployeesOutputV2>;
  Float: ResolverTypeWrapper<Scalars['Float']>;
  GetActionItemsInput: GetActionItemsInput;
  GetBestSellersFilters: GetBestSellersFilters;
  GetBestSellersInput: GetBestSellersInput;
  GetBestSellersOutput: ResolverTypeWrapper<GetBestSellersOutput>;
  GetCartsInput: GetCartsInput;
  GetCatalogTemplatesInput: GetCatalogTemplatesInput;
  GetCategoriesBySupplierInput: GetCategoriesBySupplierInput;
  GetCategoriesInput: GetCategoriesInput;
  GetCreditsFilters: GetCreditsFilters;
  GetCreditsInput: GetCreditsInput;
  GetCutoffTimesInput: GetCutoffTimesInput;
  GetFavoritesInput: GetFavoritesInput;
  GetInvoiceItemMatchInput: GetInvoiceItemMatchInput;
  GetInvoicesBySupplierFilters: GetInvoicesBySupplierFilters;
  GetInvoicesBySupplierInput: GetInvoicesBySupplierInput;
  GetInvoicesInput: GetInvoicesInput;
  GetItemsByFilterInput: GetItemsByFilterInput;
  GetItemsBySupplierInput: GetItemsBySupplierInput;
  GetItemsInput: GetItemsInput;
  GetOrderItemTotalsInput: GetOrderItemTotalsInput;
  GetOrdersBySupplierFilters: GetOrdersBySupplierFilters;
  GetOrdersBySupplierInput: GetOrdersBySupplierInput;
  GetOrdersInput: GetOrdersInput;
  GetPromotionsInput: GetPromotionsInput;
  GetPushNotificationTokensInput: GetPushNotificationTokensInput;
  GetRecommendationsInput: GetRecommendationsInput;
  GetRouteTotalsInput: GetRouteTotalsInput;
  GetRoutesBySupplierInput: GetRoutesBySupplierInput;
  GetRoutesInput: GetRoutesInput;
  GetSalesByRoutesFilters: GetSalesByRoutesFilters;
  GetSalesByRoutesInput: GetSalesByRoutesInput;
  GetSalesByRoutesOutput: ResolverTypeWrapper<GetSalesByRoutesOutput>;
  GetSectionsInput: GetSectionsInput;
  GetSuppliersInput: GetSuppliersInput;
  GetTagsInput: GetTagsInput;
  GetUsersByFilterInput: GetUsersByFilterInput;
  GetUsersFilters: GetUsersFilters;
  GetUsersInput: GetUsersInput;
  Goal: ResolverTypeWrapper<Goal>;
  GoalAssignment: ResolverTypeWrapper<GoalAssignment>;
  GoalPeriod: GoalPeriod;
  GoalPeriodOption: ResolverTypeWrapper<GoalPeriodOption>;
  GoalPeriodSelection: GoalPeriodSelection;
  GoalStatus: GoalStatus;
  GoalType: GoalType;
  GoalsFilters: GoalsFilters;
  GoalsInput: GoalsInput;
  GoalsResponse: ResolverTypeWrapper<GoalsResponse>;
  GroupPrices: ResolverTypeWrapper<GroupPrices>;
  GroupPricesInputV2: GroupPricesInputV2;
  GroupPricesOutputV2: ResolverTypeWrapper<GroupPricesOutputV2>;
  ID: ResolverTypeWrapper<Scalars['ID']>;
  Int: ResolverTypeWrapper<Scalars['Int']>;
  Invoice: ResolverTypeWrapper<Invoice>;
  InvoiceItem: ResolverTypeWrapper<InvoiceItem>;
  InvoiceItemInput: InvoiceItemInput;
  InvoiceItemMatch: ResolverTypeWrapper<InvoiceItemMatch>;
  InvoiceWithStatus: ResolverTypeWrapper<InvoiceWithStatus>;
  InvoicesBySupplier: ResolverTypeWrapper<InvoicesBySupplier>;
  Item: ResolverTypeWrapper<Item>;
  ItemAvailable: ResolverTypeWrapper<ItemAvailable>;
  ItemAvailableInput: ItemAvailableInput;
  ItemInput: ItemInput;
  ItemTag: ItemTag;
  ItemUOM: ResolverTypeWrapper<ItemUom>;
  ItemsFiltersV2: ItemsFiltersV2;
  ItemsInputV2: ItemsInputV2;
  ItemsOutputV2: ResolverTypeWrapper<ItemsOutputV2>;
  JSON: ResolverTypeWrapper<Scalars['JSON']>;
  JSONObject: ResolverTypeWrapper<Scalars['JSONObject']>;
  MatchStatus: MatchStatus;
  Mutation: ResolverTypeWrapper<{}>;
  Order: ResolverTypeWrapper<Order>;
  OrderBySupplier: ResolverTypeWrapper<OrderBySupplier>;
  OrderItemInput: OrderItemInput;
  OrderItemTotal: ResolverTypeWrapper<OrderItemTotal>;
  OrderStatus: ResolverTypeWrapper<OrderStatus>;
  Ordering: Ordering;
  OrdersBySupplier: ResolverTypeWrapper<OrdersBySupplier>;
  OrdersFiltersV2: OrdersFiltersV2;
  OrdersInputV2: OrdersInputV2;
  OrdersOutputV2: ResolverTypeWrapper<OrdersOutputV2>;
  PaginationInput: PaginationInput;
  Promotion: ResolverTypeWrapper<Promotion>;
  PromotionApplication: ResolverTypeWrapper<PromotionApplication>;
  PromotionCode: PromotionCode;
  PromotionFilters: PromotionFilters;
  PromotionItemInput: PromotionItemInput;
  PromotionType: ResolverTypeWrapper<PromotionType>;
  PromotionUsage: ResolverTypeWrapper<PromotionUsage>;
  PromotionUserInput: PromotionUserInput;
  PushNotificationToken: ResolverTypeWrapper<PushNotificationToken>;
  Query: ResolverTypeWrapper<{}>;
  Recommendation: ResolverTypeWrapper<Recommendation>;
  ReconcileInvoiceWithItemInput: ReconcileInvoiceWithItemInput;
  ReorderCategoriesInput: ReorderCategoriesInput;
  Role: ResolverTypeWrapper<Role>;
  Route: ResolverTypeWrapper<Route>;
  RouteInput: RouteInput;
  RouteTotal: ResolverTypeWrapper<RouteTotal>;
  RoutesFiltersV2: RoutesFiltersV2;
  RoutesInputV2: RoutesInputV2;
  SalesByRoute: ResolverTypeWrapper<SalesByRoute>;
  SalesRep: ResolverTypeWrapper<SalesRep>;
  Section: ResolverTypeWrapper<Section>;
  SortBy: SortBy;
  StartSyncInput: StartSyncInput;
  String: ResolverTypeWrapper<Scalars['String']>;
  SubCart: ResolverTypeWrapper<SubCart>;
  SubmitCreditRequestsInput: SubmitCreditRequestsInput;
  SubmitFeedbackInput: SubmitFeedbackInput;
  SubmitOrderInput: SubmitOrderInput;
  Supplier: ResolverTypeWrapper<Supplier>;
  SupplierConfig: ResolverTypeWrapper<SupplierConfig>;
  SyncStatus: SyncStatus;
  SyncStatusItem: ResolverTypeWrapper<SyncStatusItem>;
  SyncStatusResponse: ResolverTypeWrapper<SyncStatusResponse>;
  SyncType: SyncType;
  Tag: ResolverTypeWrapper<Tag>;
  Time: ResolverTypeWrapper<Scalars['Time']>;
  ToggleFavoriteInput: ToggleFavoriteInput;
  ToggleFavoriteResponse: ResolverTypeWrapper<ToggleFavoriteResponse>;
  UOM: ResolverTypeWrapper<Uom>;
  UOMInput: UomInput;
  UpdateCatalogTemplateInput: UpdateCatalogTemplateInput;
  UpdateCreditInput: UpdateCreditInput;
  UpdateDefaultInAccountInput: UpdateDefaultInAccountInput;
  UpdateEmployeeInput: UpdateEmployeeInput;
  UpdateGoalAssignmentInput: UpdateGoalAssignmentInput;
  UpdateGoalInput: UpdateGoalInput;
  UpdateInvoiceInput: UpdateInvoiceInput;
  UpdateItemInCartInput: UpdateItemInCartInput;
  UpdateOrderInput: UpdateOrderInput;
  UpdateOrderItemInput: UpdateOrderItemInput;
  UpdatePromotionInput: UpdatePromotionInput;
  UpdateSupplierConfigInput: UpdateSupplierConfigInput;
  UpdateUserSuppliersInput: UpdateUserSuppliersInput;
  UploadedOrderCsv: ResolverTypeWrapper<UploadedOrderCsv>;
  UpsertUOMInput: UpsertUomInput;
  User: ResolverTypeWrapper<User>;
  UserCustomPrices: ResolverTypeWrapper<UserCustomPrices>;
  UserInput: UserInput;
  UserSupplierInput: UserSupplierInput;
  UsersByFilter: ResolverTypeWrapper<UsersByFilter>;
  UsersFiltersV2: UsersFiltersV2;
  UsersInputV2: UsersInputV2;
  UsersOutputV2: ResolverTypeWrapper<UsersOutputV2>;
};

/** Mapping between all available schema types and the resolvers parents */
export type ResolversParentTypes = {
  Account: Account;
  ActionItem: ActionItem;
  ActivityLog: ActivityLog;
  ActivityLogFilters: ActivityLogFilters;
  ActivityLogInput: ActivityLogInput;
  ActivityLogOutput: ActivityLogOutput;
  AddInvoiceInput: AddInvoiceInput;
  AddInvoiceItemsInput: AddInvoiceItemsInput;
  AddItemToCartInput: AddItemToCartInput;
  AddPushNotificationTokenInput: AddPushNotificationTokenInput;
  AffectedItem: AffectedItem;
  AllCustomPricesInputV2: AllCustomPricesInputV2;
  AllCustomPricesOutputV2: AllCustomPricesOutputV2;
  BalanceBankAccount: BalanceBankAccount;
  BalanceCreditCard: BalanceCreditCard;
  BalanceLink: BalanceLink;
  BaseOutput: ResolversParentTypes['AllCustomPricesOutputV2'] | ResolversParentTypes['EmployeesOutputV2'] | ResolversParentTypes['GroupPricesOutputV2'] | ResolversParentTypes['ItemsOutputV2'] | ResolversParentTypes['OrdersOutputV2'] | ResolversParentTypes['UsersOutputV2'];
  BasicCategory: BasicCategory;
  Boolean: Scalars['Boolean'];
  BrandSpotlights: BrandSpotlights;
  CaptureTransactionInput: CaptureTransactionInput;
  Cart: Cart;
  CartItem: CartItem;
  CatalogDisplayOptionsInput: CatalogDisplayOptionsInput;
  CatalogTemplate: CatalogTemplate;
  CatalogTemplateConfigInput: CatalogTemplateConfigInput;
  Category: Category;
  CategoryInput: CategoryInput;
  CreateActivityLogInput: CreateActivityLogInput;
  CreateBalanceTransactionInput: CreateBalanceTransactionInput;
  CreateCartInput: CreateCartInput;
  CreateCatalogTemplateInput: CreateCatalogTemplateInput;
  CreateCreditInput: CreateCreditInput;
  CreateEmployeeInput: CreateEmployeeInput;
  CreateGoalAssignmentInput: CreateGoalAssignmentInput;
  CreateGoalInput: CreateGoalInput;
  CreateOrderInput: CreateOrderInput;
  CreatePromotionInput: CreatePromotionInput;
  CreateSupplierConfigInput: CreateSupplierConfigInput;
  CreateSupplierInput: CreateSupplierInput;
  CreateTable: CreateTable;
  CreateUserInput: CreateUserInput;
  Credit: Credit;
  CreditItem: CreditItem;
  CreditItemInput: CreditItemInput;
  CreditRequest: CreditRequest;
  CreditRequestInput: CreditRequestInput;
  CustomPrice: CustomPrice;
  CustomPriceInput: CustomPriceInput;
  CustomPriceWithoutUserId: CustomPriceWithoutUserId;
  CustomPriceWithoutUserIdInput: CustomPriceWithoutUserIdInput;
  CustomUOMPrice: CustomUomPrice;
  CustomUOMPriceInput: CustomUomPriceInput;
  CustomUOMPricesByItem: CustomUomPricesByItem;
  CustomerGroup: CustomerGroup;
  CustomerHiddenProduct: CustomerHiddenProduct;
  CustomerHiddenProductsInput: CustomerHiddenProductsInput;
  CustomerHiddenProductsOutput: CustomerHiddenProductsOutput;
  CutoffTime: CutoffTime;
  DashboardMetricBestSeller: DashboardMetricBestSeller;
  DashboardMetricBrandBreakdown: DashboardMetricBrandBreakdown;
  DashboardMetrics: DashboardMetrics;
  Date: Scalars['Date'];
  DateTime: Scalars['DateTime'];
  Deal: Deal;
  DeleteCreditInput: DeleteCreditInput;
  DeliveryWindow: DeliveryWindow;
  DeliveryWindowInput: DeliveryWindowInput;
  Employee: Employee;
  EmployeesFiltersV2: EmployeesFiltersV2;
  EmployeesInputV2: EmployeesInputV2;
  EmployeesOutputV2: EmployeesOutputV2;
  Float: Scalars['Float'];
  GetActionItemsInput: GetActionItemsInput;
  GetBestSellersFilters: GetBestSellersFilters;
  GetBestSellersInput: GetBestSellersInput;
  GetBestSellersOutput: GetBestSellersOutput;
  GetCartsInput: GetCartsInput;
  GetCatalogTemplatesInput: GetCatalogTemplatesInput;
  GetCategoriesBySupplierInput: GetCategoriesBySupplierInput;
  GetCategoriesInput: GetCategoriesInput;
  GetCreditsFilters: GetCreditsFilters;
  GetCreditsInput: GetCreditsInput;
  GetCutoffTimesInput: GetCutoffTimesInput;
  GetFavoritesInput: GetFavoritesInput;
  GetInvoiceItemMatchInput: GetInvoiceItemMatchInput;
  GetInvoicesBySupplierFilters: GetInvoicesBySupplierFilters;
  GetInvoicesBySupplierInput: GetInvoicesBySupplierInput;
  GetInvoicesInput: GetInvoicesInput;
  GetItemsByFilterInput: GetItemsByFilterInput;
  GetItemsBySupplierInput: GetItemsBySupplierInput;
  GetItemsInput: GetItemsInput;
  GetOrderItemTotalsInput: GetOrderItemTotalsInput;
  GetOrdersBySupplierFilters: GetOrdersBySupplierFilters;
  GetOrdersBySupplierInput: GetOrdersBySupplierInput;
  GetOrdersInput: GetOrdersInput;
  GetPromotionsInput: GetPromotionsInput;
  GetPushNotificationTokensInput: GetPushNotificationTokensInput;
  GetRecommendationsInput: GetRecommendationsInput;
  GetRouteTotalsInput: GetRouteTotalsInput;
  GetRoutesBySupplierInput: GetRoutesBySupplierInput;
  GetRoutesInput: GetRoutesInput;
  GetSalesByRoutesFilters: GetSalesByRoutesFilters;
  GetSalesByRoutesInput: GetSalesByRoutesInput;
  GetSalesByRoutesOutput: GetSalesByRoutesOutput;
  GetSectionsInput: GetSectionsInput;
  GetSuppliersInput: GetSuppliersInput;
  GetTagsInput: GetTagsInput;
  GetUsersByFilterInput: GetUsersByFilterInput;
  GetUsersFilters: GetUsersFilters;
  GetUsersInput: GetUsersInput;
  Goal: Goal;
  GoalAssignment: GoalAssignment;
  GoalPeriodOption: GoalPeriodOption;
  GoalPeriodSelection: GoalPeriodSelection;
  GoalsFilters: GoalsFilters;
  GoalsInput: GoalsInput;
  GoalsResponse: GoalsResponse;
  GroupPrices: GroupPrices;
  GroupPricesInputV2: GroupPricesInputV2;
  GroupPricesOutputV2: GroupPricesOutputV2;
  ID: Scalars['ID'];
  Int: Scalars['Int'];
  Invoice: Invoice;
  InvoiceItem: InvoiceItem;
  InvoiceItemInput: InvoiceItemInput;
  InvoiceItemMatch: InvoiceItemMatch;
  InvoiceWithStatus: InvoiceWithStatus;
  InvoicesBySupplier: InvoicesBySupplier;
  Item: Item;
  ItemAvailable: ItemAvailable;
  ItemAvailableInput: ItemAvailableInput;
  ItemInput: ItemInput;
  ItemUOM: ItemUom;
  ItemsFiltersV2: ItemsFiltersV2;
  ItemsInputV2: ItemsInputV2;
  ItemsOutputV2: ItemsOutputV2;
  JSON: Scalars['JSON'];
  JSONObject: Scalars['JSONObject'];
  Mutation: {};
  Order: Order;
  OrderBySupplier: OrderBySupplier;
  OrderItemInput: OrderItemInput;
  OrderItemTotal: OrderItemTotal;
  OrderStatus: OrderStatus;
  OrdersBySupplier: OrdersBySupplier;
  OrdersFiltersV2: OrdersFiltersV2;
  OrdersInputV2: OrdersInputV2;
  OrdersOutputV2: OrdersOutputV2;
  PaginationInput: PaginationInput;
  Promotion: Promotion;
  PromotionApplication: PromotionApplication;
  PromotionFilters: PromotionFilters;
  PromotionItemInput: PromotionItemInput;
  PromotionType: PromotionType;
  PromotionUsage: PromotionUsage;
  PromotionUserInput: PromotionUserInput;
  PushNotificationToken: PushNotificationToken;
  Query: {};
  Recommendation: Recommendation;
  ReconcileInvoiceWithItemInput: ReconcileInvoiceWithItemInput;
  ReorderCategoriesInput: ReorderCategoriesInput;
  Role: Role;
  Route: Route;
  RouteInput: RouteInput;
  RouteTotal: RouteTotal;
  RoutesFiltersV2: RoutesFiltersV2;
  RoutesInputV2: RoutesInputV2;
  SalesByRoute: SalesByRoute;
  SalesRep: SalesRep;
  Section: Section;
  SortBy: SortBy;
  StartSyncInput: StartSyncInput;
  String: Scalars['String'];
  SubCart: SubCart;
  SubmitCreditRequestsInput: SubmitCreditRequestsInput;
  SubmitFeedbackInput: SubmitFeedbackInput;
  SubmitOrderInput: SubmitOrderInput;
  Supplier: Supplier;
  SupplierConfig: SupplierConfig;
  SyncStatusItem: SyncStatusItem;
  SyncStatusResponse: SyncStatusResponse;
  Tag: Tag;
  Time: Scalars['Time'];
  ToggleFavoriteInput: ToggleFavoriteInput;
  ToggleFavoriteResponse: ToggleFavoriteResponse;
  UOM: Uom;
  UOMInput: UomInput;
  UpdateCatalogTemplateInput: UpdateCatalogTemplateInput;
  UpdateCreditInput: UpdateCreditInput;
  UpdateDefaultInAccountInput: UpdateDefaultInAccountInput;
  UpdateEmployeeInput: UpdateEmployeeInput;
  UpdateGoalAssignmentInput: UpdateGoalAssignmentInput;
  UpdateGoalInput: UpdateGoalInput;
  UpdateInvoiceInput: UpdateInvoiceInput;
  UpdateItemInCartInput: UpdateItemInCartInput;
  UpdateOrderInput: UpdateOrderInput;
  UpdateOrderItemInput: UpdateOrderItemInput;
  UpdatePromotionInput: UpdatePromotionInput;
  UpdateSupplierConfigInput: UpdateSupplierConfigInput;
  UpdateUserSuppliersInput: UpdateUserSuppliersInput;
  UploadedOrderCsv: UploadedOrderCsv;
  UpsertUOMInput: UpsertUomInput;
  User: User;
  UserCustomPrices: UserCustomPrices;
  UserInput: UserInput;
  UserSupplierInput: UserSupplierInput;
  UsersByFilter: UsersByFilter;
  UsersFiltersV2: UsersFiltersV2;
  UsersInputV2: UsersInputV2;
  UsersOutputV2: UsersOutputV2;
};

export type AccountResolvers<ContextType = any, ParentType extends ResolversParentTypes['Account'] = ResolversParentTypes['Account']> = {
  balanceBankAccount?: Resolver<Maybe<ResolversTypes['BalanceBankAccount']>, ParentType, ContextType>;
  balanceCreditCard?: Resolver<Maybe<ResolversTypes['BalanceCreditCard']>, ParentType, ContextType>;
  id?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  is_default?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  type?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type ActionItemResolvers<ContextType = any, ParentType extends ResolversParentTypes['ActionItem'] = ResolversParentTypes['ActionItem']> = {
  description?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  invoiceItem?: Resolver<Maybe<ResolversTypes['InvoiceItem']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type ActivityLogResolvers<ContextType = any, ParentType extends ResolversParentTypes['ActivityLog'] = ResolversParentTypes['ActivityLog']> = {
  created_at?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  employee_id?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  id?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  location?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  log_number?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  metadata?: Resolver<Maybe<ResolversTypes['JSONObject']>, ParentType, ContextType>;
  supplier_id?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  type?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  user_id?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type ActivityLogOutputResolvers<ContextType = any, ParentType extends ResolversParentTypes['ActivityLogOutput'] = ResolversParentTypes['ActivityLogOutput']> = {
  activityLog?: Resolver<Maybe<Array<Maybe<ResolversTypes['ActivityLog']>>>, ParentType, ContextType>;
  totalCount?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type AffectedItemResolvers<ContextType = any, ParentType extends ResolversParentTypes['AffectedItem'] = ResolversParentTypes['AffectedItem']> = {
  discount_amount?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  item_id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  promotion_id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type AllCustomPricesOutputV2Resolvers<ContextType = any, ParentType extends ResolversParentTypes['AllCustomPricesOutputV2'] = ResolversParentTypes['AllCustomPricesOutputV2']> = {
  totalCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  userCustomPrices?: Resolver<Maybe<Array<Maybe<ResolversTypes['UserCustomPrices']>>>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type BalanceBankAccountResolvers<ContextType = any, ParentType extends ResolversParentTypes['BalanceBankAccount'] = ResolversParentTypes['BalanceBankAccount']> = {
  accountName?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  accountNumberMask?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  institutionName?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type BalanceCreditCardResolvers<ContextType = any, ParentType extends ResolversParentTypes['BalanceCreditCard'] = ResolversParentTypes['BalanceCreditCard']> = {
  brand?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  expiredMonth?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  expiredYear?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  last4?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type BalanceLinkResolvers<ContextType = any, ParentType extends ResolversParentTypes['BalanceLink'] = ResolversParentTypes['BalanceLink']> = {
  link?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type BaseOutputResolvers<ContextType = any, ParentType extends ResolversParentTypes['BaseOutput'] = ResolversParentTypes['BaseOutput']> = {
  __resolveType: TypeResolveFn<'AllCustomPricesOutputV2' | 'EmployeesOutputV2' | 'GroupPricesOutputV2' | 'ItemsOutputV2' | 'OrdersOutputV2' | 'UsersOutputV2', ParentType, ContextType>;
  totalCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
};

export type BasicCategoryResolvers<ContextType = any, ParentType extends ResolversParentTypes['BasicCategory'] = ResolversParentTypes['BasicCategory']> = {
  image?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  value?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type BrandSpotlightsResolvers<ContextType = any, ParentType extends ResolversParentTypes['BrandSpotlights'] = ResolversParentTypes['BrandSpotlights']> = {
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  spotlight_image?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CartResolvers<ContextType = any, ParentType extends ResolversParentTypes['Cart'] = ResolversParentTypes['Cart']> = {
  cartItems?: Resolver<Maybe<Array<Maybe<ResolversTypes['CartItem']>>>, ParentType, ContextType>;
  created_at?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  is_open?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  subCarts?: Resolver<Maybe<Array<Maybe<ResolversTypes['SubCart']>>>, ParentType, ContextType>;
  subtotal?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  total_quantity?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  updated_at?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  userId?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CartItemResolvers<ContextType = any, ParentType extends ResolversParentTypes['CartItem'] = ResolversParentTypes['CartItem']> = {
  archived?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  cart_id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  cog_price?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  created_at?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  crv?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  custom_price?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  discounted_price?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  image?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  item_id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  item_uom_id?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  metadata?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  nacs_category?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  nacs_subcategory?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  notes?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  oos?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  price?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  price_purchased_at?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  qb_id?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  qoh?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  quantity?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  size?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  supplier?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  supplier_code?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  unit_size?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  uoms?: Resolver<Maybe<ResolversTypes['ItemUOM']>, ParentType, ContextType>;
  upc1?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  upc2?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  updated_at?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CatalogTemplateResolvers<ContextType = any, ParentType extends ResolversParentTypes['CatalogTemplate'] = ResolversParentTypes['CatalogTemplate']> = {
  config?: Resolver<ResolversTypes['JSON'], ParentType, ContextType>;
  created_at?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  pdf_path?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  supplier_id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  updated_at?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CategoryResolvers<ContextType = any, ParentType extends ResolversParentTypes['Category'] = ResolversParentTypes['Category']> = {
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  image?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  items?: Resolver<Maybe<Array<Maybe<ResolversTypes['Item']>>>, ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  ordering?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  supplier_id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CreditResolvers<ContextType = any, ParentType extends ResolversParentTypes['Credit'] = ResolversParentTypes['Credit']> = {
  archived?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  cash_amount?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  created_at?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  creditItems?: Resolver<Maybe<Array<Maybe<ResolversTypes['CreditItem']>>>, ParentType, ContextType>;
  credit_number?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  customerDetails?: Resolver<Maybe<ResolversTypes['User']>, ParentType, ContextType>;
  id?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  images?: Resolver<Maybe<Array<Maybe<ResolversTypes['String']>>>, ParentType, ContextType>;
  invoiceDetails?: Resolver<Maybe<ResolversTypes['Invoice']>, ParentType, ContextType>;
  invoice_id?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  order_id?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  order_number?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  status?: Resolver<ResolversTypes['CreditStatus'], ParentType, ContextType>;
  supplier_id?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  total?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  updated_at?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  user_id?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CreditItemResolvers<ContextType = any, ParentType extends ResolversParentTypes['CreditItem'] = ResolversParentTypes['CreditItem']> = {
  credit_id?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  id?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  invoice_item_id?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  item_id?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  item_snapshot?: Resolver<Maybe<ResolversTypes['Item']>, ParentType, ContextType>;
  note?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  quantity?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  reason?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  total_price?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  unit_price?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CreditRequestResolvers<ContextType = any, ParentType extends ResolversParentTypes['CreditRequest'] = ResolversParentTypes['CreditRequest']> = {
  customerDetails?: Resolver<Maybe<ResolversTypes['User']>, ParentType, ContextType>;
  damaged?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  expired?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  image?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  itemDetails?: Resolver<Maybe<ResolversTypes['Item']>, ParentType, ContextType>;
  item_id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  mispick?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  order_id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  price_purchased_at?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  quantity?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  status?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  supplier_id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  user_id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CustomPriceResolvers<ContextType = any, ParentType extends ResolversParentTypes['CustomPrice'] = ResolversParentTypes['CustomPrice']> = {
  item_id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  price?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  user_id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CustomPriceWithoutUserIdResolvers<ContextType = any, ParentType extends ResolversParentTypes['CustomPriceWithoutUserId'] = ResolversParentTypes['CustomPriceWithoutUserId']> = {
  item_id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  price?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CustomUomPriceResolvers<ContextType = any, ParentType extends ResolversParentTypes['CustomUOMPrice'] = ResolversParentTypes['CustomUOMPrice']> = {
  item_uom_id?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  price?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  uom_id?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  uom_name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  user_id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CustomUomPricesByItemResolvers<ContextType = any, ParentType extends ResolversParentTypes['CustomUOMPricesByItem'] = ResolversParentTypes['CustomUOMPricesByItem']> = {
  item_id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  uom_prices?: Resolver<Array<ResolversTypes['CustomUOMPrice']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CustomerGroupResolvers<ContextType = any, ParentType extends ResolversParentTypes['CustomerGroup'] = ResolversParentTypes['CustomerGroup']> = {
  active?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  count?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  group?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CustomerHiddenProductResolvers<ContextType = any, ParentType extends ResolversParentTypes['CustomerHiddenProduct'] = ResolversParentTypes['CustomerHiddenProduct']> = {
  itemId?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  supplierId?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  userId?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CustomerHiddenProductsOutputResolvers<ContextType = any, ParentType extends ResolversParentTypes['CustomerHiddenProductsOutput'] = ResolversParentTypes['CustomerHiddenProductsOutput']> = {
  items?: Resolver<Array<Maybe<ResolversTypes['CustomerHiddenProduct']>>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CutoffTimeResolvers<ContextType = any, ParentType extends ResolversParentTypes['CutoffTime'] = ResolversParentTypes['CutoffTime']> = {
  cutoffDay?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  cutoffTime?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  daysToDelivery?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  deliveryDay?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  deliveryTime?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  id?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  supplier?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  supplierInfo?: Resolver<Maybe<ResolversTypes['Supplier']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type DashboardMetricBestSellerResolvers<ContextType = any, ParentType extends ResolversParentTypes['DashboardMetricBestSeller'] = ResolversParentTypes['DashboardMetricBestSeller']> = {
  category?: Resolver<Maybe<ResolversTypes['Category']>, ParentType, ContextType>;
  image?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  quantity?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type DashboardMetricBrandBreakdownResolvers<ContextType = any, ParentType extends ResolversParentTypes['DashboardMetricBrandBreakdown'] = ResolversParentTypes['DashboardMetricBrandBreakdown']> = {
  brand?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  breakdown?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  quantity?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type DashboardMetricsResolvers<ContextType = any, ParentType extends ResolversParentTypes['DashboardMetrics'] = ResolversParentTypes['DashboardMetrics']> = {
  ACTIVE_BUYERS?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  AVG_ORDER_VALUE?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  BEST_SELLERS?: Resolver<Maybe<Array<Maybe<ResolversTypes['DashboardMetricBestSeller']>>>, ParentType, ContextType>;
  BRAND_BREAKDOWN?: Resolver<Maybe<Array<Maybe<ResolversTypes['DashboardMetricBrandBreakdown']>>>, ParentType, ContextType>;
  CASES_ORDERED?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  CREDIT_TOTAL?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  FIRST_ORDERS?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  GMV_TOTAL?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  NET_REVENUE?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  NUM_CANCELED_ORDERS?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  NUM_CONFIRMED_ORDERS?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  NUM_INVOICES?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  NUM_ORDERS?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  NUM_UNAPPROVED_CUSTOMERS?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  NUM_UNCONFIRMED_ORDERS?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  NUM_USERS?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  PROFIT_TOTAL?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  SALES_BY_ROUTES?: Resolver<Maybe<Array<Maybe<ResolversTypes['SalesByRoute']>>>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface DateScalarConfig extends GraphQLScalarTypeConfig<ResolversTypes['Date'], any> {
  name: 'Date';
}

export interface DateTimeScalarConfig extends GraphQLScalarTypeConfig<ResolversTypes['DateTime'], any> {
  name: 'DateTime';
}

export type DealResolvers<ContextType = any, ParentType extends ResolversParentTypes['Deal'] = ResolversParentTypes['Deal']> = {
  deal_id?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  discount?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  item_id?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  quantity?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  type?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type DeliveryWindowResolvers<ContextType = any, ParentType extends ResolversParentTypes['DeliveryWindow'] = ResolversParentTypes['DeliveryWindow']> = {
  days_of_week?: Resolver<Maybe<Array<Maybe<ResolversTypes['String']>>>, ParentType, ContextType>;
  end_time?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  start_time?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type EmployeeResolvers<ContextType = any, ParentType extends ResolversParentTypes['Employee'] = ResolversParentTypes['Employee']> = {
  app_access?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  archived?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  created_at?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  dashboard_access?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  email?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  last_login?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  phone?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  roles?: Resolver<Maybe<Array<ResolversTypes['Role']>>, ParentType, ContextType>;
  routes?: Resolver<Maybe<Array<ResolversTypes['Route']>>, ParentType, ContextType>;
  updated_at?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type EmployeesOutputV2Resolvers<ContextType = any, ParentType extends ResolversParentTypes['EmployeesOutputV2'] = ResolversParentTypes['EmployeesOutputV2']> = {
  employees?: Resolver<Array<ResolversTypes['Employee']>, ParentType, ContextType>;
  totalCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GetBestSellersOutputResolvers<ContextType = any, ParentType extends ResolversParentTypes['GetBestSellersOutput'] = ResolversParentTypes['GetBestSellersOutput']> = {
  bestSellers?: Resolver<Array<Maybe<ResolversTypes['DashboardMetricBestSeller']>>, ParentType, ContextType>;
  totalCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GetSalesByRoutesOutputResolvers<ContextType = any, ParentType extends ResolversParentTypes['GetSalesByRoutesOutput'] = ResolversParentTypes['GetSalesByRoutesOutput']> = {
  salesByRoutes?: Resolver<Array<Maybe<ResolversTypes['SalesByRoute']>>, ParentType, ContextType>;
  totalCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GoalResolvers<ContextType = any, ParentType extends ResolversParentTypes['Goal'] = ResolversParentTypes['Goal']> = {
  assignments?: Resolver<Array<ResolversTypes['GoalAssignment']>, ParentType, ContextType>;
  available_periods?: Resolver<Array<ResolversTypes['GoalPeriodOption']>, ParentType, ContextType>;
  created_at?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  end_date?: Resolver<Maybe<ResolversTypes['Date']>, ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  is_active?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  period?: Resolver<ResolversTypes['GoalPeriod'], ParentType, ContextType>;
  start_date?: Resolver<ResolversTypes['Date'], ParentType, ContextType>;
  status?: Resolver<ResolversTypes['GoalStatus'], ParentType, ContextType>;
  supplier_id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  target_amount?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  type?: Resolver<ResolversTypes['GoalType'], ParentType, ContextType>;
  updated_at?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GoalAssignmentResolvers<ContextType = any, ParentType extends ResolversParentTypes['GoalAssignment'] = ResolversParentTypes['GoalAssignment']> = {
  created_at?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  current_progress?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  employee?: Resolver<Maybe<ResolversTypes['Employee']>, ParentType, ContextType>;
  employee_id?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  goal_id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  percentage_complete?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  target_amount?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  updated_at?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GoalPeriodOptionResolvers<ContextType = any, ParentType extends ResolversParentTypes['GoalPeriodOption'] = ResolversParentTypes['GoalPeriodOption']> = {
  label?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  value?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GoalsResponseResolvers<ContextType = any, ParentType extends ResolversParentTypes['GoalsResponse'] = ResolversParentTypes['GoalsResponse']> = {
  goals?: Resolver<Array<ResolversTypes['Goal']>, ParentType, ContextType>;
  totalCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GroupPricesResolvers<ContextType = any, ParentType extends ResolversParentTypes['GroupPrices'] = ResolversParentTypes['GroupPrices']> = {
  group?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  prices?: Resolver<Maybe<Array<Maybe<ResolversTypes['CustomPriceWithoutUserId']>>>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type GroupPricesOutputV2Resolvers<ContextType = any, ParentType extends ResolversParentTypes['GroupPricesOutputV2'] = ResolversParentTypes['GroupPricesOutputV2']> = {
  groupPrices?: Resolver<Maybe<Array<Maybe<ResolversTypes['GroupPrices']>>>, ParentType, ContextType>;
  totalCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type InvoiceResolvers<ContextType = any, ParentType extends ResolversParentTypes['Invoice'] = ResolversParentTypes['Invoice']> = {
  archived?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  config?: Resolver<Maybe<ResolversTypes['JSONObject']>, ParentType, ContextType>;
  credit?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  customerDetails?: Resolver<Maybe<ResolversTypes['User']>, ParentType, ContextType>;
  date_created?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  date_received?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  discount?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  id?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  invoiceItems?: Resolver<Maybe<Array<Maybe<ResolversTypes['InvoiceItem']>>>, ParentType, ContextType>;
  invoice_id?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  notes?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  orderDetails?: Resolver<Maybe<ResolversTypes['Order']>, ParentType, ContextType>;
  order_id?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  order_number?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  paid?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  payment_method?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  payment_status?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  qb_id?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  signature?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  signature_name?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  subtotal?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  supplier_id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  total?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  updated_at?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type InvoiceItemResolvers<ContextType = any, ParentType extends ResolversParentTypes['InvoiceItem'] = ResolversParentTypes['InvoiceItem']> = {
  checked_in?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  checked_in_quantity?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  cog_price?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  invoice_id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  is_mispick?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  item_id?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  item_uom_id?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  metadata?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  price?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  qb_id?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  quantity?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  size?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  unit_size?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  uoms?: Resolver<Maybe<ResolversTypes['ItemUOM']>, ParentType, ContextType>;
  upc1?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  upc2?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  upc3?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  upc4?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type InvoiceItemMatchResolvers<ContextType = any, ParentType extends ResolversParentTypes['InvoiceItemMatch'] = ResolversParentTypes['InvoiceItemMatch']> = {
  itemMatches?: Resolver<Maybe<Array<Maybe<ResolversTypes['InvoiceItem']>>>, ParentType, ContextType>;
  matchStatus?: Resolver<Maybe<ResolversTypes['MatchStatus']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type InvoiceWithStatusResolvers<ContextType = any, ParentType extends ResolversParentTypes['InvoiceWithStatus'] = ResolversParentTypes['InvoiceWithStatus']> = {
  invoice?: Resolver<Maybe<ResolversTypes['Invoice']>, ParentType, ContextType>;
  processing?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type InvoicesBySupplierResolvers<ContextType = any, ParentType extends ResolversParentTypes['InvoicesBySupplier'] = ResolversParentTypes['InvoicesBySupplier']> = {
  invoices?: Resolver<Array<ResolversTypes['Invoice']>, ParentType, ContextType>;
  paymentTotals?: Resolver<ResolversTypes['JSONObject'], ParentType, ContextType>;
  totalCases?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  totalCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  totalCredit?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  totalProfit?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type ItemResolvers<ContextType = any, ParentType extends ResolversParentTypes['Item'] = ResolversParentTypes['Item']> = {
  archived?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  avg_cases_per_week?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  back_in_stock_date?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  cog_price?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  created_at?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  crv?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  discounted_price?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  image?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  img_sm?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  isFavorited?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  item_uom_id?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  last_ordered_date?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  local_item?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  metadata?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  min_sale_price?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  moq?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  nacs_category?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  nacs_subcategory?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  oos?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  outdated?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  price?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  promotions?: Resolver<Maybe<Array<Maybe<ResolversTypes['Promotion']>>>, ParentType, ContextType>;
  qb_id?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  qb_sync_token?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  qoh?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  qty_on_hand?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  related_items?: Resolver<Maybe<Array<Maybe<ResolversTypes['Item']>>>, ParentType, ContextType>;
  size?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  supplier?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  supplier_code?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  supplier_info?: Resolver<Maybe<ResolversTypes['Supplier']>, ParentType, ContextType>;
  tags?: Resolver<Maybe<Array<Maybe<ResolversTypes['ItemTag']>>>, ParentType, ContextType>;
  unit_size?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  uoms?: Resolver<Maybe<Array<Maybe<ResolversTypes['ItemUOM']>>>, ParentType, ContextType>;
  upc1?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  upc2?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  updated_at?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type ItemAvailableResolvers<ContextType = any, ParentType extends ResolversParentTypes['ItemAvailable'] = ResolversParentTypes['ItemAvailable']> = {
  item_id?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  mapped?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  quantity?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  supplier?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type ItemUomResolvers<ContextType = any, ParentType extends ResolversParentTypes['ItemUOM'] = ResolversParentTypes['ItemUOM']> = {
  archived?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  item_id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  item_uom_id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  price?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  quantity?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  supplier_id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  uom_id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  upc?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type ItemsOutputV2Resolvers<ContextType = any, ParentType extends ResolversParentTypes['ItemsOutputV2'] = ResolversParentTypes['ItemsOutputV2']> = {
  items?: Resolver<Array<Maybe<ResolversTypes['Item']>>, ParentType, ContextType>;
  totalCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface JsonScalarConfig extends GraphQLScalarTypeConfig<ResolversTypes['JSON'], any> {
  name: 'JSON';
}

export interface JsonObjectScalarConfig extends GraphQLScalarTypeConfig<ResolversTypes['JSONObject'], any> {
  name: 'JSONObject';
}

export type MutationResolvers<ContextType = any, ParentType extends ResolversParentTypes['Mutation'] = ResolversParentTypes['Mutation']> = {
  addInvoice?: Resolver<Maybe<ResolversTypes['Invoice']>, ParentType, ContextType, RequireFields<MutationAddInvoiceArgs, 'addInvoiceInput'>>;
  addInvoiceItems?: Resolver<Maybe<Array<Maybe<ResolversTypes['InvoiceItem']>>>, ParentType, ContextType, RequireFields<MutationAddInvoiceItemsArgs, 'addInvoiceItemsInput'>>;
  addPushNotificationToken?: Resolver<Maybe<ResolversTypes['PushNotificationToken']>, ParentType, ContextType, Partial<MutationAddPushNotificationTokenArgs>>;
  applyPromotion?: Resolver<ResolversTypes['Order'], ParentType, ContextType, RequireFields<MutationApplyPromotionArgs, 'order_id' | 'promotion_id' | 'supplier_id'>>;
  approveUser?: Resolver<Maybe<ResolversTypes['User']>, ParentType, ContextType, RequireFields<MutationApproveUserArgs, 'id'>>;
  captureTransaction?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType, Partial<MutationCaptureTransactionArgs>>;
  createAccountFromBalance?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType, RequireFields<MutationCreateAccountFromBalanceArgs, 'payload'>>;
  createBalanceTransaction?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType, RequireFields<MutationCreateBalanceTransactionArgs, 'createBalanceTransactionInput'>>;
  createCart?: Resolver<Maybe<ResolversTypes['Cart']>, ParentType, ContextType, RequireFields<MutationCreateCartArgs, 'cart'>>;
  createCatalogTemplate?: Resolver<ResolversTypes['CatalogTemplate'], ParentType, ContextType, RequireFields<MutationCreateCatalogTemplateArgs, 'input'>>;
  createCredit?: Resolver<Maybe<ResolversTypes['Credit']>, ParentType, ContextType, RequireFields<MutationCreateCreditArgs, 'createCreditInput'>>;
  createEmployee?: Resolver<Maybe<ResolversTypes['Employee']>, ParentType, ContextType, RequireFields<MutationCreateEmployeeArgs, 'input'>>;
  createGoal?: Resolver<ResolversTypes['Goal'], ParentType, ContextType, RequireFields<MutationCreateGoalArgs, 'createGoalInput'>>;
  createInvoice?: Resolver<Maybe<ResolversTypes['Invoice']>, ParentType, ContextType, RequireFields<MutationCreateInvoiceArgs, 'orderId' | 'supplierId'>>;
  createOrder?: Resolver<Maybe<ResolversTypes['Order']>, ParentType, ContextType, Partial<MutationCreateOrderArgs>>;
  createPromotion?: Resolver<ResolversTypes['Promotion'], ParentType, ContextType, RequireFields<MutationCreatePromotionArgs, 'input'>>;
  createSupplier?: Resolver<Maybe<ResolversTypes['Supplier']>, ParentType, ContextType, RequireFields<MutationCreateSupplierArgs, 'input'>>;
  createSupplierConfig?: Resolver<ResolversTypes['SupplierConfig'], ParentType, ContextType, RequireFields<MutationCreateSupplierConfigArgs, 'input'>>;
  createUser?: Resolver<Maybe<Array<Maybe<ResolversTypes['String']>>>, ParentType, ContextType, Partial<MutationCreateUserArgs>>;
  deleteCatalogTemplate?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType, RequireFields<MutationDeleteCatalogTemplateArgs, 'id'>>;
  deleteCategory?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType, RequireFields<MutationDeleteCategoryArgs, 'id'>>;
  deleteCredit?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType, RequireFields<MutationDeleteCreditArgs, 'deleteCreditInput'>>;
  deleteGoal?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType, RequireFields<MutationDeleteGoalArgs, 'id'>>;
  deleteItems?: Resolver<Maybe<Array<Maybe<ResolversTypes['ID']>>>, ParentType, ContextType, RequireFields<MutationDeleteItemsArgs, 'itemIds'>>;
  deleteRoute?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType, RequireFields<MutationDeleteRouteArgs, 'id'>>;
  deleteUsers?: Resolver<Maybe<Array<Maybe<ResolversTypes['ID']>>>, ParentType, ContextType, RequireFields<MutationDeleteUsersArgs, 'userIds'>>;
  reconcileInvoiceWithItem?: Resolver<Maybe<ResolversTypes['InvoiceItem']>, ParentType, ContextType, Partial<MutationReconcileInvoiceWithItemArgs>>;
  reorderCategories?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType, RequireFields<MutationReorderCategoriesArgs, 'input'>>;
  sendReceivingReport?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType, Partial<MutationSendReceivingReportArgs>>;
  startSync?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType, RequireFields<MutationStartSyncArgs, 'input'>>;
  submitCreditRequests?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType, Partial<MutationSubmitCreditRequestsArgs>>;
  submitFeedback?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType, RequireFields<MutationSubmitFeedbackArgs, 'submitFeedbackInput'>>;
  submitOrder?: Resolver<Maybe<ResolversTypes['Order']>, ParentType, ContextType, RequireFields<MutationSubmitOrderArgs, 'submitOrderInput'>>;
  toggleFavorite?: Resolver<ResolversTypes['ToggleFavoriteResponse'], ParentType, ContextType, RequireFields<MutationToggleFavoriteArgs, 'input'>>;
  updateCatalogTemplate?: Resolver<ResolversTypes['CatalogTemplate'], ParentType, ContextType, RequireFields<MutationUpdateCatalogTemplateArgs, 'input'>>;
  updateCategoryOrder?: Resolver<Maybe<ResolversTypes['Category']>, ParentType, ContextType, RequireFields<MutationUpdateCategoryOrderArgs, 'categoryId' | 'newOrder' | 'supplierId'>>;
  updateCredit?: Resolver<Maybe<ResolversTypes['Credit']>, ParentType, ContextType, RequireFields<MutationUpdateCreditArgs, 'updateCreditInput'>>;
  updateCreditRequestStatus?: Resolver<Maybe<ResolversTypes['CreditRequest']>, ParentType, ContextType, RequireFields<MutationUpdateCreditRequestStatusArgs, 'id' | 'status'>>;
  updateDefaultInAccount?: Resolver<Maybe<Array<Maybe<ResolversTypes['Account']>>>, ParentType, ContextType, Partial<MutationUpdateDefaultInAccountArgs>>;
  updateEmployee?: Resolver<Maybe<ResolversTypes['Employee']>, ParentType, ContextType, RequireFields<MutationUpdateEmployeeArgs, 'input'>>;
  updateGoal?: Resolver<ResolversTypes['Goal'], ParentType, ContextType, RequireFields<MutationUpdateGoalArgs, 'updateGoalInput'>>;
  updateInvoice?: Resolver<Maybe<ResolversTypes['Invoice']>, ParentType, ContextType, RequireFields<MutationUpdateInvoiceArgs, 'updateInvoiceInput'>>;
  updateItemInCart?: Resolver<Maybe<ResolversTypes['Cart']>, ParentType, ContextType, RequireFields<MutationUpdateItemInCartArgs, 'updateItemInCartInput'>>;
  updateOrder?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType, Partial<MutationUpdateOrderArgs>>;
  updatePromotion?: Resolver<ResolversTypes['Promotion'], ParentType, ContextType, RequireFields<MutationUpdatePromotionArgs, 'input'>>;
  updateSupplierConfig?: Resolver<ResolversTypes['SupplierConfig'], ParentType, ContextType, RequireFields<MutationUpdateSupplierConfigArgs, 'input'>>;
  updateUser?: Resolver<Maybe<ResolversTypes['User']>, ParentType, ContextType, RequireFields<MutationUpdateUserArgs, 'user'>>;
  updateUserSuppliers?: Resolver<Maybe<Array<Maybe<ResolversTypes['CutoffTime']>>>, ParentType, ContextType, Partial<MutationUpdateUserSuppliersArgs>>;
  upsertAllCustomPrices?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType, RequireFields<MutationUpsertAllCustomPricesArgs, 'itemId' | 'price' | 'supplierId' | 'userId'>>;
  upsertCategory?: Resolver<Maybe<ResolversTypes['Category']>, ParentType, ContextType, RequireFields<MutationUpsertCategoryArgs, 'categoryInput'>>;
  upsertCustomPrices?: Resolver<Maybe<Array<Maybe<ResolversTypes['CustomPrice']>>>, ParentType, ContextType, RequireFields<MutationUpsertCustomPricesArgs, 'customPrices'>>;
  upsertItems?: Resolver<Maybe<Array<Maybe<ResolversTypes['Item']>>>, ParentType, ContextType, RequireFields<MutationUpsertItemsArgs, 'items'>>;
  upsertRoute?: Resolver<Maybe<ResolversTypes['Route']>, ParentType, ContextType, RequireFields<MutationUpsertRouteArgs, 'routeInput'>>;
  upsertUOM?: Resolver<Maybe<Array<Maybe<ResolversTypes['UOM']>>>, ParentType, ContextType, RequireFields<MutationUpsertUomArgs, 'input'>>;
  upsertUOMAllCustomPrices?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType, RequireFields<MutationUpsertUomAllCustomPricesArgs, 'itemId' | 'price' | 'supplierId' | 'userId'>>;
};

export type OrderResolvers<ContextType = any, ParentType extends ResolversParentTypes['Order'] = ResolversParentTypes['Order']> = {
  config?: Resolver<Maybe<ResolversTypes['JSONObject']>, ParentType, ContextType>;
  customerDetails?: Resolver<Maybe<ResolversTypes['User']>, ParentType, ContextType>;
  date_submitted?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  delivery_date?: Resolver<Maybe<ResolversTypes['Date']>, ParentType, ContextType>;
  discount?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  invoice?: Resolver<Maybe<ResolversTypes['Invoice']>, ParentType, ContextType>;
  notes?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  orderItems?: Resolver<Maybe<Array<Maybe<ResolversTypes['CartItem']>>>, ParentType, ContextType>;
  orderName?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  order_number?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  promotions?: Resolver<Maybe<Array<Maybe<ResolversTypes['PromotionUsage']>>>, ParentType, ContextType>;
  sales_rep?: Resolver<Maybe<ResolversTypes['SalesRep']>, ParentType, ContextType>;
  status?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  subtotal?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  supplier?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  supplier_logo?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  totalQuantity?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type OrderBySupplierResolvers<ContextType = any, ParentType extends ResolversParentTypes['OrderBySupplier'] = ResolversParentTypes['OrderBySupplier']> = {
  orderItems?: Resolver<Maybe<Array<Maybe<ResolversTypes['CartItem']>>>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type OrderItemTotalResolvers<ContextType = any, ParentType extends ResolversParentTypes['OrderItemTotal'] = ResolversParentTypes['OrderItemTotal']> = {
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  nacs_category?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  next_day_presale_totals?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  weekly_totals?: Resolver<Array<ResolversTypes['Int']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type OrderStatusResolvers<ContextType = any, ParentType extends ResolversParentTypes['OrderStatus'] = ResolversParentTypes['OrderStatus']> = {
  delivering_date?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  delivery_date?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  id?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  order_id?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  submission_date?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  supplier_id?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type OrdersBySupplierResolvers<ContextType = any, ParentType extends ResolversParentTypes['OrdersBySupplier'] = ResolversParentTypes['OrdersBySupplier']> = {
  orders?: Resolver<Array<ResolversTypes['Order']>, ParentType, ContextType>;
  totalCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type OrdersOutputV2Resolvers<ContextType = any, ParentType extends ResolversParentTypes['OrdersOutputV2'] = ResolversParentTypes['OrdersOutputV2']> = {
  orders?: Resolver<Array<Maybe<ResolversTypes['Order']>>, ParentType, ContextType>;
  totalCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PromotionResolvers<ContextType = any, ParentType extends ResolversParentTypes['Promotion'] = ResolversParentTypes['Promotion']> = {
  active?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  applies_to_all_items?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  applies_to_all_users?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  archived?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  buy_quantity?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  created_at?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  discount_amount?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  discount_percentage?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  end_date?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  free_quantity?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  items?: Resolver<Maybe<Array<ResolversTypes['Item']>>, ParentType, ContextType>;
  max_uses_per_customer?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  min_order_amount?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  promotion_type?: Resolver<ResolversTypes['PromotionType'], ParentType, ContextType>;
  start_date?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  supplier_id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  total_usage_limit?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  updated_at?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  usage_count?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  users?: Resolver<Maybe<Array<ResolversTypes['User']>>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PromotionApplicationResolvers<ContextType = any, ParentType extends ResolversParentTypes['PromotionApplication'] = ResolversParentTypes['PromotionApplication']> = {
  affected_items?: Resolver<Array<ResolversTypes['AffectedItem']>, ParentType, ContextType>;
  applied_promotions?: Resolver<Array<ResolversTypes['Promotion']>, ParentType, ContextType>;
  total_discount?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PromotionTypeResolvers<ContextType = any, ParentType extends ResolversParentTypes['PromotionType'] = ResolversParentTypes['PromotionType']> = {
  active?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  code?: Resolver<ResolversTypes['PromotionCode'], ParentType, ContextType>;
  created_at?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  description?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  updated_at?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PromotionUsageResolvers<ContextType = any, ParentType extends ResolversParentTypes['PromotionUsage'] = ResolversParentTypes['PromotionUsage']> = {
  created_at?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  order_id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  promotion?: Resolver<ResolversTypes['Promotion'], ParentType, ContextType>;
  promotion_id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  used_at?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  user_id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type PushNotificationTokenResolvers<ContextType = any, ParentType extends ResolversParentTypes['PushNotificationToken'] = ResolversParentTypes['PushNotificationToken']> = {
  token?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  user_id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type QueryResolvers<ContextType = any, ParentType extends ResolversParentTypes['Query'] = ResolversParentTypes['Query']> = {
  accounts?: Resolver<Maybe<Array<Maybe<ResolversTypes['Account']>>>, ParentType, ContextType, Partial<QueryAccountsArgs>>;
  actionItems?: Resolver<Maybe<Array<Maybe<ResolversTypes['ActionItem']>>>, ParentType, ContextType, Partial<QueryActionItemsArgs>>;
  activityLog?: Resolver<ResolversTypes['ActivityLogOutput'], ParentType, ContextType, RequireFields<QueryActivityLogArgs, 'activityLogInput'>>;
  allCustomPricesV2?: Resolver<ResolversTypes['AllCustomPricesOutputV2'], ParentType, ContextType, RequireFields<QueryAllCustomPricesV2Args, 'allCustomPricesInput'>>;
  balanceLink?: Resolver<Maybe<ResolversTypes['BalanceLink']>, ParentType, ContextType, Partial<QueryBalanceLinkArgs>>;
  brandSections?: Resolver<Array<Maybe<ResolversTypes['Supplier']>>, ParentType, ContextType, Partial<QueryBrandSectionsArgs>>;
  brandSpotlights?: Resolver<Maybe<Array<Maybe<ResolversTypes['BrandSpotlights']>>>, ParentType, ContextType, Partial<QueryBrandSpotlightsArgs>>;
  calculatePromotions?: Resolver<ResolversTypes['PromotionApplication'], ParentType, ContextType, RequireFields<QueryCalculatePromotionsArgs, 'items' | 'supplier_id' | 'user_id'>>;
  carts?: Resolver<Array<Maybe<ResolversTypes['Cart']>>, ParentType, ContextType, RequireFields<QueryCartsArgs, 'getCartsInput'>>;
  categories?: Resolver<Array<Maybe<ResolversTypes['BasicCategory']>>, ParentType, ContextType, Partial<QueryCategoriesArgs>>;
  categoriesBySupplier?: Resolver<Maybe<Array<Maybe<ResolversTypes['Category']>>>, ParentType, ContextType, Partial<QueryCategoriesBySupplierArgs>>;
  creditRequests?: Resolver<Maybe<Array<Maybe<ResolversTypes['CreditRequest']>>>, ParentType, ContextType, Partial<QueryCreditRequestsArgs>>;
  credits?: Resolver<Array<Maybe<ResolversTypes['Credit']>>, ParentType, ContextType, Partial<QueryCreditsArgs>>;
  customUOMPrices?: Resolver<Array<Maybe<ResolversTypes['CustomUOMPricesByItem']>>, ParentType, ContextType, RequireFields<QueryCustomUomPricesArgs, 'itemId' | 'supplierId' | 'userId'>>;
  customerGroups?: Resolver<Maybe<Array<Maybe<ResolversTypes['CustomerGroup']>>>, ParentType, ContextType, Partial<QueryCustomerGroupsArgs>>;
  customerHiddenProducts?: Resolver<ResolversTypes['CustomerHiddenProductsOutput'], ParentType, ContextType, RequireFields<QueryCustomerHiddenProductsArgs, 'customerHiddenProductsInput'>>;
  cutoffTimes?: Resolver<Array<Maybe<ResolversTypes['CutoffTime']>>, ParentType, ContextType, Partial<QueryCutoffTimesArgs>>;
  dashboardMetrics?: Resolver<Maybe<ResolversTypes['DashboardMetrics']>, ParentType, ContextType, RequireFields<QueryDashboardMetricsArgs, 'supplier_id'>>;
  employeesV2?: Resolver<ResolversTypes['EmployeesOutputV2'], ParentType, ContextType, RequireFields<QueryEmployeesV2Args, 'employeesInput'>>;
  expectedOrders?: Resolver<Array<Maybe<ResolversTypes['Order']>>, ParentType, ContextType, RequireFields<QueryExpectedOrdersArgs, 'supplierId'>>;
  getBestSellers?: Resolver<ResolversTypes['GetBestSellersOutput'], ParentType, ContextType, RequireFields<QueryGetBestSellersArgs, 'getBestSellersInput'>>;
  getCatalogTemplates?: Resolver<Array<Maybe<ResolversTypes['CatalogTemplate']>>, ParentType, ContextType, RequireFields<QueryGetCatalogTemplatesArgs, 'getCatalogTemplatesInput'>>;
  getFavorites?: Resolver<Array<ResolversTypes['Item']>, ParentType, ContextType, RequireFields<QueryGetFavoritesArgs, 'input'>>;
  getSyncingStatus?: Resolver<ResolversTypes['SyncStatusResponse'], ParentType, ContextType, RequireFields<QueryGetSyncingStatusArgs, 'supplierId'>>;
  goal?: Resolver<Maybe<ResolversTypes['Goal']>, ParentType, ContextType, RequireFields<QueryGoalArgs, 'id'>>;
  goalPeriods?: Resolver<Array<ResolversTypes['GoalPeriodOption']>, ParentType, ContextType, RequireFields<QueryGoalPeriodsArgs, 'goalId'>>;
  goals?: Resolver<ResolversTypes['GoalsResponse'], ParentType, ContextType, RequireFields<QueryGoalsArgs, 'goalsInput'>>;
  groupPricesV2?: Resolver<ResolversTypes['GroupPricesOutputV2'], ParentType, ContextType, RequireFields<QueryGroupPricesV2Args, 'groupPricesInput'>>;
  invoice?: Resolver<Maybe<ResolversTypes['InvoiceWithStatus']>, ParentType, ContextType, RequireFields<QueryInvoiceArgs, 'orderId'>>;
  invoiceItemMatch?: Resolver<Maybe<ResolversTypes['InvoiceItemMatch']>, ParentType, ContextType, Partial<QueryInvoiceItemMatchArgs>>;
  invoiceItems?: Resolver<Array<Maybe<ResolversTypes['InvoiceItem']>>, ParentType, ContextType, Partial<QueryInvoiceItemsArgs>>;
  invoices?: Resolver<Maybe<Array<Maybe<ResolversTypes['Invoice']>>>, ParentType, ContextType, Partial<QueryInvoicesArgs>>;
  invoicesBySupplier?: Resolver<ResolversTypes['InvoicesBySupplier'], ParentType, ContextType, Partial<QueryInvoicesBySupplierArgs>>;
  itemUOMs?: Resolver<Array<Maybe<ResolversTypes['ItemUOM']>>, ParentType, ContextType, RequireFields<QueryItemUoMsArgs, 'itemId' | 'supplierId'>>;
  items?: Resolver<Array<Maybe<ResolversTypes['Item']>>, ParentType, ContextType, Partial<QueryItemsArgs>>;
  itemsAvailableDuffl?: Resolver<Maybe<Array<Maybe<ResolversTypes['ItemAvailable']>>>, ParentType, ContextType, Partial<QueryItemsAvailableDufflArgs>>;
  itemsByFilter?: Resolver<Array<Maybe<ResolversTypes['Item']>>, ParentType, ContextType, Partial<QueryItemsByFilterArgs>>;
  itemsBySupplier?: Resolver<Array<Maybe<ResolversTypes['Item']>>, ParentType, ContextType, Partial<QueryItemsBySupplierArgs>>;
  itemsV2?: Resolver<ResolversTypes['ItemsOutputV2'], ParentType, ContextType, RequireFields<QueryItemsV2Args, 'itemsInput'>>;
  orderBySupplier?: Resolver<Maybe<ResolversTypes['OrderBySupplier']>, ParentType, ContextType, Partial<QueryOrderBySupplierArgs>>;
  orderItemTotals?: Resolver<Array<ResolversTypes['OrderItemTotal']>, ParentType, ContextType, Partial<QueryOrderItemTotalsArgs>>;
  orderStatuses?: Resolver<Maybe<Array<Maybe<ResolversTypes['OrderStatus']>>>, ParentType, ContextType, Partial<QueryOrderStatusesArgs>>;
  orders?: Resolver<Array<Maybe<ResolversTypes['Order']>>, ParentType, ContextType, Partial<QueryOrdersArgs>>;
  ordersBySupplier?: Resolver<ResolversTypes['OrdersBySupplier'], ParentType, ContextType, Partial<QueryOrdersBySupplierArgs>>;
  ordersV2?: Resolver<ResolversTypes['OrdersOutputV2'], ParentType, ContextType, RequireFields<QueryOrdersV2Args, 'ordersInput'>>;
  promotion?: Resolver<Maybe<ResolversTypes['Promotion']>, ParentType, ContextType, RequireFields<QueryPromotionArgs, 'id' | 'supplierId'>>;
  promotionTypes?: Resolver<Array<ResolversTypes['PromotionType']>, ParentType, ContextType>;
  promotions?: Resolver<Array<ResolversTypes['Promotion']>, ParentType, ContextType, Partial<QueryPromotionsArgs>>;
  pushNotificationTokens?: Resolver<Maybe<Array<Maybe<ResolversTypes['PushNotificationToken']>>>, ParentType, ContextType, Partial<QueryPushNotificationTokensArgs>>;
  recommendations?: Resolver<Array<Maybe<ResolversTypes['Recommendation']>>, ParentType, ContextType, Partial<QueryRecommendationsArgs>>;
  routeTotals?: Resolver<Array<ResolversTypes['RouteTotal']>, ParentType, ContextType, Partial<QueryRouteTotalsArgs>>;
  routes?: Resolver<Maybe<Array<Maybe<ResolversTypes['Route']>>>, ParentType, ContextType, Partial<QueryRoutesArgs>>;
  routesBySupplier?: Resolver<Maybe<Array<Maybe<ResolversTypes['Route']>>>, ParentType, ContextType, Partial<QueryRoutesBySupplierArgs>>;
  routesV2?: Resolver<Array<Maybe<ResolversTypes['Route']>>, ParentType, ContextType, RequireFields<QueryRoutesV2Args, 'routesInput'>>;
  sections?: Resolver<Array<Maybe<ResolversTypes['Section']>>, ParentType, ContextType, Partial<QuerySectionsArgs>>;
  spotlights?: Resolver<Maybe<Array<Maybe<ResolversTypes['Supplier']>>>, ParentType, ContextType, Partial<QuerySpotlightsArgs>>;
  supplierConfig?: Resolver<ResolversTypes['SupplierConfig'], ParentType, ContextType, RequireFields<QuerySupplierConfigArgs, 'supplierId'>>;
  suppliers?: Resolver<Array<Maybe<ResolversTypes['Supplier']>>, ParentType, ContextType, Partial<QuerySuppliersArgs>>;
  tags?: Resolver<Array<Maybe<ResolversTypes['Tag']>>, ParentType, ContextType, Partial<QueryTagsArgs>>;
  uoms?: Resolver<Array<Maybe<ResolversTypes['UOM']>>, ParentType, ContextType, RequireFields<QueryUomsArgs, 'supplierId'>>;
  uploadedOrderCsv?: Resolver<Maybe<ResolversTypes['UploadedOrderCsv']>, ParentType, ContextType, Partial<QueryUploadedOrderCsvArgs>>;
  users?: Resolver<Array<Maybe<ResolversTypes['User']>>, ParentType, ContextType, Partial<QueryUsersArgs>>;
  usersByFilter?: Resolver<Maybe<ResolversTypes['UsersByFilter']>, ParentType, ContextType, Partial<QueryUsersByFilterArgs>>;
  usersOnTodaysRoutes?: Resolver<Array<Maybe<ResolversTypes['User']>>, ParentType, ContextType, RequireFields<QueryUsersOnTodaysRoutesArgs, 'supplierId'>>;
  usersV2?: Resolver<ResolversTypes['UsersOutputV2'], ParentType, ContextType, RequireFields<QueryUsersV2Args, 'usersInput'>>;
};

export type RecommendationResolvers<ContextType = any, ParentType extends ResolversParentTypes['Recommendation'] = ResolversParentTypes['Recommendation']> = {
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  is_trending?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  item?: Resolver<Maybe<ResolversTypes['Item']>, ParentType, ContextType>;
  item_id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  num_store?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  quantity?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  user_id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type RoleResolvers<ContextType = any, ParentType extends ResolversParentTypes['Role'] = ResolversParentTypes['Role']> = {
  description?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type RouteResolvers<ContextType = any, ParentType extends ResolversParentTypes['Route'] = ResolversParentTypes['Route']> = {
  color?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  config?: Resolver<Maybe<ResolversTypes['JSONObject']>, ParentType, ContextType>;
  day_of_week?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  driver?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  supplier_id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type RouteTotalResolvers<ContextType = any, ParentType extends ResolversParentTypes['RouteTotal'] = ResolversParentTypes['RouteTotal']> = {
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  weekly_totals?: Resolver<Array<ResolversTypes['Int']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type SalesByRouteResolvers<ContextType = any, ParentType extends ResolversParentTypes['SalesByRoute'] = ResolversParentTypes['SalesByRoute']> = {
  number_of_stores?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  rank?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  route_name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  total_sales_value?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type SalesRepResolvers<ContextType = any, ParentType extends ResolversParentTypes['SalesRep'] = ResolversParentTypes['SalesRep']> = {
  email?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  id?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  source?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type SectionResolvers<ContextType = any, ParentType extends ResolversParentTypes['Section'] = ResolversParentTypes['Section']> = {
  image?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  items?: Resolver<Maybe<Array<Maybe<ResolversTypes['Item']>>>, ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  value?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type SubCartResolvers<ContextType = any, ParentType extends ResolversParentTypes['SubCart'] = ResolversParentTypes['SubCart']> = {
  cartItems?: Resolver<Maybe<Array<Maybe<ResolversTypes['CartItem']>>>, ParentType, ContextType>;
  discount?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  minimum?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  supplier?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type SupplierResolvers<ContextType = any, ParentType extends ResolversParentTypes['Supplier'] = ResolversParentTypes['Supplier']> = {
  address?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  config?: Resolver<Maybe<ResolversTypes['JSONObject']>, ParentType, ContextType>;
  email?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  itemsPreview?: Resolver<Maybe<Array<Maybe<ResolversTypes['Item']>>>, ParentType, ContextType>;
  logo?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  minimum?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  need_signup?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  orderCount?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  phone_number?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  qb_realm_id?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  spotlight_image?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type SupplierConfigResolvers<ContextType = any, ParentType extends ResolversParentTypes['SupplierConfig'] = ResolversParentTypes['SupplierConfig']> = {
  allow_image_upload?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  auto_set_delivery_date?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  default_dashboard_duration?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  enable_bounced_check_tracking?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  enable_catalog_sharing?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  enable_minimum_pricing?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  exclude_canceled_orders?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  filter_orders_by_sales_rep?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  invoice_scale_width?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  is_dsd?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  pavilions_address?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  pavilions_display_name?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  promotions_enabled?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  qb_credit_memo_date_filter?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  requires_delivery_date?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  send_back_in_stock_notification?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  send_daily_back_in_stock_notification?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  send_order_notifications?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  show_goals_tracking_feature?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  show_open_carts?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  show_order_tabs?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  show_profit_info?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  skip_qb_sync?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  sort_invoice_items?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  spotlight_ids?: Resolver<Maybe<Array<Maybe<ResolversTypes['Int']>>>, ParentType, ContextType>;
  use_alltime_avg_calculation?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  use_custom_driver_field?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type SyncStatusItemResolvers<ContextType = any, ParentType extends ResolversParentTypes['SyncStatusItem'] = ResolversParentTypes['SyncStatusItem']> = {
  createdAt?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  durationMs?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  endTime?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  error?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  lastSynced?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  startTime?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  status?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  type?: Resolver<ResolversTypes['SyncType'], ParentType, ContextType>;
  updatedAt?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type SyncStatusResponseResolvers<ContextType = any, ParentType extends ResolversParentTypes['SyncStatusResponse'] = ResolversParentTypes['SyncStatusResponse']> = {
  statuses?: Resolver<Array<ResolversTypes['SyncStatusItem']>, ParentType, ContextType>;
  supplierId?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type TagResolvers<ContextType = any, ParentType extends ResolversParentTypes['Tag'] = ResolversParentTypes['Tag']> = {
  name?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  value?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export interface TimeScalarConfig extends GraphQLScalarTypeConfig<ResolversTypes['Time'], any> {
  name: 'Time';
}

export type ToggleFavoriteResponseResolvers<ContextType = any, ParentType extends ResolversParentTypes['ToggleFavoriteResponse'] = ResolversParentTypes['ToggleFavoriteResponse']> = {
  isFavorited?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  message?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  success?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type UomResolvers<ContextType = any, ParentType extends ResolversParentTypes['UOM'] = ResolversParentTypes['UOM']> = {
  archived?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  supplier_id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type UploadedOrderCsvResolvers<ContextType = any, ParentType extends ResolversParentTypes['UploadedOrderCsv'] = ResolversParentTypes['UploadedOrderCsv']> = {
  download_url?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type UserResolvers<ContextType = any, ParentType extends ResolversParentTypes['User'] = ResolversParentTypes['User']> = {
  address?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  approved?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  archived?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  config?: Resolver<Maybe<ResolversTypes['JSONObject']>, ParentType, ContextType>;
  contact_email?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  created_at?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  created_by?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  custom_prices?: Resolver<Maybe<Array<Maybe<ResolversTypes['CustomPriceWithoutUserId']>>>, ParentType, ContextType>;
  custom_uom_prices?: Resolver<Maybe<Array<Maybe<ResolversTypes['CustomUOMPricesByItem']>>>, ParentType, ContextType>;
  delivery_window?: Resolver<Maybe<ResolversTypes['DeliveryWindow']>, ParentType, ContextType>;
  driver?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  ein?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  email?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  last_order_date?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  net_terms_days?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  open_cart?: Resolver<Maybe<ResolversTypes['Cart']>, ParentType, ContextType>;
  phone_number?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  qb_id?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  route_id?: Resolver<Maybe<ResolversTypes['ID']>, ParentType, ContextType>;
  store_group?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  supplier_beta?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  suppliers?: Resolver<Maybe<Array<Maybe<ResolversTypes['Supplier']>>>, ParentType, ContextType>;
  unpaid_balance?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  updated_at?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  user_name?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type UserCustomPricesResolvers<ContextType = any, ParentType extends ResolversParentTypes['UserCustomPrices'] = ResolversParentTypes['UserCustomPrices']> = {
  prices?: Resolver<Maybe<Array<Maybe<ResolversTypes['CustomPriceWithoutUserId']>>>, ParentType, ContextType>;
  userId?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type UsersByFilterResolvers<ContextType = any, ParentType extends ResolversParentTypes['UsersByFilter'] = ResolversParentTypes['UsersByFilter']> = {
  totalCount?: Resolver<Maybe<ResolversTypes['Float']>, ParentType, ContextType>;
  users?: Resolver<Maybe<Array<Maybe<ResolversTypes['User']>>>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type UsersOutputV2Resolvers<ContextType = any, ParentType extends ResolversParentTypes['UsersOutputV2'] = ResolversParentTypes['UsersOutputV2']> = {
  totalCount?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  users?: Resolver<Array<Maybe<ResolversTypes['User']>>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type Resolvers<ContextType = any> = {
  Account?: AccountResolvers<ContextType>;
  ActionItem?: ActionItemResolvers<ContextType>;
  ActivityLog?: ActivityLogResolvers<ContextType>;
  ActivityLogOutput?: ActivityLogOutputResolvers<ContextType>;
  AffectedItem?: AffectedItemResolvers<ContextType>;
  AllCustomPricesOutputV2?: AllCustomPricesOutputV2Resolvers<ContextType>;
  BalanceBankAccount?: BalanceBankAccountResolvers<ContextType>;
  BalanceCreditCard?: BalanceCreditCardResolvers<ContextType>;
  BalanceLink?: BalanceLinkResolvers<ContextType>;
  BaseOutput?: BaseOutputResolvers<ContextType>;
  BasicCategory?: BasicCategoryResolvers<ContextType>;
  BrandSpotlights?: BrandSpotlightsResolvers<ContextType>;
  Cart?: CartResolvers<ContextType>;
  CartItem?: CartItemResolvers<ContextType>;
  CatalogTemplate?: CatalogTemplateResolvers<ContextType>;
  Category?: CategoryResolvers<ContextType>;
  Credit?: CreditResolvers<ContextType>;
  CreditItem?: CreditItemResolvers<ContextType>;
  CreditRequest?: CreditRequestResolvers<ContextType>;
  CustomPrice?: CustomPriceResolvers<ContextType>;
  CustomPriceWithoutUserId?: CustomPriceWithoutUserIdResolvers<ContextType>;
  CustomUOMPrice?: CustomUomPriceResolvers<ContextType>;
  CustomUOMPricesByItem?: CustomUomPricesByItemResolvers<ContextType>;
  CustomerGroup?: CustomerGroupResolvers<ContextType>;
  CustomerHiddenProduct?: CustomerHiddenProductResolvers<ContextType>;
  CustomerHiddenProductsOutput?: CustomerHiddenProductsOutputResolvers<ContextType>;
  CutoffTime?: CutoffTimeResolvers<ContextType>;
  DashboardMetricBestSeller?: DashboardMetricBestSellerResolvers<ContextType>;
  DashboardMetricBrandBreakdown?: DashboardMetricBrandBreakdownResolvers<ContextType>;
  DashboardMetrics?: DashboardMetricsResolvers<ContextType>;
  Date?: GraphQLScalarType;
  DateTime?: GraphQLScalarType;
  Deal?: DealResolvers<ContextType>;
  DeliveryWindow?: DeliveryWindowResolvers<ContextType>;
  Employee?: EmployeeResolvers<ContextType>;
  EmployeesOutputV2?: EmployeesOutputV2Resolvers<ContextType>;
  GetBestSellersOutput?: GetBestSellersOutputResolvers<ContextType>;
  GetSalesByRoutesOutput?: GetSalesByRoutesOutputResolvers<ContextType>;
  Goal?: GoalResolvers<ContextType>;
  GoalAssignment?: GoalAssignmentResolvers<ContextType>;
  GoalPeriodOption?: GoalPeriodOptionResolvers<ContextType>;
  GoalsResponse?: GoalsResponseResolvers<ContextType>;
  GroupPrices?: GroupPricesResolvers<ContextType>;
  GroupPricesOutputV2?: GroupPricesOutputV2Resolvers<ContextType>;
  Invoice?: InvoiceResolvers<ContextType>;
  InvoiceItem?: InvoiceItemResolvers<ContextType>;
  InvoiceItemMatch?: InvoiceItemMatchResolvers<ContextType>;
  InvoiceWithStatus?: InvoiceWithStatusResolvers<ContextType>;
  InvoicesBySupplier?: InvoicesBySupplierResolvers<ContextType>;
  Item?: ItemResolvers<ContextType>;
  ItemAvailable?: ItemAvailableResolvers<ContextType>;
  ItemUOM?: ItemUomResolvers<ContextType>;
  ItemsOutputV2?: ItemsOutputV2Resolvers<ContextType>;
  JSON?: GraphQLScalarType;
  JSONObject?: GraphQLScalarType;
  Mutation?: MutationResolvers<ContextType>;
  Order?: OrderResolvers<ContextType>;
  OrderBySupplier?: OrderBySupplierResolvers<ContextType>;
  OrderItemTotal?: OrderItemTotalResolvers<ContextType>;
  OrderStatus?: OrderStatusResolvers<ContextType>;
  OrdersBySupplier?: OrdersBySupplierResolvers<ContextType>;
  OrdersOutputV2?: OrdersOutputV2Resolvers<ContextType>;
  Promotion?: PromotionResolvers<ContextType>;
  PromotionApplication?: PromotionApplicationResolvers<ContextType>;
  PromotionType?: PromotionTypeResolvers<ContextType>;
  PromotionUsage?: PromotionUsageResolvers<ContextType>;
  PushNotificationToken?: PushNotificationTokenResolvers<ContextType>;
  Query?: QueryResolvers<ContextType>;
  Recommendation?: RecommendationResolvers<ContextType>;
  Role?: RoleResolvers<ContextType>;
  Route?: RouteResolvers<ContextType>;
  RouteTotal?: RouteTotalResolvers<ContextType>;
  SalesByRoute?: SalesByRouteResolvers<ContextType>;
  SalesRep?: SalesRepResolvers<ContextType>;
  Section?: SectionResolvers<ContextType>;
  SubCart?: SubCartResolvers<ContextType>;
  Supplier?: SupplierResolvers<ContextType>;
  SupplierConfig?: SupplierConfigResolvers<ContextType>;
  SyncStatusItem?: SyncStatusItemResolvers<ContextType>;
  SyncStatusResponse?: SyncStatusResponseResolvers<ContextType>;
  Tag?: TagResolvers<ContextType>;
  Time?: GraphQLScalarType;
  ToggleFavoriteResponse?: ToggleFavoriteResponseResolvers<ContextType>;
  UOM?: UomResolvers<ContextType>;
  UploadedOrderCsv?: UploadedOrderCsvResolvers<ContextType>;
  User?: UserResolvers<ContextType>;
  UserCustomPrices?: UserCustomPricesResolvers<ContextType>;
  UsersByFilter?: UsersByFilterResolvers<ContextType>;
  UsersOutputV2?: UsersOutputV2Resolvers<ContextType>;
};

