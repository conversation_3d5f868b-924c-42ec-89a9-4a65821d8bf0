import knex from "../../../knex/knex";
import { filterByUpc, logUpc, modifyUpc } from "../../util/upcs";
import addLastOrderedDate from "../../services/itemService/addLastOrderedDate";
import { QueryItemsArgs } from "../../generated/graphql";
import modifyToCustomPricing from "../../services/itemService/modifyToCustomPricing";
import { getHiddenProductIds } from "../../services/itemService";
import { addFavoriteStatusToItems } from "../../services/favoriteService";
import { populateItemsUOMs } from "../../services/itemService/uomService";

const Items = async (_, args: QueryItemsArgs) => {
  const { userId, ids, upcs, pagination } = args.getItemsInput;
  const { offset, limit } = pagination;

  // Get hidden product IDs if userId is provided
  let hiddenProductIds: string[] = [];
  if (userId) {
    // Get supplier ID from supplier_times table
    const supplierInfo = await knex("supplier_times")
      .select("supplier_id")
      .where("business_id", userId)
      .first();

    if (supplierInfo) {
      hiddenProductIds = await getHiddenProductIds(
        supplierInfo.supplier_id.toString(),
        userId
      );
    }
  }

  if (!upcs && !ids) {
    let items;
    if (userId === "1") {
      let query = knex
        .select()
        .table("item")
        .where("supplier", "Pitco Foods")
        .where("archived", false)
        .whereNotIn("nacs_category", [
          "TOBACCO - TAXABLE",
          "TOBACCO ACCESSORIES - TAXABLE",
          "CIGARETTES - TAXABLE",
          "ALCOHOL - TAXABLE",
        ]);

      // Filter out hidden products if any exist
      if (hiddenProductIds.length > 0) {
        query = query.whereNotIn("id", hiddenProductIds);
      }

      items = await query.limit(limit).offset(offset).orderBy("name", "asc");
    } else {
      let query = knex
        .select()
        .table("item")
        .where("supplier", "Pitco Foods")
        .where("local_item", false)
        .where("outdated", false)
        .where("archived", false);

      // Filter out hidden products if any exist
      if (hiddenProductIds.length > 0) {
        query = query.whereNotIn("id", hiddenProductIds);
      }

      items = await query.limit(limit).offset(offset).orderBy("name", "asc");
    }

    const itemsWithFavorites = await addFavoriteStatusToItems(
      items,
      parseInt(userId)
    );
    await modifyToCustomPricing(itemsWithFavorites, userId);
    return addLastOrderedDate(itemsWithFavorites, userId);
  }

  if (ids) {
    let query = knex.select().table("item").whereIn("id", ids);
    // Filter out hidden products if any exist
    if (hiddenProductIds.length > 0) {
      query = query.whereNotIn("id", hiddenProductIds);
    }

    const items = await query;
    const item = items[0];

    // Get related items
    let relatedItemsQuery = knex
      .select()
      .table("item")
      .where("supplier", item.supplier)
      .where("nacs_category", item.nacs_category)
      .andWhere("nacs_subcategory", item.nacs_subcategory)
      .whereNotIn("id", ids)
      .where("archived", false)
      .limit(10);

    // Filter out hidden products from related items
    if (hiddenProductIds.length > 0) {
      relatedItemsQuery = relatedItemsQuery.whereNotIn("id", hiddenProductIds);
    }

    const relatedItems = await relatedItemsQuery;

    const relatedItemsWithFavorites = await addFavoriteStatusToItems(
      relatedItems,
      parseInt(userId)
    );

    // Add favorite status to main item
    const itemsWithFavorites = await addFavoriteStatusToItems(
      items,
      parseInt(userId)
    );
    const itemWithFavorite = itemsWithFavorites[0];

    itemWithFavorite["related_items"] = relatedItemsWithFavorites;

    // Populate UOM data for the main item and related items
    await populateItemsUOMs([itemWithFavorite]);
    await populateItemsUOMs(relatedItemsWithFavorites);

    await modifyToCustomPricing([itemWithFavorite], userId);
    return addLastOrderedDate([itemWithFavorite], userId);
  }

  if (upcs) {
    const modifiedUpc = modifyUpc(upcs[0]);
    const suppliers = await knex
      .select()
      .table("supplier_times")
      .join("supplier", "supplier_times.supplier_id", "supplier.id")
      .where("supplier_times.business_id", userId)
      .orderBy("supplier.id", "asc");

    let query = knex("item")
      .select("item.*", knex.raw("json_agg(supplier.*)->0 as supplier_info"))
      .whereIn(
        "supplier",
        suppliers.map((supplier) => supplier.name)
      )
      .where("local_item", false)
      .where("outdated", false)
      .where("archived", false)
      .where((whereBuilder) => filterByUpc(whereBuilder, modifiedUpc))
      .leftJoin("supplier", "item.supplier", "supplier.name")
      .groupBy("item.id");

    // Filter out hidden products if any exist
    if (hiddenProductIds.length > 0) {
      query = query.whereNotIn("item.id", hiddenProductIds);
    }

    const result = await query;

    // Populate UOM data for each item based on its supplier
    for (const item of result) {
      if (item.supplier_info && item.supplier_info.id) {
        await populateItemsUOMs([item], item.supplier_info.id.toString());
      }
    }

    const user = await knex("attain_user").where("id", userId);
    await logUpc(user[0].name, userId, "General", upcs[0], modifiedUpc, result);

    const itemsWithFavorites = await addFavoriteStatusToItems(
      result,
      parseInt(userId)
    );
    await modifyToCustomPricing(itemsWithFavorites, userId);
    return addLastOrderedDate(itemsWithFavorites, userId);
  }
};

export default Items;
