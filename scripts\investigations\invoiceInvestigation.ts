import { quickBooksDesktopConfig } from "../../src/config/environment";
import { Invoice, InvoiceItem, Item, User } from "../../src/generated/graphql";
import { Ordering } from "../../src/generated/graphql";
import QuickBooksDesktopClient from "../../src/lib/quickBooksDesktop/client";
import { pullCustomersInfo } from "../../src/lib/quickBooksDesktop/customers";
import dayjs from "dayjs";
import fs from "fs";
import { getOrders } from "../../src/services/orderService/orderService";
import { getInvoiceByOrderId } from "../../src/services/invoiceService/getInvoice";
import {
  InvoiceMod,
  InvoiceLineMod,
} from "conductor-node/dist/src/integrations/qbd/qbdTypes";
import util from "util";
import knex from "../../knex/knex";

const client = new QuickBooksDesktopClient(quickBooksDesktopConfig.userId);

// These functions are similar to our syncing functions,
// but are made for updating invoices rather than creating them.
const mapDBInvoiceToQBUpdate = (
  invoice: Invoice
): Omit<InvoiceMod, "TxnID" | "EditSequence"> => {
  return {
    CustomerRef: {
      ListID: invoice.customerDetails.qb_id,
    },
    TxnDate: dayjs(invoice.date_created).format("YYYY-MM-DD"),
    InvoiceLineMod: invoice.invoiceItems
      .filter((i) => i.id)
      .map((invoiceItem: InvoiceItem) => ({
        TxnLineID: "-1",
        Rate: String(invoiceItem.price),
        Quantity: Math.abs(invoiceItem.quantity),
        ItemRef: {
          ListID: invoiceItem.qb_id,
        },
        ...(!invoiceItem.name.toLowerCase().includes("(z")
          ? {
              InventorySiteRef: {
                FullName: "OPERATIONS CENTER",
              },
            }
          : {}),
      })) as InvoiceLineMod[],
    RefNumber: invoice.id,
    Other: `ATTAIN-${invoice.id}`,
  };
};

export const fetchInvoiceSets = async (supplierId: string) => {
  const invoices: Invoice[] = await knex<Invoice>("invoice")
    .select(
      "invoice.*",
      knex.raw(
        `json_build_object('id', "attain_user".id, 'name', "attain_user".name, 'qb_id', "attain_user".qb_id) as "customerDetails"`
      ),
      knex.raw(
        `array_agg(json_build_object('id', "invoice_item".id, 'name', "invoice_item".name, 'price', "invoice_item".price, 'quantity', "invoice_item".quantity, 'qb_id', "invoice_item".qb_id)) as "invoiceItems"`
      )
    )
    .leftJoin("attain_user", "invoice.user_id", "attain_user.id")
    .leftJoin("invoice_item", "invoice.id", "invoice_item.invoice_id")
    .leftJoin("order_detail", "invoice.order_id", "order_detail.id")
    .orderBy("id", "desc")
    .groupBy("invoice.id", "attain_user.id")
    .where("invoice.supplier_id", "68")
    .whereNot("invoice.archived", true)
    .where("invoice.subtotal", ">=", 0)
    .where("order_detail.status", "Delivered")
    .modify((query) => {
      if (supplierId === "68") {
        query.andWhere("invoice.date_created", ">", "2024-09-01");
      }
    });

  return {
    syncedInvoices: invoices.filter((invoice) => invoice.qb_id),
    unsyncedInvoices: invoices.filter(
      (invoice) =>
        !invoice.qb_id &&
        invoice.invoiceItems.filter((i) => i.id).length > 0 &&
        invoice.signature_name
    ),
  };
};

// This function is used to investigate the invoices that are different between Attain and QuickBooks
// and reconcile them by deferring to the Attain invoice. Use with caution.
const main = async () => {
  const invoices = (await fetchInvoiceSets("68")).syncedInvoices;

  const attainInvoiceIds = invoices
    .filter(
      (invoice) =>
        invoice !== null &&
        invoice !== undefined &&
        invoice.invoiceItems.length > 0 &&
        invoice.id !== "14282"
    )
    .map((invoice) => [invoice.id.toString(), invoice.subtotal])
    .sort((a, b) => a[0].toString().localeCompare(b[0].toString()));

  console.log("Attain invoices:", attainInvoiceIds.length);

  const qbInvoices = await client.getInvoices({
    TxnDateRangeFilter: {
      FromTxnDate: dayjs("2025-03-15").format("YYYY-MM-DD"),
      ToTxnDate: dayjs("2025-04-06").format("YYYY-MM-DD"),
    },
  });

  const qbInvoiceData = qbInvoices
    .filter(
      (invoice) => invoice.RefNumber !== null && invoice.RefNumber !== undefined
    )
    .map((invoice) => [invoice.RefNumber, invoice.Subtotal])
    .sort((a, b) => a[0].localeCompare(b[0]));

  console.log("QuickBooks invoices:", qbInvoiceData.length);

  // Create maps for easier lookup
  const attainInvoiceMap = new Map();
  attainInvoiceIds.forEach(([id, subtotal]) => {
    attainInvoiceMap.set(id, subtotal);
  });

  const qbInvoiceMap = new Map();
  qbInvoiceData.forEach(([refNumber, subtotal]) => {
    qbInvoiceMap.set(refNumber, subtotal);
  });

  // Find invoices with different subtotals
  const differentSubtotals = [];

  attainInvoiceMap.forEach((attainSubtotal, invoiceId) => {
    if (qbInvoiceMap.has(invoiceId)) {
      const qbSubtotal = qbInvoiceMap.get(invoiceId);
      // Check if subtotals are different, using a small threshold to handle floating point comparison
      if (Math.abs(Number(attainSubtotal) - Number(qbSubtotal)) > 0.01) {
        differentSubtotals.push({
          invoiceId,
          attainSubtotal: Number(attainSubtotal),
          qbSubtotal: Number(qbSubtotal),
          difference: Number(attainSubtotal) - Number(qbSubtotal),
        });
      }
    }
  });

  console.log("Invoices with different subtotals:", differentSubtotals.length);
  console.log(util.inspect(differentSubtotals, false, null, true));

  // Find invoices in Attain but not in QB
  const inAttainOnly = [...attainInvoiceMap.keys()].filter(
    (id) => !qbInvoiceMap.has(id)
  );
  console.log("Invoices in Attain only:", inAttainOnly.length);

  // Find invoices in QB but not in Attain
  const inQBOnly = [...qbInvoiceMap.keys()].filter(
    (id) => !attainInvoiceMap.has(id)
  );
  console.log("Invoices in QuickBooks only:", inQBOnly.length);

  // Process invoices with different subtotals sequentially using for...of loop (better for async operations)
  const updateResults = [];
  for (const difference of differentSubtotals) {
    try {
      const oldQbInvoice = qbInvoices.find(
        (invoice) => invoice.RefNumber === difference.invoiceId
      );

      const attainInvoice = invoices.find(
        (invoice) => invoice.id.toString() === difference.invoiceId
      );

      if (!oldQbInvoice || !attainInvoice) {
        console.log(`Could not find invoice for ${difference.invoiceId}`);
        continue;
      }

      const baseInvoice = mapDBInvoiceToQBUpdate(attainInvoice);
      const newQbInvoice: InvoiceMod = {
        ...baseInvoice,
        TxnID: oldQbInvoice.TxnID,
        EditSequence: oldQbInvoice.EditSequence,
      };

      console.log(`Updating invoice ${difference.invoiceId}...`);
      console.log(newQbInvoice);

      // // Wait for the update to complete
      // const resultInvoice = await client.updateInvoice(newQbInvoice);
      // console.log(
      //   `Successfully updated invoice ${difference.invoiceId}:`,
      //   resultInvoice
      // );

      updateResults.push({
        invoiceId: difference.invoiceId,
        status: "success",
        result: newQbInvoice,
      });
    } catch (error) {
      console.error(`Error updating invoice ${difference.invoiceId}:`, error);
      updateResults.push({
        invoiceId: difference.invoiceId,
        status: "error",
        error: error.message || String(error),
      });
    }
  }

  console.log("Update results summary:");
  console.log(`- Total processed: ${updateResults.length}`);
  console.log(
    `- Successful: ${
      updateResults.filter((r) => r.status === "success").length
    }`
  );
  console.log(
    `- Failed: ${updateResults.filter((r) => r.status === "error").length}`
  );

  return {
    differentSubtotals,
    updateResults,
  };
};

// This function is used to update a QB invoice from an Attain invoice
const updateQBInvoiceWithAttainInvoice = async (invoiceId: string) => {
  const invoices = (await fetchInvoiceSets("68")).syncedInvoices;

  const attainInvoice = invoices.find(
    (invoice) => invoice.id.toString() === invoiceId
  );
  if (!attainInvoice) {
    console.log(`Could not find invoice for ${invoiceId}`);
    return;
  }

  const qbInvoices = await client.getInvoices({
    RefNumber: attainInvoice.id.toString(),
  });

  if (qbInvoices.length === 0) {
    console.log(`Could not find invoice for ${invoiceId} in QuickBooks`);
    return;
  }

  const qbInvoice = qbInvoices[0];

  const baseInvoice = mapDBInvoiceToQBUpdate(attainInvoice);
  const newQbInvoice: InvoiceMod = {
    ...baseInvoice,
    TxnID: qbInvoice.TxnID,
    EditSequence: qbInvoice.EditSequence,
  };

  console.log(`Updating invoice ${attainInvoice.id}...`);
  console.log(util.inspect(newQbInvoice, false, null, true));

  // // Wait for the update to complete
  // const resultInvoice = await client.updateInvoice(newQbInvoice);
  // console.log(
  //   `Successfully updated invoice ${attainInvoice.id}:`,
  //   resultInvoice
  // );
};

// updateQBInvoiceWithAttainInvoice("22825").catch((error) => {
//   console.error("Error in updateQBInvoiceWithAttainInvoice:", error);
//   process.exit(1);
// });

// const main2 = async () => {
//   const invoices = await client.getInvoices({
//     RefNumber: "22825",
//     IncludeLineItems: true,
//   });
//   console.log("qb invoices", invoices);
//   console.log("--------");
//   console.log(util.inspect(invoices, false, null, true /* enable colors */));
// };
