import { <PERSON><PERSON> } from "knex";
import knex from "../../../knex/knex";
import { CreatePromotionInput } from "../../generated/graphql";
import { getPromotion } from "./getPromotions";

export const createPromotion = async (
  data: CreatePromotionInput,
  trx?: Knex.Transaction
): Promise<any> => {
  const knexInstance = trx || knex;

  const [promotion] = await knexInstance("promotions")
    .insert({
      name: data.name,
      supplier_id: data.supplier_id,
      promotion_type_id: data.promotion_type,
      start_date: data.start_date,
      end_date: data.end_date,
      applies_to_all_items: data.applies_to_all_items,
      applies_to_all_users: data.applies_to_all_users,
      max_uses_per_customer: data.max_uses_per_customer,
      total_usage_limit: data.total_usage_limit,
      buy_quantity: data.buy_quantity,
      discount_amount: data.discount_amount,
      discount_percentage: data.discount_percentage,
      free_quantity: data.free_quantity,
      min_order_amount: data.min_order_amount,
      active: data.active,
    })
    .returning("*");

  if (!data.applies_to_all_items && data.items?.length) {
    await knexInstance("promotion_items").insert(
      data.items.map((item) => ({
        promotion_id: promotion.id,
        item_id: item.item_id,
      }))
    );
  }

  if (!data.applies_to_all_users && data.users?.length) {
    await knexInstance("promotion_users").insert(
      data.users.map((user) => ({
        promotion_id: promotion.id,
        user_id: user.user_id,
      }))
    );
  }

  return getPromotion(data.supplier_id, promotion.id);
};