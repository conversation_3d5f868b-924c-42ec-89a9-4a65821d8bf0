import knex from "../../../knex/knex";
import { MutationDeleteCategoryArgs } from "../../generated/graphql";

const DeleteCategory = async (_, args: MutationDeleteCategoryArgs) => {
  const { id: categoryToDelete } = args;

  await knex("category_item")
    .delete()
    .where("category_id", categoryToDelete)
    .then(() => {
      return knex("category").delete().where("id", categoryToDelete);
    });

  return categoryToDelete;
};

export default DeleteCategory;
