"use strict";

var dbm;
var type;
var seed;

exports.setup = function (options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

exports.up = function (db) {
  return db
    .addColumn("invoice", "payment_status", {
      type: "string",
      length: 50,
      defaultValue: "paid",
    })
    .then(function () {
      return db.addIndex("invoice", "idx_invoice_payment_status", [
        "payment_status",
      ]);
    })
    .then(function () {
      // Update existing records based on current payment state
      return db.runSql(`
      UPDATE invoice 
      SET payment_status = CASE 
        WHEN ROUND(COALESCE(total, 0) - COALESCE(paid, 0), 2) <= 0 THEN 'paid'
        WHEN ROUND(COALESCE(paid, 0), 2) > 0 THEN 'partial'
        ELSE 'unpaid'
      END
    `);
    });
};

exports.down = function (db) {
  return db
    .removeIndex("invoice", "idx_invoice_payment_status")
    .then(function () {
      return db.removeColumn("invoice", "payment_status");
    });
};

exports._meta = {
  version: 1,
};
