'use strict';

var dbm;
var type;
var seed;

/**
  * We receive the dbmigrate dependency from dbmigrate initially.
  * This enables us to not have to rely on NODE_PATH.
  */
exports.setup = function(options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

exports.up = function(db) {
  return db.createTable('supplier_config', {
    id: { 
      type: 'int', 
      primaryKey: true, 
      autoIncrement: true 
    },
    supplier_id: {
      type: 'int',
      foreignKey: {
        name: 'supplier_config_supplier_id_fk',
        table: 'supplier',
        rules: {
          onDelete: 'CASCADE',
          onUpdate: 'RESTRICT'
        },
        mapping: 'id'
      }
    },
    key: { 
      type: 'string', 
      notNull: true 
    },
    value: { 
      type: 'string', 
      notNull: true 
    },
    created_at: {
      type: 'timestamp',
      notNull: true,
      defaultValue: new String('CURRENT_TIMESTAMP')
    },
    updated_at: {
      type: 'timestamp',
      notNull: true,
      defaultValue: new String('CURRENT_TIMESTAMP')
    }
  });
};

exports.down = function(db) {
  return db.dropTable('supplier_config');
};

exports._meta = {
  "version": 1
};
