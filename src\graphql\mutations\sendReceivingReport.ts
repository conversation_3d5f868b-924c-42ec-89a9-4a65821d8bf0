const axios = require("axios");

const sendReceivingReport = async (_, args) => {
  const { userId, invoiceId } = args;

  if (userId !== "11") {
    return "bad input";
  }

  const text = "Send invoice report to " + userId + " for invoice " + invoiceId;
  try {
    await axios.post(
      "*********************************************************************************",
      { text },
      {
        headers: {
          accept: "application/json",
          "content-type": "application/json",
        },
      }
    );
  } catch (error) {
    console.log("Error sending Slack message", error);
  }

  return "success";
};

export default sendReceivingReport;
