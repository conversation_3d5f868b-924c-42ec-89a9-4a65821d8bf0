import Conductor from "conductor-node";
import {
  CreditMemoAddRq,
  CreditMemoModRq,
  CustomerAddRq,
  CustomerModRq,
  CustomerQueryRq,
  InventoryAdjustmentAddRq,
  InvoiceAddRq,
  InvoiceModRq,
  InvoiceQueryRq,
  ItemInventoryAddRq,
  ItemInventoryModRq,
  ItemInventoryQueryRq,
} from "conductor-node/dist/src/integrations/qbd/qbdTypes";

class QuickBooksDesktopClient extends Conductor {
  private userId: string;
  constructor(endUserId: string) {
    const clientSecret = process.env.QUICKBOOKS_DESKTOP_CLIENT_SECRET;
    super(clientSecret);
    this.userId = endUserId;
  }
  async getItems(
    params?: ItemInventoryQueryRq,
    includeInventoryAssembly = false,
    includeCharge = false,
    includeNonInventory = false
  ) {
    const items = await this.qbd.itemInventory.query(this.userId, params);
    if (includeInventoryAssembly) {
      const otherAssemblyItems = await this.qbd.itemInventoryAssembly.query(
        this.userId,
        params
      );
      items.push(...otherAssemblyItems);
    }
    if (includeCharge) {
      const otherChargeItems = await this.qbd.itemOtherCharge.query(
        this.userId,
        params
      );
      items.push(...otherChargeItems);
    }
    if (includeNonInventory) {
      const otherItems = await this.qbd.itemNonInventory.query(
        this.userId,
        params
      );
      items.push(...otherItems);
    }
    return items;
  }

  async getCustomers(params?: CustomerQueryRq) {
    return this.qbd.customer.query(this.userId, params);
  }

  async getInvoices(params?: InvoiceQueryRq) {
    return this.qbd.invoice.query(this.userId, params);
  }

  async updateItem(item: ItemInventoryModRq["ItemInventoryMod"]) {
    return this.qbd.itemInventory.mod(this.userId, item);
  }

  async updateCustomer(customer: CustomerModRq["CustomerMod"]) {
    return this.qbd.customer.mod(this.userId, customer);
  }

  async updateInvoice(invoice: InvoiceModRq["InvoiceMod"]) {
    return this.qbd.invoice.mod(this.userId, invoice);
  }

  async updateCreditMemo(creditMemo: CreditMemoModRq["CreditMemoMod"]) {
    return this.qbd.creditMemo.mod(this.userId, creditMemo);
  }

  async createItem(item: ItemInventoryAddRq["ItemInventoryAdd"]) {
    return this.qbd.itemInventory.add(this.userId, item);
  }

  async createCustomer(customer: CustomerAddRq["CustomerAdd"]) {
    return this.qbd.customer.add(this.userId, customer);
  }

  async createInvoice(invoice: InvoiceAddRq["InvoiceAdd"]) {
    return this.qbd.invoice.add(this.userId, invoice);
  }

  async createCreditMemo(creditMemo: CreditMemoAddRq["CreditMemoAdd"]) {
    return this.qbd.creditMemo.add(this.userId, creditMemo);
  }

  async createInventoryAdjustment(
    inventoryAdjustment: InventoryAdjustmentAddRq["InventoryAdjustmentAdd"]
  ) {
    return this.qbd.inventoryAdjustment.add(this.userId, inventoryAdjustment);
  }
}

export default QuickBooksDesktopClient;
