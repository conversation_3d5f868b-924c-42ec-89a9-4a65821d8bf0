import knex from "../../../knex/knex";
import { CartItem, Item } from "../../generated/graphql";

// TODO: make this functional
const modifyToCustomPricing = async (
  items: (Item | CartItem)[],
  userId: string
) => {
  const customPrices = await knex("custom_item_price")
    .select("item_id", "price")
    .where("user_id", userId);

  if (customPrices.length === 0) {
    return;
  }
  items.forEach((item) => {
    const customPrice = customPrices.find((price) => {
      if ((item as CartItem).item_id) {
        return price.item_id === (item as CartItem).item_id;
      }
      return price.item_id === item.id;
    });
    if (customPrice) {
      if (item.discounted_price) {
        item.discounted_price = customPrice.price;
      }
      item.price = customPrice.price;
    }
  });
};

export default modifyToCustomPricing;
