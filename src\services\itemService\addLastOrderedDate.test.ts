import { queryBuilder } from "../../../knex/mock";
import { Item } from "../../generated/graphql";

const knex = jest.requireActual("../../../knex/knex").default;

jest.mock("../../../knex/knex", () => ({
  __esModule: true,
  default: queryBuilder,
}));

describe("addLastOrderedDate", () => {
  let addLastOrderedDate: typeof import("./addLastOrderedDate").default;

  const userId = "user-456";

  beforeAll(async () => {
    ({ default: addLastOrderedDate } = await import("./addLastOrderedDate"));
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterAll(async () => {
    await knex.destroy();
  });

  const mockItems: Item[] = [
    { id: 1, name: "Item A", price: 10 },
    { id: 2, name: "Item B", price: 20 },
  ] as any;

  it("should enrich items with stats using all-time average for supplier_id = 31", async () => {
    queryBuilder.first.mockResolvedValueOnce({ supplier_id: 31 });

    // No need to mock each chained call individually if they just return `this`.
    // The queryBuilder mock object already does this.
    queryBuilder.orderBy.mockResolvedValueOnce([
      { item_id: 1, date_submitted: "2023-01-01", quantity: 10 },
      { item_id: 1, date_submitted: "2023-02-01", quantity: 20 },
      { item_id: 2, date_submitted: "2023-03-01", quantity: 40 },
    ]);

    const result = await addLastOrderedDate(mockItems, userId);

    expect(result[0]).toMatchObject({
      id: 1,
      avg_cases_per_week: expect.any(Number),
      total_orders: 2,
      total_quantity: 30,
      first_ordered_date: "2023-01-01",
      last_ordered_date: "2023-02-01",
    });

    expect(result[1]).toMatchObject({
      id: 2,
      avg_cases_per_week: expect.any(Number),
      total_orders: 1,
      total_quantity: 40,
      first_ordered_date: "2023-03-01",
      last_ordered_date: "2023-03-01",
    });
  });

  it("should enrich using last 4 days average for non-supplier 31", async () => {
    queryBuilder.first.mockResolvedValueOnce({ supplier_id: 99 });

    queryBuilder.orderBy.mockResolvedValueOnce([
      { item_id: 1, date_submitted: "2024-06-01", quantity: 5 },
      { item_id: 1, date_submitted: "2024-06-10", quantity: 15 },
      { item_id: 1, date_submitted: "2024-06-20", quantity: 20 },
      { item_id: 2, date_submitted: "2024-06-01", quantity: 25 },
      { item_id: 2, date_submitted: "2024-06-10", quantity: 35 },
    ]);

    const result = await addLastOrderedDate(mockItems, userId);

    expect(result[0].avg_cases_per_week).toBeGreaterThanOrEqual(0);
    expect(result[1].avg_cases_per_week).toBeGreaterThanOrEqual(0);
  });

  it("should handle items with no order history", async () => {
    queryBuilder.first.mockResolvedValueOnce({ supplier_id: 31 });
    queryBuilder.orderBy.mockResolvedValueOnce([]);

    const result = await addLastOrderedDate(mockItems, userId);

    for (const item of result) {
      expect(item).toMatchObject({
        avg_cases_per_week: 0,
        total_orders: 0,
        total_quantity: 0,
        first_ordered_date: null,
        last_ordered_date: null,
      });
    }
  });

  it("should handle missing supplier entry for user", async () => {
    queryBuilder.first.mockResolvedValueOnce(null); // No supplier found
    queryBuilder.orderBy.mockResolvedValueOnce([]);

    const result = await addLastOrderedDate(mockItems, userId);

    for (const item of result) {
      expect(item.avg_cases_per_week).toEqual(0);
    }
  });
});
