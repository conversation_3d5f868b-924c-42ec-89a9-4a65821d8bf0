import { Knex } from "knex";
import knex from "../../../knex/knex";
import { Promotion, PromotionUsage } from "../../generated/graphql";
import { getPromotion, getPromotions } from "./getPromotions";

export const getPromotionTypes = async (): Promise<any[]> => {
  const types = await knex
    .select("*")
    .from("promotion_types")
    .where("active", true)
    .orderBy("name");

  return types.map((type) => ({
    id: type.id,
    name: type.name,
    code: type.code,
    description: type.description,
    active: type.active,
    created_at: type.created_at,
    updated_at: type.updated_at,
  }));
};

export const getApplicablePromotionsForItem = async (
  supplierId: string,
  userId: string,
  itemId: string
): Promise<Promotion[]> => {
  const now = new Date();
  const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const endOfDay = new Date(
    now.getFullYear(),
    now.getMonth(),
    now.getDate(),
    23,
    59,
    59,
    999
  );

  const { promotions } = await getPromotions({
    supplierId,
    filters: {
      active: true,
      dateRange: [startOfDay, endOfDay],
    },
  });

  // Process each promotion to check if it's applicable
  const applicablePromotions = promotions.filter((promotion) => {
    // Check if the promotion applies to the items in cart
    const hasApplicableItems =
      promotion.applies_to_all_items ||
      (promotion.items &&
        promotion.items.some((item) => itemId === item.id.toString()));
    if (!hasApplicableItems) {
      return false;
    }

    // Check if the promotion applies to the user
    const isUserEligible =
      promotion.applies_to_all_users ||
      (promotion.users && promotion.users.some((user) => user.id === userId));
    if (!isUserEligible) {
      return false;
    }
    return true;
  });

  return applicablePromotions;
};

export const getApplicablePromotions = async (
  supplierId: string,
  userId: string,
  items: Array<{ item_id: string; quantity: number; price: number }>
): Promise<any[]> => {
  const now = new Date();
  const orderSubtotal = items.reduce(
    (sum, item) => sum + item.price * item.quantity,
    0
  );
  const itemIds = items.map((item) => item.item_id);

  // Get all active promotions for this supplier
  // Use a wider date range to be more lenient with timing
  const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const endOfDay = new Date(
    now.getFullYear(),
    now.getMonth(),
    now.getDate(),
    23,
    59,
    59,
    999
  );

  const { promotions } = await getPromotions({
    supplierId,
    filters: {
      active: true,
      dateRange: [startOfDay, endOfDay],
    },
  });

  // Process each promotion to check if it's applicable
  const applicablePromotions = promotions.filter((promotion) => {
    // Check minimum order amount
    if (
      promotion.min_order_amount &&
      orderSubtotal < promotion.min_order_amount
    ) {
      return false;
    }

    // Check if the promotion applies to the items in cart
    const hasApplicableItems =
      promotion.applies_to_all_items ||
      (promotion.items &&
        promotion.items.some((item) => itemIds.includes(item.id.toString())));
    if (!hasApplicableItems) {
      return false;
    }

    // Check if the promotion applies to the user
    const isUserEligible =
      promotion.applies_to_all_users ||
      (promotion.users && promotion.users.some((user) => user.id === userId));
    if (!isUserEligible) {
      return false;
    }

    // Check if the items meet the quantity requirements based on promotion type
    switch (promotion.promotion_type.code) {
      case "BUY_X_GET_PRICE":
      case "BUY_X_GET_DISCOUNT": {
        // Check if any item meets the buy quantity requirement
        const hasEnoughQuantity = items.some((item) => {
          const promotionItem = promotion.items?.find(
            (pi) => pi.id.toString() === item.item_id
          );
          const result =
            (promotion.applies_to_all_items || promotionItem) &&
            item.quantity >= promotion.buy_quantity;
          return result;
        });
        return hasEnoughQuantity;
      }

      case "BUY_X_GET_Y_FREE": {
        // Check if any item has enough quantity for at least one complete set
        const hasEnoughForFree = items.some((item) => {
          const promotionItem = promotion.items?.find(
            (pi) => pi.id.toString() === item.item_id
          );

          const result =
            (promotion.applies_to_all_items || promotionItem) &&
            item.quantity >= promotion.buy_quantity + promotion.free_quantity;
          return result;
        });
        return hasEnoughForFree;
      }

      case "FIXED_DISCOUNT":
      case "PERCENTAGE_OFF":
        // These types always apply if we've made it this far
        return true;

      default:
        return false;
    }
  });

  return applicablePromotions;
};

export const calculatePromotionDiscount = async (
  supplierId: string,
  promotionId: string,
  items: Array<{ item_id: string; quantity: number; price: number }>,
  orderSubtotal: number
): Promise<{
  discountAmount: number;
  affectedItems: Array<{ item_id: string; discountAmount: number }>;
}> => {
  const promotion = await getPromotion(supplierId, promotionId);
  if (!promotion) throw new Error("Promotion not found");

  let discountAmount = 0;
  const affectedItems = [];

  switch (promotion.promotion_type.code) {
    case "BUY_X_GET_PRICE":
      // Apply fixed price when buying X quantity
      for (const item of items) {
        if (
          (promotion.applies_to_all_items ||
            promotion.items.some((i) => i.id.toString() === item.item_id)) &&
          item.quantity >= promotion.buy_quantity
        ) {
          discountAmount += Number(promotion.discount_amount);
          affectedItems.push({
            item_id: item.item_id,
            discountAmount: Number(promotion.discount_amount),
          });
        }
      }
      break;

    case "BUY_X_GET_DISCOUNT":
      // Apply percentage discount when buying X quantity
      for (const item of items) {
        if (
          (promotion.applies_to_all_items ||
            promotion.items.some((i) => i.id.toString() === item.item_id)) &&
          item.quantity >= promotion.buy_quantity
        ) {
          // Apply discount to all items when buy quantity is met
          const itemTotal = item.price * item.quantity;
          const itemDiscount =
            itemTotal * (promotion.discount_percentage / 100);
          discountAmount += itemDiscount;
          affectedItems.push({
            item_id: item.item_id,
            discountAmount: itemDiscount,
          });
        }
      }
      break;

    case "BUY_X_GET_Y_FREE":
      // Get Y items free when buying X quantity
      for (const item of items) {
        if (
          (promotion.applies_to_all_items ||
            promotion.items.some((i) => i.id.toString() === item.item_id)) &&
          item.quantity >= promotion.buy_quantity
        ) {
          const sets = Math.floor(
            item.quantity / (promotion.buy_quantity + promotion.free_quantity)
          );
          const freeItems = sets * promotion.free_quantity;
          const itemDiscount = item.price * freeItems;
          discountAmount += itemDiscount;
          affectedItems.push({
            item_id: item.item_id,
            discountAmount: itemDiscount,
          });
        }
      }
      break;

    case "FIXED_DISCOUNT":
      // Apply fixed amount discount to order
      discountAmount = Number(promotion.discount_amount);
      break;

    case "PERCENTAGE_OFF":
      // Apply percentage discount to order
      discountAmount = orderSubtotal * (promotion.discount_percentage / 100);
      break;
  }

  return { discountAmount, affectedItems };
};

export const applyPromotion = async (
  promotionId: string,
  orderId: string,
  userId: string,
  discountAmount: number
): Promise<void> => {
  await knex.transaction(async (trx) => {
    // Record promotion usage
    await trx("promotion_usage").insert({
      promotion_id: promotionId,
      user_id: userId,
      order_id: orderId,
      discount_amount: discountAmount,
    });

    // Update order with discount
    await trx("order_detail")
      .where("id", orderId)
      .update({
        discount: knex.raw("COALESCE(discount, 0) + ?", [discountAmount]),
      });
  });
};

export const recordPromotionUsage = async (
  promotions: Array<{
    id: string;
    discountAmount: number;
  }>,
  orderId: string,
  userId: string,
  trx?: Knex.Transaction
): Promise<void> => {
  const knexInstance = trx || knex;

  await knexInstance("promotion_usage").insert(
    promotions.map((promo) => ({
      promotion_id: promo.id,
      user_id: userId,
      order_id: orderId,
      discount_amount: promo.discountAmount,
    }))
  );
};

export const getPromotionUsageForOrders = async (
  supplierId: string,
  orderIds: string[]
): Promise<PromotionUsage[]> => {
  // Get promotion usage records for these orders
  const promotionUsage = await knex
    .select(
      "promotion_usage.id",
      "promotion_usage.promotion_id",
      "promotion_usage.user_id",
      "promotion_usage.order_id",
      "promotion_usage.used_at",
      "promotion_usage.created_at",
      "promotions.*",
      "promotion_types.id as type_id",
      "promotion_types.name as type_name",
      "promotion_types.code as type_code",
      "promotion_types.description as type_description",
      "promotion_types.active as type_active",
      "promotion_types.created_at as type_created_at",
      "promotion_types.updated_at as type_updated_at"
    )
    .from("promotion_usage")
    .leftJoin("promotions", "promotion_usage.promotion_id", "promotions.id")
    .leftJoin(
      "promotion_types",
      "promotions.promotion_type_id",
      "promotion_types.id"
    )
    .whereIn("order_id", orderIds)
    .where("promotions.supplier_id", supplierId);

  // Transform the raw data to match the PromotionUsage type
  return Promise.all(
    promotionUsage.map(async (usage) => {
      // Transform promotion type
      const promotionType = {
        id: usage.type_id,
        name: usage.type_name,
        code: usage.type_code,
        description: usage.type_description,
        active: usage.type_active,
        created_at: usage.type_created_at,
        updated_at: usage.type_updated_at,
      };

      // Get items and users for the promotion if needed
      let items = undefined;
      let users = undefined;

      if (!usage.applies_to_all_items) {
        items = await knex
          .select("item.*")
          .from("promotion_items")
          .join("item", "promotion_items.item_id", "item.id")
          .where("promotion_items.promotion_id", usage.promotion_id);
      }

      if (!usage.applies_to_all_users) {
        users = await knex
          .select("attain_user.*")
          .from("promotion_users")
          .join("attain_user", "promotion_users.user_id", "attain_user.id")
          .where("promotion_users.promotion_id", usage.promotion_id);
      }

      // Build the promotion object
      const promotion: Promotion = {
        id: usage.promotion_id,
        name: usage.name,
        supplier_id: usage.supplier_id,
        promotion_type: promotionType,
        start_date: usage.start_date,
        end_date: usage.end_date,
        applies_to_all_items: usage.applies_to_all_items,
        applies_to_all_users: usage.applies_to_all_users,
        max_uses_per_customer: usage.max_uses_per_customer,
        total_usage_limit: usage.total_usage_limit,
        buy_quantity: usage.buy_quantity,
        discount_amount: usage.discount_amount,
        discount_percentage: usage.discount_percentage,
        free_quantity: usage.free_quantity,
        min_order_amount: usage.min_order_amount,
        active: usage.active,
        archived: usage.archived,
        created_at: usage.created_at,
        updated_at: usage.updated_at,
        items,
        users,
        usage_count: await knex("promotion_usage")
          .where("promotion_id", usage.promotion_id)
          .count("id")
          .first()
          .then((result) => parseInt(result.count as string, 10)),
      };

      // Return the PromotionUsage object
      return {
        id: usage.id,
        promotion_id: usage.promotion_id,
        promotion,
        user_id: usage.user_id,
        order_id: usage.order_id,
        used_at: usage.used_at || usage.created_at, // Fallback to created_at if used_at is null
        created_at: usage.created_at,
      };
    })
  );
};
