import db from "../../../knex/knex";
import { DeleteCreditInput } from "../../generated/graphql";
import { CreditRow } from "./db.types";

async function deleteCredit(
  deleteCreditInput: DeleteCreditInput
): Promise<string> {
  const { id } = deleteCreditInput;

  const trx = await db.transaction();

  try {
    await trx<CreditRow>("credit").where({ id }).update({
      archived: true,
      updated_at: new Date(),
    });

    await trx.commit();

    return id.toString();
  } catch (error) {
    await trx.rollback();
    throw error;
  }
}

export default deleteCredit;
