describe("Knex", () => {
  const OLD_ENV = process.env; // Save current environment

  beforeEach(() => {
    jest.resetModules(); // Most important - it clears the cache
    process.env = { ...OLD_ENV }; // Make a copy of the environment
  });

  // After the tests we will stop our server
  afterAll(async () => {
    process.env = OLD_ENV; // Restore old environment
  });

  it("uses development credentials in undefined environment", () => {
    process.env.NODE_ENV = undefined;
    const knex = require("./knex").default;
    expect(knex.client.config).toBeTruthy();
    expect(knex.client.config.seeds.directory).toContain("dev");
  });
});
