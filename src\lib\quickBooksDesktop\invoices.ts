import {
  CreditMemoAdd,
  CreditMemoMod,
  CreditMemoRet,
  InventoryAdjustmentRet,
  InvoiceAdd,
  InvoiceMod,
  InvoiceRet,
} from "conductor-node/dist/src/integrations/qbd/qbdTypes";
import dayjs from "dayjs";
import { Invoice, InvoiceItem } from "../../generated/graphql";
import QuickBooksDesktopClient from "./client";

export const mapDBInvoiceToQB = (
  invoice: Invoice
): InvoiceAdd | CreditMemoAdd => {
  return {
    CustomerRef: {
      ListID: invoice.customerDetails.qb_id,
    },
    TxnDate: dayjs(invoice.date_created).format("YYYY-MM-DD"),
    [invoice.subtotal >= 0 ? "InvoiceLineAdd" : "CreditMemoLineAdd"]:
      invoice.invoiceItems
        .filter((i) => i.id)
        .map((invoiceItem: InvoiceItem) => ({
          Rate: String(invoiceItem.price),
          Quantity: Math.abs(invoiceItem.quantity),
          ItemRef: {
            ListID: invoiceItem.qb_id,
          },
          ...(!invoiceItem.name.toLowerCase().includes("(z")
            ? {
                InventorySiteRef: {
                  FullName: "OPERATIONS CENTER",
                },
              }
            : {}),
        })),
    RefNumber: invoice.id,
    Other: `ATTAIN-${invoice.id}`,
  };
};

export const pushInvoicesInfo = async (
  client: QuickBooksDesktopClient,
  invoices: Invoice[],
  callbackForEachInvoice?: (invoice: InvoiceRet) => Promise<void>
) => {
  const qbInvoices: InvoiceAdd[] = invoices.map(mapDBInvoiceToQB);
  const returnedQBInvoices: InvoiceRet[] = [];
  for (const invoice of qbInvoices) {
    const returnedInvoice = await client.createInvoice(invoice).catch((e) => {
      console.error("Error creating invoice", invoice.RefNumber, e);
      return {} as InvoiceRet;
    });
    returnedQBInvoices.push(returnedInvoice);
    if (callbackForEachInvoice) {
      await callbackForEachInvoice(returnedInvoice);
    }
  }
  return returnedQBInvoices;
};

export const pushCreditMemosInfo: (
  client: QuickBooksDesktopClient,
  creditMemos: Invoice[],
  addInventoryAdjustments?: boolean,
  callbackForEachCreditMemo?: (creditMemo: CreditMemoRet) => Promise<void>
) => Promise<[CreditMemoRet[], InventoryAdjustmentRet[]]> = async (
  client: QuickBooksDesktopClient,
  creditMemos: Invoice[],
  addInventoryAdjustments = false,
  callbackForEachCreditMemo
) => {
  const qbCreditMemos: CreditMemoAdd[] = creditMemos.map(mapDBInvoiceToQB);
  const returnedQBCreditMemos: CreditMemoRet[] = [];
  for (const creditMemo of qbCreditMemos) {
    const returnedCreditMemo = await client
      .createCreditMemo(creditMemo)
      .catch((e) => {
        console.error("Error creating credit memo", creditMemo.RefNumber, e);
        return {} as CreditMemoRet;
      });
    returnedQBCreditMemos.push(returnedCreditMemo);
    if (callbackForEachCreditMemo) {
      await callbackForEachCreditMemo(returnedCreditMemo);
    }
  }

  if (addInventoryAdjustments) {
    const inventoryAdjustments: InventoryAdjustmentRet[] = [];
    for (let i = 0; i < returnedQBCreditMemos.length; ++i) {
      const creditMemo = returnedQBCreditMemos[i];
      inventoryAdjustments.push(
        creditMemo.RefNumber
          ? await client
              .createInventoryAdjustment({
                TxnDate: creditMemo.TxnDate,
                AccountRef: {
                  FullName: "Cost of Goods Sold",
                },
                InventorySiteRef: {
                  FullName: "OPERATIONS CENTER",
                },
                Memo: `Created By Attain for credit ${creditMemos[i].id}`,
                InventoryAdjustmentLineAdd: creditMemos[i].invoiceItems
                  .filter((i) => !i.name.toLowerCase().includes("(z"))
                  .reduce<InvoiceItem[]>((acc, invoiceItem) => {
                    const foundExisting = acc.findIndex(
                      (i) => i.qb_id === invoiceItem.qb_id
                    );
                    if (foundExisting !== -1) {
                      acc[foundExisting].quantity += invoiceItem.quantity;
                    } else {
                      acc.push(invoiceItem);
                    }
                    return acc;
                  }, [])
                  .map((invoiceItem: InvoiceItem) => ({
                    QuantityAdjustment: {
                      QuantityDifference: -1 * Math.abs(invoiceItem.quantity),
                    },
                    ItemRef: {
                      ListID: invoiceItem.qb_id,
                    },
                  })),
              })
              .catch((e) => {
                console.error(
                  "Error creating inventory adjustment for credit memo",
                  creditMemo.RefNumber,
                  e
                );
                return {} as InventoryAdjustmentRet;
              })
          : ({} as InventoryAdjustmentRet)
      );
    }

    return [returnedQBCreditMemos, inventoryAdjustments];
  }

  return [returnedQBCreditMemos, []];
};

export const pushUpdatedInvoicesInfo = async (
  client: QuickBooksDesktopClient,
  invoices: Invoice[]
) => {
  const qbInvoices = invoices.map(mapDBInvoiceToQB) as InvoiceMod[];
  return await Promise.all(
    qbInvoices.map((invoice) => client.updateInvoice(invoice))
  );
};

export const pushUpdatedCreditMemosInfo = async (
  client: QuickBooksDesktopClient,
  creditMemos: Invoice[]
) => {
  const qbCreditMemos = creditMemos.map(mapDBInvoiceToQB) as CreditMemoMod[];
  return await Promise.all(
    qbCreditMemos.map((creditMemo) => client.updateCreditMemo(creditMemo))
  );
};
