import {
  mockKnex,
  queryBuilder as queryBuilderOriginal,
} from "../../../knex/mock";
import ItemsByFilterQuery from "./itemsByFilter";

mockKnex();
const mockedAddLastOrderedDate = jest.fn();
jest.mock("../../services/addLastOrderedDate", () => mockedAddLastOrderedDate);

describe("Items by Filter query", () => {
  let itemsByFilter: typeof ItemsByFilterQuery;
  let queryBuilder: typeof queryBuilderOriginal;

  beforeAll(async () => {
    ({ default: itemsByFilter } = await import("./itemsByFilter"));
    queryBuilderOriginal.where.mockImplementation((builderFn) => {
      if (typeof builderFn === "function") {
        builderFn.apply(queryBuilder, [queryBuilder]);
      }
      return queryBuilder;
    });
    queryBuilder = { ...queryBuilderOriginal };
  });
  beforeEach(() => {
    queryBuilder = { ...queryBuilderOriginal };
  });

  const getItemsByFilterInput = {
    pagination: {
      offset: 0,
      limit: 5,
    },
  };

  it("adds last ordered dat when passed userId", async () => {
    const userId = "1234";
    queryBuilder.offset.mockResolvedValue([]);

    await itemsByFilter(undefined, {
      getItemsByFilterInput: { ...getItemsByFilterInput, userId },
    });

    expect(queryBuilder.where).toHaveBeenCalledTimes(1);
    expect(queryBuilder.whereRaw).not.toHaveBeenCalled();
    expect(queryBuilder.orWhereRaw).not.toHaveBeenCalled();
    expect(mockedAddLastOrderedDate).toHaveBeenCalledWith(
      expect.anything(),
      userId
    );
  });

  it("does not last ordered date when userId not passed", async () => {
    queryBuilder.offset.mockResolvedValue([]);

    await itemsByFilter(undefined, { getItemsByFilterInput });

    expect(queryBuilder.where).toHaveBeenCalledTimes(1);
    expect(queryBuilder.whereRaw).not.toHaveBeenCalled();
    expect(queryBuilder.orWhereRaw).not.toHaveBeenCalled();
    expect(mockedAddLastOrderedDate).not.toHaveBeenCalled();
  });

  it("does not filter by either category or map when neither provided", async () => {
    queryBuilder.offset.mockResolvedValue([]);

    await itemsByFilter(undefined, { getItemsByFilterInput });

    expect(queryBuilder.where).toHaveBeenCalledTimes(1);
    expect(queryBuilder.whereRaw).not.toHaveBeenCalled();
    expect(queryBuilder.orWhereRaw).not.toHaveBeenCalled();
    expect(mockedAddLastOrderedDate).not.toHaveBeenCalled();
  });

  const categoryTests = [
    { category: "tobacco", expectedLength: 2 },
    { category: "snack", expectedLength: 2 },
    { category: "pastry", expectedLength: 4 },
    { category: "gum", expectedLength: 1 },
    { category: "beverage", expectedLength: 2 },
    { category: "health", expectedLength: 2 },
    { category: "cleaning", expectedLength: 30 },
    { category: "energy", expectedLength: 1 },
    { category: "alcohol", expectedLength: 2 },
  ];
  it.each(categoryTests)(
    "filters only by $category category when only category provided",
    async ({ category, expectedLength }) => {
      await itemsByFilter(undefined, {
        getItemsByFilterInput: {
          ...getItemsByFilterInput,
          category,
        },
      });

      expect(queryBuilder.where).toHaveBeenCalledTimes(2);
      expect(queryBuilder.whereRaw).toHaveBeenCalledWith(
        "nacs_category ilike ANY(?)",
        [Array(expectedLength).fill(expect.anything())]
      );
      expect(queryBuilder.orWhereRaw).toHaveBeenCalledWith(
        "nacs_subcategory ilike ANY(?)",
        [Array(expectedLength).fill(expect.anything())]
      );
      expect(queryBuilder.whereRaw).not.toHaveBeenCalledWith(
        "name ilike ANY(?)",
        expect.anything()
      );
      expect(mockedAddLastOrderedDate).not.toHaveBeenCalled();
    }
  );

  const tagTests: { tag: string; expectedLength?: number }[] = [
    { tag: "marlboro" },
    { tag: "american_spirit" },
    { tag: "camel" },
    { tag: "newport" },
    { tag: "lucky_strike" },
    { tag: "pall_mall" },
    { tag: "swisher_sweet" },
    { tag: "dutch" },
    { tag: "fortuna" },
    { tag: "bon_appetite", expectedLength: 2 },
    { tag: "lays" },
    { tag: "taki" },
    { tag: "trident", expectedLength: 2 },
    { tag: "doublemint" },
    { tag: "orbit" },
    { tag: "mentos" },
    { tag: "stride" },
    { tag: "sprite" },
    { tag: "gatorade" },
    { tag: "powerade" },
    { tag: "arizona" },
    { tag: "snapple" },
    { tag: "bandaid" },
    { tag: "allegra" },
    { tag: "tylenol" },
    { tag: "nyquil" },
    { tag: "advil" },
    { tag: "detergent" },
    { tag: "dish_soap" },
    { tag: "bleach" },
    { tag: "ariel" },
    { tag: "lirio" },
    { tag: "tide" },
    { tag: "clorox" },
    { tag: "mr_clean" },
    { tag: "lysol" },
    { tag: "airwick" },
    { tag: "glad" },
    { tag: "dawn_ultra" },
    { tag: "downy" },
    { tag: "cloralen" },
    { tag: "lucky" },
  ];
  it.each(tagTests)(
    "filters only by $tag tag when only tag provided",
    async ({ tag, expectedLength }) => {
      await itemsByFilter(undefined, {
        getItemsByFilterInput: {
          ...getItemsByFilterInput,
          tag,
        },
      });

      expect(queryBuilder.where).toHaveBeenCalledTimes(1);
      expect(queryBuilder.whereRaw).not.toHaveBeenCalledWith(
        "nacs_category ilike ANY(?)",
        expect.anything()
      );
      expect(queryBuilder.orWhereRaw).not.toHaveBeenCalledWith(
        "nacs_subcategory ilike ANY(?)",
        expect.anything()
      );
      expect(queryBuilder.whereRaw).toHaveBeenCalledWith("name ilike ANY(?)", [
        Array(expectedLength ?? 1).fill(expect.anything()),
      ]);
      expect(mockedAddLastOrderedDate).not.toHaveBeenCalled();
    }
  );
});
