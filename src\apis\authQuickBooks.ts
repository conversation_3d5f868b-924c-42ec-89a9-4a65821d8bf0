import { Request, Response } from "express";
import RestEndpoint from "./_restEndpoint";

import OAuthClient from "intuit-oauth-ts";
import { ApolloServer } from "@apollo/server";
import Expo from "expo-server-sdk";
import SimpleCrypto from "simple-crypto-js";
import QuickBooksClient from "../lib/quickBooks/client";
import { sync } from "../services/integrationService/quickBooks";
import knex from "../../knex/knex";
export default class AuthQuickBooks extends RestEndpoint {
  private intuitOAuthClient: OAuthClient;
  private simpleCryptoClient: SimpleCrypto;
  constructor(
    apolloServerInit: ApolloServer,
    expoClient: Expo,
    oAuthClient: OAuthClient,
    simpleCrypto: SimpleCrypto
  ) {
    super(apolloServerInit, expoClient);
    this.intuitOAuthClient = oAuthClient;
    this.simpleCryptoClient = simpleCrypto;
  }
  public async handler(req: Request, res: Response) {
    const redirectUri = await this.worker(req.url, req.query, req.params);
    if (!redirectUri) res.status(500).send("Error connecting QuickBooks.");
    else res.redirect(redirectUri);
  }

  protected async worker(url, queryParams, urlParams) {
    // console.log("QB Auth url params:", urlParams);
    // console.log("QB Auth query params:", queryParams);
    const { token } = await this.intuitOAuthClient.createToken(url);
    const [supplierName, supplierId, origin] = (
      this.simpleCryptoClient.decrypt(queryParams.state) as string
    ).split("/#");
    // console.log(supplierName, supplierId, origin);

    await knex("supplier")
      .update({
        qb_realm_id: queryParams.realmId,
        qb_access_token: token.access_token,
        qb_refresh_token: token.refresh_token,
      })
      .where({ id: supplierId, name: supplierName });

    const qClient = new QuickBooksClient(
      token.access_token,
      queryParams.realmId,
      token.refresh_token
    );

    try {
      await sync(qClient, supplierId, supplierName, false, false);
      return `${origin}/catalog?qb=1`;
    } catch (error) {
      console.error(`Error syncing QB:`, error);
    }
  }
}
