import { GraphQLError } from "graphql";
import { MutationUpsertCustomPricesArgs } from "../../generated/graphql";
import knex from "../../../knex/knex";

const upsertCustomPrices = async (_, args: MutationUpsertCustomPricesArgs) => {
  const { customPrices } = args;
  if (!customPrices.length) {
    throw new GraphQLError("No custom prices provided");
  }
  return await knex("custom_item_price")
    .insert(customPrices)
    .onConflict(["user_id", "item_id"])
    .merge()
    .returning("*");
};

export default upsertCustomPrices;
