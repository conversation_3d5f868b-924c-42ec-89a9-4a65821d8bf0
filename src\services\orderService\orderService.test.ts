import { getOrders, getExpectedOrders } from "./orderService";
import knex from "../../../knex/knex";
import { getUsersScheduledOnDay } from "../userService/userService";

jest.mock("../../../knex/knex");
jest.mock("../../util/dayjsConfig", () =>
  jest.fn((d) => ({
    utc: () => ({ format: () => "2025-01-01" }),
    toDate: () => new Date("2025-01-01"),
  }))
);
jest.mock("../itemService/modifyToCustomPricing");
jest.mock("../userService/userService");

describe("getOrders", () => {
  beforeEach(() => jest.clearAllMocks());

  it("throws error if supplierId is not provided", async () => {
    await expect(getOrders({ supplierId: "" })).rejects.toThrow(
      "Supplier ID is required"
    );
  });

  it("returns orders and totalCount with default filters", async () => {
    const fakeOrders = [{ id: "order1", user_id: "user1" }];
    const fakeDbResult = [{ ...fakeOrders[0], total_count: "1" }];

    (knex.select as jest.Mock)
      // 1. Mock for the getSupplierName query
      .mockReturnValueOnce({
        from: jest.fn().mockReturnThis(),
        where: jest.fn().mockResolvedValue([{ name: "SupplierX" }]),
      })
      .mockReturnValueOnce({
        from: jest.fn().mockReturnThis(),
        rightOuterJoin: jest.fn().mockReturnThis(),
        leftOuterJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        modify: jest.fn().mockReturnThis(),
      });

    // Mock the `fullQuery` chain which starts with `knex.with`
    (knex.with as jest.Mock).mockReturnValue({
      select: jest.fn().mockReturnThis(),
      from: jest.fn().mockReturnThis(),
      orderByRaw: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      offset: jest.fn().mockReturnThis(),
      rightJoin: jest.fn().mockResolvedValue(fakeDbResult),
    });

    // Spy on and mock the internal call to getPopulatedOrders
    const populatedMock = jest
      .spyOn(require("./orderService"), "getPopulatedOrders")
      .mockResolvedValue(fakeOrders);

    const result = await getOrders({ supplierId: "1" });

    expect(result.orders).toEqual(fakeOrders);
    expect(result.totalCount).toBe(1);
    expect(populatedMock).toHaveBeenCalled();
  });
});

describe("getExpectedOrders", () => {
  beforeEach(() => jest.clearAllMocks());

  it("returns expected orders for users without an order", async () => {
    const supplierId = "s1";
    const date = new Date("2025-01-01");
    const users = [{ id: "u1" }, { id: "u2" }];
    const placedOrders = [{ customerDetails: { id: "u1" } }];

    (getUsersScheduledOnDay as jest.Mock).mockResolvedValue(users);

    // Spy on the internal call to getOrders and provide a mock return value
    jest.spyOn(require("./orderService"), "getOrders").mockResolvedValue({
      orders: placedOrders,
      totalCount: 1,
    });

    const result = await getExpectedOrders(supplierId, date);

    expect(result).toHaveLength(1);
    expect(result[0].id).toBe("u2");
    expect(result[0].status).toBe("Expected");
  });
});
