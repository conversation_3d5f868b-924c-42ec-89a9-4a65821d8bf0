import knex from "../../../knex/knex";
import { QueryCutoffTimesArgs } from "../../generated/graphql";

const CutoffTimes = async (_, args: QueryCutoffTimesArgs) => {
  const { businessId } = args.getCutoffTimesInput;
  const supplierRows = await knex
    .select()
    .table("supplier_times")
    .where("business_id", businessId)
    .orderBy("id", "asc");

  const results = await Promise.all(
    supplierRows.map(async (row) => {
      const supplierData = await knex
        .select()
        .table("supplier")
        .where("id", row["supplier_id"]);
      if (
        supplierData.length === 0 ||
        typeof supplierData[0]["name"] !== "string"
      ) {
        return null;
      }

      return {
        id: row["id"],
        supplier: supplierData[0]["name"],
        supplierInfo: supplierData[0],
        cutoffDay: row["cutoff_day"],
        cutoffTime: row["cutoff_time"],
        deliveryDay: row["delivery_day"],
        deliveryTime: row["delivery_time"],
        daysToDelivery: row["days_to_delivery"],
      };
    })
  );

  return results.filter((result) => result !== null);
};

export default CutoffTimes;
