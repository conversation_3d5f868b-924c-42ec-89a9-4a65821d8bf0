import dayjs from "dayjs";
import knex from "../../../knex/knex";
import { Route } from "../../generated/graphql";

export const getRoutes = async (
  supplierId: string,
  filters?: { routeId?: string }
): Promise<Route[]> => {
  let query = knex("route").select("*").where("supplier_id", supplierId);

  if (filters?.routeId) {
    query = query.where("id", filters.routeId);
  }

  const routes = await query;
  return routes;
};

export const getRoutesOnDay = async (
  supplierId: string,
  date: Date
): Promise<Route[]> => {
  let routes = await knex("route")
    .select("*")
    .where("supplier_id", supplierId)
    .where("day_of_week", dayjs.utc(date).format("dddd"));
  routes = routes.filter((route) => route.name !== "UNASSIGNED");
  return routes;
};
