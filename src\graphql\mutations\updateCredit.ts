import knex from "../../../knex/knex";
import { updateCredit } from "../../services/creditService";
import { UpdateCreditInput } from "../../generated/graphql";

export async function updateCreditResolver(
  _: any,
  { updateCreditInput }: { updateCreditInput: UpdateCreditInput }
) {
  const trx = await knex.transaction();
  try {
    const credit = await updateCredit(trx, updateCreditInput);
    await trx.commit();
    return credit;
  } catch (error) {
    await trx.rollback();
    throw error;
  }
}

export default updateCreditResolver;
