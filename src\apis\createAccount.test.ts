import CreateAccount from "./createAccount";

describe("createAccount REST endpoint", () => {
  const mockReq = {
    body: '{"test": "key"}',
  };
  const mockRes = {
    send: jest.fn().mockReturnThis(),
    status: jest.fn().mockReturnThis(),
  };
  const createAccountApi: CreateAccount = new CreateAccount(
    jest.fn() as never,
    jest.fn() as never
  );
  const endpointApolloHttpPostSpy: jest.SpyInstance = jest.spyOn(
    createAccountApi as never,
    "apolloHttpPost"
  );

  beforeEach(() => {
    endpointApolloHttpPostSpy.mockClear();
  });

  it("sends successful result after creating account", async () => {
    const result = {
      createAccountFromBalance: "success",
    };
    endpointApolloHttpPostSpy.mockResolvedValue(result as never);

    await createAccountApi.handler(mockReq as never, mockRes as never);

    expect(endpointApolloHttpPostSpy).toHaveBeenCalled();
    expect(mockRes.send).toHaveBeenCalledWith(result.createAccountFromBalance);
    expect(mockRes.status).not.toHaveBeenCalled();
  });

  it("sends error 500 when unknown error occurred", async () => {
    const errorMessage = "some weird error";
    endpointApolloHttpPostSpy.mockImplementation(() => {
      throw errorMessage;
    });

    await createAccountApi.handler(mockReq as never, mockRes as never);

    expect(endpointApolloHttpPostSpy).toHaveBeenCalled();
    expect(mockRes.status).toHaveBeenCalledWith(500);
    expect(mockRes.send).toHaveBeenCalledWith(
      `Error creating account: ${errorMessage}`
    );
  });

  it("sends custom error code when known error occurred", async () => {
    const error = {
      code: 400,
      message: "not weird error",
    };
    endpointApolloHttpPostSpy.mockImplementation(() => {
      throw error;
    });

    await createAccountApi.handler(mockReq as never, mockRes as never);

    expect(endpointApolloHttpPostSpy).toHaveBeenCalled();
    expect(mockRes.status).toHaveBeenCalledWith(error.code);
    expect(mockRes.send).toHaveBeenCalledWith(
      `Error creating account: ${error.message}`
    );
  });
});
