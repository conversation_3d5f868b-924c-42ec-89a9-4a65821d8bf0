import { MutationResolvers } from "../../generated/graphql";
import { upsertSupplierConfig } from "../../services/supplierService/supplierService";

export const createSupplierConfigMutation: MutationResolvers["createSupplierConfig"] =
  async (_, { input }) => {
    const { supplierId, show_order_tabs } = input;
    const config = await upsertSupplierConfig(
      supplierId,
      "show_order_tabs",
      show_order_tabs
    );
    if (!config) {
      throw new Error(
        `Failed to create supplier config for supplier ${supplierId}`
      );
    }
    return {
      show_order_tabs: config.show_order_tabs,
    };
  };

export const updateSupplierConfigMutation: MutationResolvers["updateSupplierConfig"] =
  async (_, { input }) => {
    const { supplierId, show_order_tabs } = input;
    const config = await upsertSupplierConfig(
      supplierId,
      "show_order_tabs",
      show_order_tabs
    );
    if (!config) {
      throw new Error(
        `Failed to update supplier config for supplier ${supplierId}`
      );
    }
    return {
      show_order_tabs: config.show_order_tabs,
    };
  };
