export const queryBuilder = {
  select: jest.fn().mockReturnThis(),
  insert: jest.fn().mockReturnThis(),
  update: jest.fn().mockReturnThis(),
  del: jest.fn().mockReturnThis(),
  as: jest.fn().mockReturnThis(),
  columns: jest.fn().mockReturnThis(),
  column: jest.fn().mockReturnThis(),
  hintComment: jest.fn().mockReturnThis(),
  from: jest.fn().mockReturnThis(),
  fromRaw: jest.fn().mockReturnThis(),
  into: jest.fn().mockReturnThis(),
  table: jest.fn().mockReturnThis(),
  distinct: jest.fn().mockReturnThis(),
  distinctOn: jest.fn().mockReturnThis(),
  join: jest.fn().mockReturnThis(),
  joinRaw: jest.fn().mockReturnThis(),
  innerJoin: jest.fn().mockReturnThis(),
  leftJoin: jest.fn().mockReturnThis(),
  leftOuterJoin: jest.fn().mockReturnThis(),
  rightJoin: jest.fn().mockReturnThis(),
  rightOuterJoin: jest.fn().mockReturnThis(),
  outerJoin: jest.fn().mockReturnThis(),
  fullOuterJoin: jest.fn().mockReturnThis(),
  crossJoin: jest.fn().mockReturnThis(),
  jsonExtract: jest.fn().mockReturnThis(),
  jsonSet: jest.fn().mockReturnThis(),
  jsonInsert: jest.fn().mockReturnThis(),
  jsonRemove: jest.fn().mockReturnThis(),
  using: jest.fn().mockReturnThis(),
  with: jest.fn().mockReturnThis(),
  withMaterialized: jest.fn().mockReturnThis(),
  withNotMaterialized: jest.fn().mockReturnThis(),
  withRecursive: jest.fn().mockReturnThis(),
  withRaw: jest.fn().mockReturnThis(),
  withSchema: jest.fn().mockReturnThis(),
  withWrapped: jest.fn().mockReturnThis(),
  where: jest.fn().mockReturnThis(),
  andWhere: jest.fn().mockReturnThis(),
  orWhere: jest.fn().mockReturnThis(),
  whereNot: jest.fn().mockReturnThis(),
  andWhereNot: jest.fn().mockReturnThis(),
  orWhereNot: jest.fn().mockReturnThis(),
  whereRaw: jest.fn().mockReturnThis(),
  orWhereRaw: jest.fn().mockReturnThis(),
  andWhereRaw: jest.fn().mockReturnThis(),
  whereWrapped: jest.fn().mockReturnThis(),
  havingWrapped: jest.fn().mockReturnThis(),
  whereExists: jest.fn().mockReturnThis(),
  orWhereExists: jest.fn().mockReturnThis(),
  whereNotExists: jest.fn().mockReturnThis(),
  orWhereNotExists: jest.fn().mockReturnThis(),
  whereIn: jest.fn().mockReturnThis(),
  orWhereIn: jest.fn().mockReturnThis(),
  whereNotIn: jest.fn().mockReturnThis(),
  orWhereNotIn: jest.fn().mockReturnThis(),
  whereLike: jest.fn().mockReturnThis(),
  andWhereLike: jest.fn().mockReturnThis(),
  orWhereLike: jest.fn().mockReturnThis(),
  whereILike: jest.fn().mockReturnThis(),
  andWhereILike: jest.fn().mockReturnThis(),
  orWhereILike: jest.fn().mockReturnThis(),
  whereNull: jest.fn().mockReturnThis(),
  orWhereNull: jest.fn().mockReturnThis(),
  whereNotNull: jest.fn().mockReturnThis(),
  orWhereNotNull: jest.fn().mockReturnThis(),
  whereBetween: jest.fn().mockReturnThis(),
  orWhereBetween: jest.fn().mockReturnThis(),
  andWhereBetween: jest.fn().mockReturnThis(),
  whereNotBetween: jest.fn().mockReturnThis(),
  orWhereNotBetween: jest.fn().mockReturnThis(),
  andWhereNotBetween: jest.fn().mockReturnThis(),
  whereJsonObject: jest.fn().mockReturnThis(),
  orWhereJsonObject: jest.fn().mockReturnThis(),
  andWhereJsonObject: jest.fn().mockReturnThis(),
  whereNotJsonObject: jest.fn().mockReturnThis(),
  orWhereNotJsonObject: jest.fn().mockReturnThis(),
  andWhereNotJsonObject: jest.fn().mockReturnThis(),
  whereJsonPath: jest.fn().mockReturnThis(),
  orWhereJsonPath: jest.fn().mockReturnThis(),
  andWhereJsonPath: jest.fn().mockReturnThis(),
  whereJsonSupersetOf: jest.fn().mockReturnThis(),
  orWhereJsonSupersetOf: jest.fn().mockReturnThis(),
  andWhereJsonSupersetOf: jest.fn().mockReturnThis(),
  whereJsonNotSupersetOf: jest.fn().mockReturnThis(),
  orWhereJsonNotSupersetOf: jest.fn().mockReturnThis(),
  andWhereJsonNotSupersetOf: jest.fn().mockReturnThis(),
  whereJsonSubsetOf: jest.fn().mockReturnThis(),
  orWhereJsonSubsetOf: jest.fn().mockReturnThis(),
  andWhereJsonSubsetOf: jest.fn().mockReturnThis(),
  whereJsonNotSubsetOf: jest.fn().mockReturnThis(),
  orWhereJsonNotSubsetOf: jest.fn().mockReturnThis(),
  andWhereJsonNotSubsetOf: jest.fn().mockReturnThis(),
  groupBy: jest.fn().mockReturnThis(),
  groupByRaw: jest.fn().mockReturnThis(),
  orderBy: jest.fn().mockReturnThis(),
  orderByRaw: jest.fn().mockReturnThis(),
  partitionBy: jest.fn().mockReturnThis(),
  intersect: jest.fn().mockReturnThis(),
  union: jest.fn().mockReturnThis(),
  unionAll: jest.fn().mockReturnThis(),
  having: jest.fn().mockReturnThis(),
  andHaving: jest.fn().mockReturnThis(),
  havingRaw: jest.fn().mockReturnThis(),
  orHaving: jest.fn().mockReturnThis(),
  orHavingRaw: jest.fn().mockReturnThis(),
  havingIn: jest.fn().mockReturnThis(),
  orHavingNotBetween: jest.fn().mockReturnThis(),
  havingNotBetween: jest.fn().mockReturnThis(),
  orHavingBetween: jest.fn().mockReturnThis(),
  havingBetween: jest.fn().mockReturnThis(),
  havingNotIn: jest.fn().mockReturnThis(),
  andHavingNotIn: jest.fn().mockReturnThis(),
  orHavingNotIn: jest.fn().mockReturnThis(),
  count: jest.fn().mockReturnThis(),
  countDistinct: jest.fn().mockReturnThis(),
  min: jest.fn().mockReturnThis(),
  max: jest.fn().mockReturnThis(),
  sum: jest.fn().mockReturnThis(),
  sumDistinct: jest.fn().mockReturnThis(),
  avg: jest.fn().mockReturnThis(),
  avgDistinct: jest.fn().mockReturnThis(),
  limit: jest.fn().mockReturnThis(),
  offset: jest.fn().mockReturnThis(),
  modify: jest.fn().mockReturnThis(),
  merge: jest.fn().mockReturnThis(),
  onConflict: jest.fn().mockReturnThis(),
  returning: jest.fn().mockReturnThis(),
  first: jest.fn().mockReturnThis(),
  raw: jest.fn((sql) => sql),

  then: jest.fn(),
  catch: jest.fn(),
  clone: jest.fn().mockReturnThis(),
  clearSelect: jest.fn().mockReturnThis(),
  clearOrder: jest.fn().mockReturnThis(),
  delete: jest.fn().mockResolvedValue(undefined),
  transaction: jest.fn(),
  batchInsert: jest.fn().mockReturnThis(),
};

export const mockKnex = () => {
  jest.mock("knex", () =>
    jest.fn().mockReturnValue(Object.assign(() => queryBuilder, queryBuilder))
  );
};
