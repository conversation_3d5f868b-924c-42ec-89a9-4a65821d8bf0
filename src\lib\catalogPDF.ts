import { jsPDF } from "jspdf";
import { Item, CatalogTemplateSort } from "../generated/graphql";
import * as fs from "fs";
import * as path from "path";

export interface CatalogData {
  items: Item[];
  config: {
    category?: string;
    sort_by: CatalogTemplateSort;
    title: string;
    subtitle?: string;
    company_info: string;
    pricing_enabled: boolean;
    display_options: {
      show_sku: boolean;
      show_case_size: boolean;
      show_upc: boolean;
      show_stock: boolean;
    };
  };
}

export class CatalogPDFGenerator {
  private doc: jsPDF;
  private pageWidth: number;
  private pageHeight: number;
  private margin = 12; 
  private currentY = 20;
  private itemsPerRow = 4; 
  private itemWidth: number;
  private baseItemHeight = 38;

  constructor() {
    try {
      // Initialize jsPDF with basic configuration following latest best practices
      this.doc = new jsPDF({
        orientation: "portrait",
        unit: "mm",
        format: "a4",
        compress: true,
      });

      this.pageWidth = this.doc.internal.pageSize.getWidth();
      this.pageHeight = this.doc.internal.pageSize.getHeight();
      this.itemWidth =
        (this.pageWidth - this.margin * 2 - 15) / this.itemsPerRow; // Reduced gap from 20mm to 15mm for 4 items

      // Load custom fonts
      this.loadFonts();
    } catch (error) {
      console.error("Error initializing jsPDF:", error);
      throw new Error("Failed to initialize PDF generator");
    }
  }

  // Load Lato fonts into jsPDF
  private loadFonts(): void {
    try {
      const fontsPath = path.join(__dirname, "fonts");

      // Load Lato Regular
      const latoRegular = fs.readFileSync(
        path.join(fontsPath, "lato-regular.ttf")
      );
      this.doc.addFileToVFS("lato-regular.ttf", latoRegular.toString("base64"));
      this.doc.addFont("lato-regular.ttf", "Lato", "normal");

      // Load Lato Bold
      const latoBold = fs.readFileSync(path.join(fontsPath, "lato-bold.ttf"));
      this.doc.addFileToVFS("lato-bold.ttf", latoBold.toString("base64"));
      this.doc.addFont("lato-bold.ttf", "Lato", "bold");

      // Load Lato Italic
      const latoItalic = fs.readFileSync(
        path.join(fontsPath, "lato-italic.ttf")
      );
      this.doc.addFileToVFS("lato-italic.ttf", latoItalic.toString("base64"));
      this.doc.addFont("lato-italic.ttf", "Lato", "italic");

      // Load Lato Bold Italic
      const latoBoldItalic = fs.readFileSync(
        path.join(fontsPath, "lato-bold-italic.ttf")
      );
      this.doc.addFileToVFS(
        "lato-bold-italic.ttf",
        latoBoldItalic.toString("base64")
      );
      this.doc.addFont("lato-bold-italic.ttf", "Lato", "bolditalic");

      console.log("Lato fonts loaded successfully");
    } catch (error) {
      console.error("Error loading fonts:", error);
      console.log("Falling back to default fonts");
    }
  }

  // Sanitize text to prevent jsPDF issues
  private sanitizeText(text: string): string {
    if (!text || typeof text !== "string") return "";
    // Remove problematic characters that might cause jsPDF issues
    return text
      .replace(/[^\x20-\x7E\n\r\t]/g, "") // Keep only printable ASCII, newlines, carriage returns, and tabs
      .trim();
  }

  // Handle multiline text properly
  private splitMultilineText(text: string, maxWidth: number): string[] {
    const sanitized = this.sanitizeText(text);
    if (!sanitized) return [""];

    // Split by actual newlines first
    const paragraphs = sanitized.split(/\r?\n/);
    const allLines: string[] = [];

    paragraphs.forEach((paragraph) => {
      if (paragraph.trim()) {
        // Use jsPDF's splitTextToSize for each paragraph
        const lines = this.doc.splitTextToSize(paragraph.trim(), maxWidth);
        if (Array.isArray(lines)) {
          allLines.push(...lines);
        } else {
          allLines.push(lines);
        }
      } else {
        allLines.push(""); // Preserve empty lines
      }
    });

    return allLines;
  }

  // Calculate dynamic item height based on display options
  private calculateItemHeight(config: any): number {
    let height = this.baseItemHeight;

    // Add height for each enabled display option
    const { display_options, pricing_enabled } = config;

    if (display_options.show_case_size) height += 4;
    if (display_options.show_upc) height += 4;
    if (display_options.show_stock) height += 4;
    if (pricing_enabled) height += 6;

    return height;
  }

  private async loadImageAsDataURL(imageUrl: string): Promise<string | null> {
    try {
      if (!imageUrl) return null;

      // Fetch the image
      const response = await fetch(imageUrl);
      if (!response.ok) return null;

      const arrayBuffer = await response.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      const base64 = buffer.toString("base64");

      // Determine image type from URL or content
      const contentType = response.headers.get("content-type") || "image/jpeg";
      return `data:${contentType};base64,${base64}`;
    } catch (error) {
      console.error("Error loading image:", error);
      return null;
    }
  }

  // Helper method to draw rounded rectangle
  private drawRoundedRect(
    x: number,
    y: number,
    width: number,
    height: number,
    radius: number
  ): void {
    // Check if roundedRect method exists (newer versions of jsPDF)
    if (typeof (this.doc as any).roundedRect === "function") {
      (this.doc as any).roundedRect(x, y, width, height, radius, radius, "S");
    } else {
      // Fallback to regular rectangle
      this.doc.rect(x, y, width, height, "S");
    }
  }

  public async generateCatalog(data: CatalogData): Promise<Buffer> {
    try {
      this.addHeader(data.config);
      await this.addItems(data.items, data.config);
      this.addFooter();

      // Use latest jsPDF output method - arrayBuffer is preferred over arraybuffer
      const pdfOutput = this.doc.output("arraybuffer");
      return Buffer.from(pdfOutput);
    } catch (error) {
      console.error("Error generating catalog PDF:", error);
      throw new Error(
        `PDF generation failed: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  private addHeader(config: any): void {
    try {
      // Title - using proper font setting methods
      this.doc.setFontSize(24);
      this.doc.setFont("Lato", "bold");
      this.doc.setTextColor(0, 0, 0); // Black color for title

      const title = this.sanitizeText(config.title) || "Product Catalog";
      this.doc.text(title, this.pageWidth / 2, this.currentY, {
        align: "center",
      });
      this.currentY += 7;

      // Subtitle - always check for subtitle even if empty string
      if (config.subtitle !== undefined && config.subtitle !== null) {
        const subtitle = this.sanitizeText(config.subtitle);
        // Show subtitle even if it's empty string, just not if it's null/undefined
        this.doc.setFontSize(14);
        this.doc.setFont("Lato", "normal");
        this.doc.setTextColor(0, 0, 0); // Black color for subtitle
        this.doc.text(subtitle, this.pageWidth / 2, this.currentY, {
          align: "center",
        });
        this.currentY += 8;
      }

      // Company Info - handle multiline text properly
      this.doc.setFontSize(8);
      this.doc.setTextColor(134, 142, 150); // #868e96
      const companyInfo = config.company_info || "Company Information";
      const companyLines = this.splitMultilineText(
        companyInfo,
        this.pageWidth - this.margin * 2
      );

      // Render each line separately for better control
      companyLines.forEach((line, index) => {
        if (line.trim()) {
          this.doc.text(line, this.pageWidth / 2, this.currentY + index * 4, {
            align: "center",
          });
        }
      });
      this.currentY += companyLines.length * 4 + 5; // Reduced spacing after company info from 10 to 5
    } catch (error) {
      console.error("Error adding header:", error);
      // Continue with a basic header if there's an error
      this.currentY += 30;
    }
  }

  private async addItems(items: Item[], config: any): Promise<void> {
    // Filter items by category if specified, otherwise use all items
    const filteredItems = config.category
      ? items.filter((item) => item.nacs_category === config.category)
      : items;

    // Sort all items according to sort configuration
    const sortedItems = this.sortItems(filteredItems, config.sort_by);

    // Render items in continuous grid without category segregation
    await this.renderItemsGrid(sortedItems, config);
  }

  private sortItems(items: Item[], sortBy: CatalogTemplateSort): Item[] {
    switch (sortBy) {
      case "PRODUCT_NAME":
        return items.sort((a, b) => a.name.localeCompare(b.name));
      case "SKU":
        return items.sort((a, b) =>
          (a.supplier_code || "").localeCompare(b.supplier_code || "")
        );
      case "CATEGORY":
        return items.sort((a, b) =>
          (a.nacs_category || "").localeCompare(b.nacs_category || "")
        );
      default:
        return items;
    }
  }

  private async renderItemsGrid(items: Item[], config: any): Promise<void> {
    let itemIndex = 0;
    const itemHeight = this.calculateItemHeight(config);

    while (itemIndex < items.length) {
      this.checkPageBreak(itemHeight + 10);

      const startX = this.margin;
      const startY = this.currentY;

      // Render up to itemsPerRow items in this row
      const promises = [];
      for (
        let col = 0;
        col < this.itemsPerRow && itemIndex < items.length;
        col++
      ) {
        const item = items[itemIndex];
        const x = startX + col * (this.itemWidth + 5); // 5mm gap between items (reduced from 10mm for 4 cards)
        promises.push(this.renderItemCard(item, x, startY, config, itemHeight));
        itemIndex++;
      }

      // Wait for all cards in this row to complete
      await Promise.all(promises);

      this.currentY += itemHeight + 5;
    }
  }

  private async renderItemCard(
    item: Item,
    x: number,
    y: number,
    config: any,
    itemHeight: number
  ): Promise<void> {
    try {
      // Card border with proper color setting and rounded corners
      this.doc.setLineWidth(0.3);
      this.doc.setDrawColor(236, 240, 241); // #ecf0f1
      this.drawRoundedRect(x, y, this.itemWidth, itemHeight, 2); // 2mm radius for subtle rounding

      let contentY = y + 2;

      // Product image - smaller size, centered
      const imageWidth = this.itemWidth - 20; // Reduced from 12 to 20 (8px smaller)
      const imageHeight = Math.max(24, itemHeight * 0.25); // Reduced from 30 to 24 and 0.3 to 0.25
      const imageX = x + (this.itemWidth - imageWidth) / 2; // Center horizontally

      // Load actual image
      let imageLoaded = false;
      if (item.img_sm) {
        try {
          const imageDataURL = await this.loadImageAsDataURL(item.img_sm);
          if (imageDataURL) {
            this.doc.addImage(
              imageDataURL,
              "PNG",
              imageX,
              contentY,
              imageWidth,
              imageHeight,
              undefined,
              "MEDIUM"
            );
            imageLoaded = true;
          }
        } catch (error) {
          console.error("Failed to load product image:", error);
        }
      }

      // Fallback placeholder if image didn't load
      if (!imageLoaded) {
        this.doc.setFillColor(248, 249, 250); // #f8f9fa
        this.doc.rect(imageX, contentY, imageWidth, imageHeight, "F");

        // Image placeholder border for better visibility
        this.doc.setLineWidth(0.2);
        this.doc.setDrawColor(220, 220, 220);
        this.doc.rect(imageX, contentY, imageWidth, imageHeight);

        // Better image placeholder text
        this.doc.setFontSize(7);
        this.doc.setTextColor(149, 165, 166); // #95a5a6
        this.doc.setFont("Lato", "normal");
        this.doc.text(
          "NO IMAGE",
          x + this.itemWidth / 2,
          contentY + imageHeight / 2 + 1,
          {
            align: "center",
          }
        );
      }

      contentY += imageHeight + 6; // Added 4px more margin below image (2 + 4 = 6)

      // Product name - improved text handling with tighter spacing
      this.doc.setFontSize(7);
      this.doc.setFont("Lato", "bold");
      this.doc.setTextColor(44, 62, 80); // #2c3e50
      const itemName = this.sanitizeText(item.name);
      const nameLines = this.doc.splitTextToSize(itemName, this.itemWidth - 6);
      const linesToShow = Array.isArray(nameLines)
        ? nameLines.slice(0, 2)
        : [nameLines];
      this.doc.text(linesToShow, x + 3, contentY); // Limit to 2 lines
      contentY += Math.min(linesToShow.length, 2) * 3 + 2;

      // Product details - only show enabled options with tighter spacing
      this.doc.setFontSize(7); // Increased by 2px (5 + 2 = 7)
      this.doc.setFont("Lato", "normal");
      this.doc.setTextColor(127, 140, 141); // #7f8c8d

      if (
        config.pricing_enabled &&
        item.price !== null &&
        item.price !== undefined
      ) {
        contentY += 1; // Reduced spacing before price
        const finalPrice = item.discounted_price || item.price;
        const hasDiscount =
          item.discounted_price && item.discounted_price < item.price;

        this.doc.setFontSize(10);
        this.doc.setFont("Lato", "bold");

        if (hasDiscount) {
          // Discounted price in green
          this.doc.setTextColor(0, 100, 0); // #006400
          this.doc.text(`$${Number(finalPrice).toFixed(2)}`, x + 3, contentY);

          // Original price crossed out - improved positioning
          this.doc.setFontSize(7);
          this.doc.setTextColor(52, 152, 219); // #3498db
          const originalPriceText = `$${Number(item.price).toFixed(2)}`;

          // Reset font size to get accurate width measurement for discounted price
          this.doc.setFontSize(10);
          const discountedPriceWidth = this.doc.getTextWidth(
            `$${Number(finalPrice).toFixed(2)}`
          );

          // Set font back to smaller size for original price
          this.doc.setFontSize(6);
          this.doc.text(
            originalPriceText,
            x + 3 + discountedPriceWidth + 2, // Added more spacing (4mm instead of 2mm)
            contentY
          );

          // Add line through original price
          this.doc.setLineWidth(0.3);
          this.doc.setDrawColor(0, 0, 0);
          const originalPriceWidth = this.doc.getTextWidth(originalPriceText);
          this.doc.line(
            x + 3 + discountedPriceWidth + 2,
            contentY - 0.8,
            x + 3 + discountedPriceWidth + 2 + originalPriceWidth,
            contentY - 0.8
          );
        } else {
          // Regular price
          this.doc.setTextColor(52, 152, 219); // #3498db
          this.doc.text(`$${Number(finalPrice).toFixed(2)}`, x + 3, contentY);
        }
        contentY += 5;
      }

      this.doc.setFontSize(8);
      this.doc.setFont("Lato", "normal");
      this.doc.setTextColor(127, 140, 141);

      if (config.display_options.show_case_size && item.unit_size) {
        const unitSize = this.sanitizeText(item.unit_size);
        this.doc.text(`Case: ${unitSize}`, x + 3, contentY);
        contentY += 4;
      }

      if (config.display_options.show_upc && item.upc1) {
        const upc = this.sanitizeText(item.upc1);
        this.doc.text(`UPC: ${upc}`, x + 3, contentY);
        contentY += 4;
      }

      if (config.display_options.show_stock) {
        const stockText = `Stock: ${item.qoh || 0}`;
        this.doc.text(stockText, x + 3, contentY);
      }
    } catch (error) {
      console.error("Error rendering item card:", error);
      // Continue with next item instead of failing completely
    }
  }

  private addFooter(): void {
    try {
      const footerY = this.pageHeight - 15;
      this.doc.setFontSize(8);
      this.doc.setFont("Lato", "normal");
      this.doc.setTextColor(149, 165, 166); // #95a5a6
      this.doc.text(
        `Generated on ${new Date().toLocaleDateString()}`,
        this.pageWidth / 2,
        footerY,
        { align: "center" }
      );
    } catch (error) {
      console.error("Error adding footer:", error);
    }
  }

  private checkPageBreak(requiredSpace: number): void {
    if (this.currentY + requiredSpace > this.pageHeight - 15) {
      // Reduced from 30 to 15
      this.doc.addPage();
      this.currentY = this.margin;
    }
  }
}

export async function generateCatalogPDF(data: CatalogData): Promise<Buffer> {
  try {
    const generator = new CatalogPDFGenerator();
    return await generator.generateCatalog(data);
  } catch (error) {
    console.error("Failed to generate catalog PDF:", error);
    throw new Error(
      `Catalog PDF generation failed: ${
        error instanceof Error ? error.message : "Unknown error"
      }`
    );
  }
}
