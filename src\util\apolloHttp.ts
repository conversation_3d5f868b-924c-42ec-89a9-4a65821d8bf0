import { ApolloServer, HeaderMap } from "@apollo/server";
import mime from "mime-types";

interface HTTPGraphQLRequest {
  method: string;
  headers: HeaderMap; // the `HeaderMap` class is exported by @apollo/server
  search: string;
  body: unknown;
}

export type ApolloHttpPost = (
  query: string,
  variables: Record<string, unknown>
) => Promise<unknown>;

export const apolloHttpPost = (apolloServer: ApolloServer) => {
  return async (query: string, variables: Record<string, unknown>) => {
    const headers = new HeaderMap([
      ["content-type", mime.types.json],
      ["accept", mime.types.json],
    ]);
    const httpGraphQLRequest: HTTPGraphQLRequest = {
      method: "POST",
      headers,
      body: {
        query,
        variables,
      },
      search: "",
    };
    const res = await apolloServer.executeHTTPGraphQLRequest({
      httpGraphQLRequest,
      context: () => null,
    });
    res.status = res.status || 200;
    const body = JSON.parse(
      (res.body as { kind: "complete"; string: string }).string
    );
    if (res.status == 200) {
      return body.data;
    }
    throw {
      message: `Error processing Apollo Server POST request (${body.errors[0].extensions.code}): ${body.errors[0].message}`,
      code: res.status,
    };
  };
};
