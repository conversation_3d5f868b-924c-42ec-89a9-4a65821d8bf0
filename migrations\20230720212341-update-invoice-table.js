"use strict";

var dbm;
var type;
var seed;

/**
 * We receive the dbmigrate dependency from dbmigrate initially.
 * This enables us to not have to rely on NODE_PATH.
 */
exports.setup = function (options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

var async = require("async");

exports.up = function (db, callback) {
  async.series(
    [
      db.addForeignKey.bind(
        db,
        "invoice",
        "order_detail",
        "fk_invoice_to_order_detail",
        { order_id: "id" },
        {}
      ),
      db.changeColumn.bind(db, "invoice", "order_id", { unique: true }),
    ],
    callback
  );
};

exports.down = function (db, callback) {
  async.series(
    [
      db.removeForeignKey.bind(db, "invoice", "fk_invoice_to_order_detail"),
      db.changeColumn.bind(db, "invoice", "order_id", { unique: false }),
    ],
    callback
  );
};

exports._meta = {
  version: 1,
};
