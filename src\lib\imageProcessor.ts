import sharp from "sharp";
import fs from "fs/promises";
import path from "path";
import { S3Client } from "@aws-sdk/client-s3";
import { Upload } from "@aws-sdk/lib-storage";
import { awsS3Config } from "../config/environment";

export interface ImageProcessingOptions {
  targetHeight?: number;
  format?: "webp" | "png" | "jpeg";
  quality?: number;
  compressionLevel?: number;
  effort?: number;
  nearLossless?: boolean;
}

export interface ProcessedImageResult {
  buffer: Buffer;
  metadata: {
    originalWidth: number;
    originalHeight: number;
    newWidth: number;
    newHeight: number;
    format: string;
    size: number;
  };
}

export interface S3UploadOptions {
  bucket?: string;
  keyPrefix: string;
  contentType?: string;
  acl?: string;
}

// Interface for multer file object
export interface MulterFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  size: number;
  buffer: Buffer;
}

export class ImageProcessor {
  private s3Client: S3Client;
  private defaultBucket: string;

  constructor(s3Client: S3Client) {
    this.s3Client = s3Client;
    this.defaultBucket = awsS3Config.bucket;
  }

  /**
   * Downloads an image from a URL with retry logic and proper headers
   */
  async downloadImageFromUrl(url: string): Promise<Buffer> {
    // Common browser headers to avoid 403/blocking
    const headers = {
      "User-Agent":
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
      Accept: "image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8",
      "Accept-Language": "en-US,en;q=0.9",
      "Accept-Encoding": "gzip, deflate, br",
      DNT: "1",
      Connection: "keep-alive",
      "Upgrade-Insecure-Requests": "1",
    };

    // Try with different referers if the first attempt fails
    const referers = [
      undefined, // No referer first
      "https://www.google.com/",
      "https://www.amazon.com/",
      "https://www.walmart.com/",
    ];

    for (let i = 0; i < referers.length; i++) {
      try {
        const requestHeaders = { ...headers };
        if (referers[i]) {
          requestHeaders["Referer"] = referers[i];
        }

        const response = await fetch(url, {
          headers: requestHeaders,
          redirect: "follow",
        });

        if (response.ok) {
          const arrayBuffer = await response.arrayBuffer();
          return Buffer.from(arrayBuffer);
        } else {
          if (i === referers.length - 1) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }
          continue;
        }
      } catch (error) {
        if (i === referers.length - 1) {
          throw new Error(
            `Failed to download image from ${url}: ${error.message}`
          );
        }
        // Wait a bit before retry
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }
    }

    throw new Error(
      `Failed to download image from ${url}: All attempts failed`
    );
  }

  /**
   * Processes an image buffer with the specified options
   */
  async processImage(
    imageBuffer: Buffer,
    options: ImageProcessingOptions = {}
  ): Promise<ProcessedImageResult> {
    const {
      targetHeight = 200,
      format = "png",
      quality = 100,
      compressionLevel = 9,
      effort = 6,
    } = options;

    const image = sharp(imageBuffer);
    const metadata = await image.metadata();

    const { width, height } = metadata;

    if (!width || !height) {
      throw new Error("Could not determine image dimensions");
    }

    let processedImage = image;
    let newWidth = width;
    let newHeight = height;

    // Calculate aspect ratio and determine if resizing is needed
    if (height > targetHeight) {
      const aspectRatio = width / height;
      newWidth = Math.round(targetHeight * aspectRatio);
      newHeight = targetHeight;

      processedImage = processedImage.resize(newWidth, newHeight, {
        fit: "inside",
        withoutEnlargement: true,
      });
    }

    const processedBuffer = await processedImage
      .png({
        compressionLevel,
        quality,
        effort,
        force: true,
      })
      .toBuffer();

    return {
      buffer: processedBuffer,
      metadata: {
        originalWidth: width,
        originalHeight: height,
        newWidth,
        newHeight,
        format,
        size: processedBuffer.length,
      },
    };
  }

  /**
   * Saves an image buffer to local storage
   */
  async saveImageLocally(
    imageBuffer: Buffer,
    fileName: string,
    outputDir: string
  ): Promise<string> {
    // Ensure output directory exists
    await fs.mkdir(outputDir, { recursive: true });

    const filePath = path.join(outputDir, fileName);
    await fs.writeFile(filePath, imageBuffer);

    return filePath;
  }

  /**
   * Uploads an image buffer to S3
   */
  async uploadImageToS3(
    imageBuffer: Buffer,
    fileName: string,
    options: S3UploadOptions
  ): Promise<string> {
    const {
      bucket = this.defaultBucket,
      keyPrefix,
      contentType = "image/png",
    } = options;

    const s3Key = `${keyPrefix}/${fileName}`;

    const uploadParams = {
      Bucket: bucket,
      Key: s3Key,
      Body: imageBuffer,
      ContentType: contentType,
    };

    try {
      const uploadResult = await new Upload({
        client: this.s3Client,
        params: uploadParams,
      }).done();

      // Construct the S3 URL manually since Location may not be available
      const s3Url = `https://${bucket}.s3.amazonaws.com/${s3Key}`;
      return s3Url;
    } catch (error) {
      throw new Error(`Failed to upload ${fileName} to S3: ${error.message}`);
    }
  }

  /**
   * Complete image processing pipeline: download, process, and optionally upload to S3
   */
  async processImageFromUrl(
    imageUrl: string,
    fileName: string,
    options: {
      processing?: ImageProcessingOptions;
      localSave?: {
        outputDir: string;
      };
      s3Upload?: S3UploadOptions;
    } = {}
  ): Promise<{
    metadata: ProcessedImageResult["metadata"];
    localPath?: string;
    s3Url?: string;
  }> {
    // Download image
    const imageBuffer = await this.downloadImageFromUrl(imageUrl);

    // Process image
    const processedResult = await this.processImage(
      imageBuffer,
      options.processing
    );

    const results: {
      metadata: ProcessedImageResult["metadata"];
      localPath?: string;
      s3Url?: string;
    } = {
      metadata: processedResult.metadata,
    };

    // Save locally if requested
    if (options.localSave) {
      results.localPath = await this.saveImageLocally(
        processedResult.buffer,
        fileName,
        options.localSave.outputDir
      );
    }

    // Upload to S3 if requested
    if (options.s3Upload) {
      results.s3Url = await this.uploadImageToS3(
        processedResult.buffer,
        fileName,
        options.s3Upload
      );
    }

    return results;
  }

  /**
   * Process an uploaded file (req.file) to create thumbnails
   */
  async processFileUpload(
    file: MulterFile,
    fileName: string,
    options: {
      processing?: ImageProcessingOptions;
      s3Upload?: S3UploadOptions;
    } = {}
  ): Promise<{
    metadata: ProcessedImageResult["metadata"];
    localPath?: string;
    s3Url?: string;
  }> {
    // Validate that the file is an image
    if (!file.mimetype.startsWith("image/")) {
      throw new Error(`File must be an image, received: ${file.mimetype}`);
    }


    // Process the uploaded file buffer
    const processedResult = await this.processImage(
      file.buffer,
      options.processing
    );

    const results: {
      metadata: ProcessedImageResult["metadata"];
      localPath?: string;
      s3Url?: string;
    } = {
      metadata: processedResult.metadata,
    };

    // Upload to S3 if requested
    if (options.s3Upload) {
      results.s3Url = await this.uploadImageToS3(
        processedResult.buffer,
        fileName,
        options.s3Upload
      );
    }

    return results;
  }
}
