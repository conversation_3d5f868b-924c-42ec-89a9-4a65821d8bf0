"use strict";

var dbm;
var type;
var seed;

/**
 * We receive the dbmigrate dependency from dbmigrate initially.
 * This enables us to not have to rely on NODE_PATH.
 */
exports.setup = function (options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

var async = require("async");

exports.up = function (db, callback) {
  async.series(
    [
      // Add item_uom_id to cart_item table
      db.addColumn.bind(db, "cart_item", "item_uom_id", {
        type: "int",
        foreignKey: {
          name: "fk_cart_item_to_item_uom",
          table: "item_uom",
          mapping: "id",
          rules: {
            onDelete: "SET NULL",
            onUpdate: "RESTRICT",
          },
        },
      }),

      // Add item_uom_id to order_item table
      db.addColumn.bind(db, "order_item", "item_uom_id", {
        type: "int",
        foreignKey: {
          name: "fk_order_item_to_item_uom",
          table: "item_uom",
          mapping: "id",
          rules: {
            onDelete: "SET NULL",
            onUpdate: "RESTRICT",
          },
        },
      }),

      // Add item_uom_id to invoice_item table
      db.addColumn.bind(db, "invoice_item", "item_uom_id", {
        type: "int",
        foreignKey: {
          name: "fk_invoice_item_to_item_uom",
          table: "item_uom",
          mapping: "id",
          rules: {
            onDelete: "SET NULL",
            onUpdate: "RESTRICT",
          },
        },
      }),
    ],
    callback
  );
};

exports.down = function (db, callback) {
  async.series(
    [
      // Remove item_uom_id from cart_item table
      db.removeColumn.bind(db, "cart_item", "item_uom_id"),

      // Remove item_uom_id from order_item table
      db.removeColumn.bind(db, "order_item", "item_uom_id"),

      // Remove item_uom_id from invoice_item table
      db.removeColumn.bind(db, "invoice_item", "item_uom_id"),
    ],
    callback
  );
};

exports._meta = {
  version: 1,
};
