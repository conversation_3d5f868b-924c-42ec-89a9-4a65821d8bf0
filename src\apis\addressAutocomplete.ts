import { Request, Response } from "express";
import RestEndpoint from "./_restEndpoint";

interface ResponseData {
  predictions?: {text: string, placeID: string}[];
  fullAddress?: string;
}

export default class AddressAutocomplete extends RestEndpoint {
  public async handler(req: Request, res: Response) {
    try {
      const result = await this.worker(req.query);
      res.send(result);
    } catch (err) {
      res
        .status(err.code || 500)
        .send(`Error completing address: ${err.message || err.toString()}`);
    }
  }

  protected async worker(payload) {

    const suggest = payload["suggest"];
    const fullAddress = payload["fullAddress"]
    let responseData : ResponseData = {};

    if(suggest){
      const suggestionResponse = await fetch(`https://places.googleapis.com/v1/places:autocomplete`, {
          method: "POST",
          body: JSON.stringify({ "input": suggest }),
          headers: {
          "Content-Type": "application/json",
          "X-Goog-FieldMask": "suggestions.placePrediction.text,suggestions.placePrediction.placeId",
          "X-Goog-Api-Key": process.env.GOOG_API_KEY,
          },
      });

      if (suggestionResponse.ok) {
        const data = await suggestionResponse.json();
        const predictions = data.suggestions.map((prediction : any) => {
          return {
            text: prediction.placePrediction.text.text,
            placeID: prediction.placePrediction.placeId
          }}) || [];
        responseData.predictions = predictions;
      } else {
          console.error("Failed to fetch suggestions:", suggestionResponse.status);
      }
    }

    if(fullAddress){
      const fullAddressResponse = await fetch('https://places.googleapis.com/v1/places/' + fullAddress, {
        method: 'GET',
        headers: {
          "X-Goog-FieldMask": "formattedAddress",
          "X-Goog-Api-Key": process.env.GOOG_API_KEY
        }
      });
      
      if (fullAddressResponse.ok) {
        const data = await fullAddressResponse.json();
        responseData.fullAddress = data?.formattedAddress;
      } else {
          console.error("Failed to fetch suggestions:", fullAddressResponse.status);
      }
    } 

    return responseData;
  }
}
