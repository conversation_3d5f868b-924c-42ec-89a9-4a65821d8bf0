"use strict";

var dbm;
var type;
var seed;

/**
 * We receive the dbmigrate dependency from dbmigrate initially.
 * This enables us to not have to rely on NODE_PATH.
 */
exports.setup = function (options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

/**
 * Migration: Populate supplier_config table with hardcoded supplier behaviors
 *
 * This migration replaces hardcoded supplier ID checks throughout the codebase
 * with flexible feature flags stored in the supplier_config table.
 */
exports.up = function (db) {
  console.log("🚀 Starting supplier config population migration...");

  // Helper function to upsert config values
  const upsertConfig = async (supplierId, key, value) => {
    const stringValue =
      typeof value === "string" ? value : JSON.stringify(value);

    const existing = await db.runSql(
      "SELECT * FROM supplier_config WHERE supplier_id = ? AND key = ?",
      [supplierId, key]
    );

    if (existing.rows && existing.rows.length > 0) {
      await db.runSql(
        "UPDATE supplier_config SET value = ?, updated_at = NOW() WHERE supplier_id = ? AND key = ?",
        [stringValue, supplierId, key]
      );
    } else {
      await db.runSql(
        "INSERT INTO supplier_config (supplier_id, key, value, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())",
        [supplierId, key, stringValue]
      );
    }
  };

  return Promise.resolve()
    .then(() => {
      console.log("  📦 Configuring Supplier 30 (A&I Beverage)...");
      return upsertConfig("30", "spotlight_ids", [1, 6, 7]);
    })
    .then(() => {
      console.log("  📦 Configuring Supplier 31 (C&G Snacks)...");
      return Promise.all([
        upsertConfig("31", "is_dsd", true),
        upsertConfig("31", "send_order_notifications", false),
        upsertConfig("31", "invoice_scale_width", 0.7),
        upsertConfig("31", "allow_image_upload", true),
        upsertConfig("31", "show_order_tabs", true),
        upsertConfig("31", "spotlight_ids", [1, 6, 7]),
        upsertConfig("31", "auto_set_delivery_date", true),
        upsertConfig("31", "use_alltime_avg_calculation", true),
        upsertConfig("31", "exclude_canceled_orders", true),
        upsertConfig("31", "skip_qb_sync", true),
      ]);
    })
    .then(() => {
      console.log("  📦 Configuring Spotlight ids");
      return Promise.all([
        upsertConfig("45", "spotlight_ids", [1, 6, 7]),
        upsertConfig("46", "spotlight_ids", [1, 6, 7]),
        upsertConfig("47", "spotlight_ids", [1, 6, 7]),
        upsertConfig("53", "spotlight_ids", [8, 1, 2, 3, 4, 5]),
        upsertConfig("57", "spotlight_ids", [1, 2, 3, 4, 5]),
        upsertConfig("60", "spotlight_ids", [6, 7, 8]),
      ]);
    })
    .then(() => {
      console.log("  📦 Configuring Supplier 68 (Whitestone Foods)...");
      return Promise.all([
        upsertConfig("68", "is_dsd", true),
        upsertConfig("68", "send_order_notifications", false),
        upsertConfig("68", "requires_delivery_date", true),
        upsertConfig("68", "qb_credit_memo_date_filter", "2024-09-01"),
        upsertConfig("68", "allow_image_upload", true),
        upsertConfig("68", "pavilions_display_name", "Comfy House Foods"),
        upsertConfig("68", "sort_invoice_items", true),
        upsertConfig(
          "68",
          "pavilions_address",
          "8726 S Sepulveda Blvd, Los Angeles, CA 90045"
        ),
        upsertConfig("68", "show_order_tabs", true),
      ]);
    })
    .then(() => {
      console.log("  📦 Configuring Supplier 69 (Planet Express)...");
      return upsertConfig("69", "is_dsd", true);
    })
    .then(() => {
      console.log("  📦 Adding frontend-specific configurations...");
      // Default dashboard duration config
      // Used for: duration state initialization in homepage/dashboard
      // Logic: suppliers 68 and 31 default to 'all', others default to '7'
      return Promise.all([
        upsertConfig("31", "default_dashboard_duration", "all"),
        upsertConfig("68", "default_dashboard_duration", "all"),
      ]);
    })
    .then(() => {
      console.log("  📦 Configuring order display preferences...");
      // Used for: conditionally showing profit calculation in OrderDetails
      // Used for: OrderDetails.tsx - determines UI layout (route vs driver)
      return Promise.all([
        upsertConfig("31", "show_profit_info", true),
        upsertConfig("68", "use_custom_driver_field", true),
      ]);
    })
    .then(() => {
      console.log("✅ Migration completed successfully!");
      console.log("📝 Summary of configurations created:");
      console.log("   - Supplier 30: 1 config (spotlight_ids)");
      console.log(
        "   - Supplier 31: 6 configs (dsd, notifications, scale, upload, tabs, spotlight)"
      );
      console.log("   - Supplier 45: 1 config (spotlight_ids)");
      console.log("   - Supplier 46: 1 config (spotlight_ids)");
      console.log("   - Supplier 47: 1 config (spotlight_ids)");
      console.log("   - Supplier 53: 1 config (spotlight_ids)");
      console.log("   - Supplier 57: 1 config (spotlight_ids)");
      console.log("   - Supplier 60: 1 config (spotlight_ids)");
      console.log(
        "   - Supplier 68: 8 configs (dsd, notifications, delivery, qb, upload, pavilions, tabs)"
      );
      console.log("   - Supplier 69: 1 config (is_dsd)");
      console.log(`   - Total: 21 configuration entries`);
    });
};

exports.down = function (db) {
  console.log("🔄 Rolling back supplier config migration...");

  // List of suppliers and their config keys that were added
  const supplierConfigs = {
    "30": ["spotlight_ids"],
    "31": [
      "is_dsd",
      "send_order_notifications",
      "invoice_scale_width",
      "allow_image_upload",
      "show_order_tabs",
      "spotlight_ids",
      "auto_set_delivery_date",
      "use_alltime_avg_calculation",
      "exclude_canceled_orders",
      "skip_qb_sync",
      "default_dashboard_duration",
      "show_profit_info",
    ],
    "45": ["spotlight_ids"],
    "46": ["spotlight_ids"],
    "47": ["spotlight_ids"],
    "53": ["spotlight_ids"],
    "57": ["spotlight_ids"],
    "60": ["spotlight_ids"],
    "68": [
      "is_dsd",
      "send_order_notifications",
      "requires_delivery_date",
      "qb_credit_memo_date_filter",
      "allow_image_upload",
      "pavilions_display_name",
      "pavilions_address",
      "show_order_tabs",
      "default_dashboard_duration",
      "use_custom_driver_field",
    ],
    "69": ["is_dsd"],
  };

  let promise = Promise.resolve();

  // Remove all config entries created by this migration
  Object.entries(supplierConfigs).forEach(([supplierId, configKeys]) => {
    promise = promise.then(() => {
      console.log(`  🗑️  Removing configs for supplier ${supplierId}...`);
      const placeholders = configKeys.map(() => "?").join(",");
      return db.runSql(
        `DELETE FROM supplier_config WHERE supplier_id = ? AND key IN (${placeholders})`,
        [supplierId, ...configKeys]
      );
    });
  });

  return promise.then(() => {
    console.log("✅ Migration rollback completed!");
    console.log(
      "   - Removed all supplier config entries created by this migration"
    );
  });
};

exports._meta = {
  version: 1,
}; 