"use strict";

let dbm, type, seed;
exports.setup = function (options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

exports.up = function (db) {
  return db
    .createTable("catalog_template", {
      id: { type: "serial", primaryKey: true, autoIncrement: true },
      supplier_id: {
        type: "int",
        notNull: true,
        foreignKey: {
          name: "fk_catalog_supplier",
          table: "supplier",
          mapping: "id",
          rules: { onDelete: "NO ACTION", onUpdate: "CASCADE" },
        },
      },
      name: { type: "string", notNull: true },
      config: { type: "jsonb", notNull: true },
      pdf_path: { type: "string" },
      created_by: { type: "int" },
      created_at: {
        type: "timestamp",
        notNull: true,
        defaultValue: new String("CURRENT_TIMESTAMP"),
      },
      updated_at: {
        type: "timestamp",
        notNull: true,
        defaultValue: new String("CURRENT_TIMESTAMP"),
      },
      archived_at: { type: "timestamp" },
    })
    .then(() =>
      db.addIndex("catalog_template", "idx_catalog_supplier_created_at", [
        "supplier_id",
        "created_at",
      ])
    )
    .then(() =>
      db.addColumn("item", "img_sm", {
        type: "string",
      })
    )
    .then(() => db.addColumn("item", "img_md", { type: "string" }));
};

exports.down = function (db) {
  return db
    .removeIndex("catalog_template", "idx_catalog_supplier_created_at")
    .then(() => db.dropTable("catalog_template"));
};

exports._meta = { version: 1 };
