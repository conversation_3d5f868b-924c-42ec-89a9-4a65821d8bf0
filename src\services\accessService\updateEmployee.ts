import knex from "../../../knex/knex";
import {
  createFirebaseAccount,
  deleteFirebaseAccount,
  createAuth0Account,
  deleteAuth0Account,
} from "./authController";

interface UpdateEmployeeParams {
  supplierId: string;
  id: string;
  name?: string;
  phone?: string;
  email?: string;
  password?: string;
  appAccess?: boolean;
  dashboardAccess?: boolean;
  roleIds?: string[];
  routeIds?: string[];
  archived?: boolean;
  lastLogin?: Date;
}

/**
 * Updates an employee record in the database and their authentication accounts
 * @param params - Updated employee details
 * @returns The updated employee record
 */
export const updateEmployee = async (params: UpdateEmployeeParams) => {
  const {
    supplierId,
    id,
    name,
    phone,
    email,
    password,
    appAccess,
    dashboardAccess,
    roleIds,
    routeIds,
    lastLogin,
    archived,
  } = params;

  if (!supplierId || !id) {
    throw new Error("Supplier ID and Employee ID are required");
  }

  try {
    // 1. Get the current employee record
    const currentEmployee = await knex("employees")
      .where({ id, supplier_id: supplierId })
      .first();

    if (!currentEmployee) {
      throw new Error(`Employee with ID ${id} not found`);
    }

    // Prepare update data
    const updateData: Record<string, any> = {
      updated_at: new Date(),
    };

    if (name !== undefined) updateData.name = name;
    if (phone !== undefined) updateData.phone = phone;
    if (email !== undefined) updateData.email = email;
    if (appAccess !== undefined) updateData.app_access = appAccess;
    if (dashboardAccess !== undefined)
      updateData.dashboard_access = dashboardAccess;
    if (archived !== undefined) updateData.archived = archived;

    // 2. Update the employee record in the database
    const [updatedEmployee] = await knex("employees")
      .where({ id })
      .update(updateData)
      .returning("*");

    // Check if employee is being archived
    if (archived === true && !currentEmployee.archived) {
      console.log(
        `Employee ${id} is being archived, deleting authentication accounts`
      );

      // Delete Firebase account if they had app access
      if (currentEmployee.app_access) {
        try {
          await deleteFirebaseAccount({
            employeeId: id,
            supplierId,
          });
          console.log(`Firebase account deleted for archived employee ${id}`);
        } catch (error) {
          console.error(
            `Failed to delete Firebase account for archived employee ${id}:`,
            error
          );
        }
      }

      // Delete Auth0 account if they had dashboard access
      if (currentEmployee.dashboard_access) {
        try {
          await deleteAuth0Account({
            employeeId: id,
            supplierId,
          });
          console.log(`Auth0 account deleted for archived employee ${id}`);
        } catch (error) {
          console.error(
            `Failed to delete Auth0 account for archived employee ${id}:`,
            error
          );
        }
      }

      // When archiving, we don't need to process further account changes
      // since accounts are already deleted
    } else {
      // 3. Handle Firebase account for app access
      if (appAccess !== undefined) {
        // If app access was granted but wasn't before, create account
        if (appAccess && !currentEmployee.app_access) {
          if (!password) {
            console.warn(
              "Password required to create Firebase account, skipping creation"
            );
          } else {
            await createFirebaseAccount({
              email: email || currentEmployee.email,
              password,
              employeeId: id,
              supplierId,
            });
          }
        }
        // If app access was removed but was enabled before, delete account
        else if (!appAccess && currentEmployee.app_access) {
          await deleteFirebaseAccount({
            employeeId: id,
            supplierId,
          });
        }
      }

      // 4. Handle Auth0 account for dashboard access
      if (dashboardAccess !== undefined) {
        // If dashboard access was granted but wasn't before, create account
        if (dashboardAccess && !currentEmployee.dashboard_access) {
          if (!password) {
            console.warn(
              "Password required to create Auth0 account, skipping creation"
            );
          } else {
            await createAuth0Account({
              email: email || currentEmployee.email,
              password,
              name: name || currentEmployee.name,
              employeeId: id,
              supplierId,
            });
          }
        }
        // If dashboard access was removed but was enabled before, delete account
        else if (!dashboardAccess && currentEmployee.dashboard_access) {
          await deleteAuth0Account({
            employeeId: id,
            supplierId,
          });
        }
      }
    }

    // 5. Update role assignments if needed
    if (roleIds && roleIds.length >= 0) {
      // Remove existing role assignments
      await knex("role_assignment").where({ employee_id: id }).delete();

      // Add new role assignments
      if (roleIds.length > 0) {
        const roleAssignments = roleIds.map((roleId) => ({
          employee_id: id,
          role_id: roleId,
          assigned_at: new Date(),
        }));

        await knex("role_assignment").insert(roleAssignments);
      }
    }

    // 6. Update route assignments if needed
    if (routeIds && routeIds.length >= 0) {
      // Remove existing route assignments
      await knex("route_assignment").where({ employee_id: id }).delete();

      // Add new route assignments
      if (routeIds.length > 0) {
        const routeAssignments = routeIds.map((routeId) => ({
          employee_id: id,
          route_id: routeId,
          created_at: new Date(),
          updated_at: new Date(),
        }));

        await knex("route_assignment").insert(routeAssignments);
      }
    }

    // 7. Update last login if provided
    if (lastLogin) {
      await knex("employees").where({ id }).update({ last_login: lastLogin });
    }

    // Fetch the employee with roles to return
    const employeeWithRoles = await knex
      .select(
        "employees.*",
        knex.raw(
          `COALESCE(
            json_agg(
              DISTINCT jsonb_build_object(
                'id', roles.id,
                'name', roles.name,
                'description', roles.description
              )
            ) FILTER (WHERE roles.id IS NOT NULL),
            '[]'
          ) as roles`
        ),
        knex.raw(
          `COALESCE(
            json_agg(
              DISTINCT jsonb_build_object(
                'id', route.id,
                'name', route.name
              )
            ) FILTER (WHERE route.id IS NOT NULL),
            '[]'
          ) as routes`
        )
      )
      .from("employees")
      .leftJoin(
        "role_assignment",
        "employees.id",
        "role_assignment.employee_id"
      )
      .leftJoin("roles", "role_assignment.role_id", "roles.id")
      .leftJoin(
        "route_assignment",
        "employees.id",
        "route_assignment.employee_id"
      )
      .leftJoin("route", "route_assignment.route_id", "route.id")
      .where("employees.id", id)
      .groupBy("employees.id")
      .first();

    console.log("Employee updated:", employeeWithRoles);
    return employeeWithRoles;
  } catch (error) {
    console.error("Error in updateEmployee:", error);
    throw error;
  }
};

export default updateEmployee;
