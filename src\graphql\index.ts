import * as queries from "./queries";
import * as mutations from "./mutations";
import { Resolvers } from "../generated/graphql";
import GraphQLJSON, { GraphQLJSONObject } from "graphql-type-json";
import { GraphQLDate, GraphQLTime, GraphQLDateTime } from "graphql-iso-date";
const resolvers: Resolvers = {
  Query: {
    ...queries,
  },
  Mutation: {
    ...mutations,
  },
  JSON: GraphQLJSON,
  JSONObject: GraphQLJSONObject,
  Date: GraphQLDate,
  Time: GraphQLTime,
  DateTime: GraphQLDateTime,
};

export default resolvers;
