"use strict";

var dbm;
var type;
var seed;

/**
 * We receive the dbmigrate dependency from dbmigrate initially.
 * This enables us to not have to rely on NODE_PATH.
 */
exports.setup = function (options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

exports.up = function (db) {
  return db
    .addColumn("attain_user", "start_time", {
      type: "string",
      length: 255,
    })
    .then(() => {
      return db.addColumn("attain_user", "end_time", {
        type: "string",
        length: 255,
      });
    })
    .then(() => {
      return db.createTable("delivery_window", {
        id: {
          type: "serial",
          primaryKey: true,
          autoIncrement: true,
          notNull: true,
        },
        day: {
          type: "string",
          length: 255,
          notNull: true,
        },
        store_id: {
          type: "int",
          notNull: true,
          foreignKey: {
            name: "fk_delivery_window_to_attain_user",
            table: "attain_user",
            mapping: "id",
            rules: {
              onDelete: "CASCADE",
              onUpdate: "CASCADE",
            },
          },
        },
      });
    });
};

exports.down = function (db) {
  return db
    .dropTable("delivery_window")
    .then(() => {
      return db.removeColumn("attain_user", "end_time");
    })
    .then(() => {
      return db.removeColumn("attain_user", "start_time");
    });
};

exports._meta = {
  version: 1,
};
