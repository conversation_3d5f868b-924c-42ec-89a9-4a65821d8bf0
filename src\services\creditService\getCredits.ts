import knex from "../../../knex/knex";
import {
  Credit,
  CreditItem,
  GetCreditsInput,
  Item,
} from "../../generated/graphql";
import { Knex } from "knex";

import { CreditItemRow, CreditRow, CreditImageRow } from "./db.types";
import { getUser } from "../userService/index";
import { getInvoice } from "../invoiceService/getInvoice";

async function getCredits(
  getCreditsInput: GetCreditsInput,
  trx?: Knex.Transaction
): Promise<{ credits: Credit[]; totalCount: number }> {
  const {
    supplier_id,
    filters,
    pagination = { offset: 0, limit: 20 },
    sortBy = { field: "created_at", ordering: "DESC" },
  } = getCreditsInput;

  const {
    ids,
    invoice_ids,
    user_ids,
    statuses,
    createdAtRange,
    archived = false,
  } = filters || {};

  // Use the transaction if provided, otherwise use the default knex instance
  const queryBuilder = trx || knex;

  let query = queryBuilder<CreditRow>("credit");

  // Apply filters
  if (ids && ids.length > 0) {
    query = query.whereIn("credit.id", ids);
  }

  if (invoice_ids && invoice_ids.length > 0) {
    query = query.whereIn("credit.invoice_id", invoice_ids);
  }

  if (user_ids && user_ids.length > 0) {
    query = query.whereIn("credit.user_id", user_ids);
  }

  if (supplier_id) {
    query = query.where("credit.supplier_id", supplier_id);
  }

  if (statuses && statuses.length > 0) {
    query = query.whereIn("credit.status", statuses);
  }

  // Apply archived filter
  query = query.where("credit.archived", archived);

  // Apply date range filter
  if (createdAtRange && createdAtRange.length === 2) {
    const [startDate, endDate] = createdAtRange;
    query = query
      .where("credit.created_at", ">=", startDate)
      .where("credit.created_at", "<=", endDate);
  }

  // Get total count before applying pagination
  const countResult = await query
    .clone()
    .clearSelect() // Clear the previous selects
    .count({ count: "credit.id" })
    .first();
  const totalCount = countResult ? Number(countResult.count) : 0;

  // Apply sorting
  const { field, ordering } = sortBy;
  // Handle special case for fields that might be ambiguous
  const orderField = field.includes(".") ? field : `credit.${field}`;
  query = query.orderBy(orderField, ordering);

  // Apply pagination
  const { offset = 0, limit = 20 } = pagination;
  query = query.offset(offset).limit(limit);

  // Execute query
  const credits = (await query) as CreditRow[];

  // Get credit items for all fetched credits
  const creditIds = credits.map((credit) => credit.id);

  if (creditIds.length === 0) {
    return {
      credits: [],
      totalCount: 0,
    };
  }

  const allCreditItems = (await queryBuilder("credit_item")
    .whereIn("credit_id", creditIds)
    .select("*")) as CreditItemRow[];

  // Get credit images for all fetched credits
  const allCreditImages = (await queryBuilder("credit_image")
    .whereIn("credit_id", creditIds)
    .select("*")
    .orderBy("created_at", "asc")) as CreditImageRow[];

  // Populate credits with items and images
  const populatedCredits = await Promise.all(
    credits.map(async (credit) => {
      const creditItems: CreditItem[] = allCreditItems.filter(
        (item) => item.credit_id === credit.id
      );

      const images: string[] = allCreditImages
        .filter((image) => image.credit_id === credit.id)
        .map((image) => image.url);

      const customerDetails = await getUser(credit.user_id.toString());
      const invoiceDetails = await getInvoice(credit.invoice_id.toString());

      return {
        ...credit,
        creditItems,
        customerDetails,
        invoiceDetails,
        images,
      };
    })
  );

  return {
    credits: populatedCredits,
    totalCount,
  };
}

export default getCredits;
