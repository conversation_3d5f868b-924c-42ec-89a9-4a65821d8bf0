import knex from "../../../knex/knex";

/**
 * Updates the 'updated_at' timestamp for a specific cart.
 * @param cartId - The ID of the cart to update.
 * @throws Will throw an error if the database update fails.
 */
export const updateCartTimestamp = async (cartId: string | number) => {
  try {
    await knex("cart").where("id", cartId).update({
      updated_at: knex.fn.now(),
    });
  } catch (error) {
    console.error("Error updating cart timestamp:", error);
    throw error;
  }
};

/**
 * Updates the 'updated_at' timestamp for a single cart item and its parent cart.
 * This operation is performed within a transaction to ensure data integrity.
 * @param cartItemId - The ID of the cart item to update.
 * @throws Will throw an error if the transaction fails.
 */
export const updateCartItemTimestamp = async (cartItemId: string | number) => {
  try {
    // Start a transaction to ensure both updates happen together
    await knex.transaction(async (trx) => {
      // Update cart item timestamp
      const cartItem = await trx("cart_item")
        .where("id", cartItemId)
        .update({
          updated_at: knex.fn.now(),
        })
        .returning("cart_id");

      if (cartItem && cartItem[0]) {
        // Update parent cart timestamp
        await trx("cart").where("id", cartItem[0].cart_id).update({
          updated_at: knex.fn.now(),
        });
      }
    });
  } catch (error) {
    console.error("Error updating cart item timestamp:", error);
    throw error;
  }
};

/**
 * Updates the 'updated_at' timestamp for multiple cart items and their unique parent carts.
 * This operation is performed within a single transaction.
 * @param cartItemIds - An array of cart item IDs to update.
 * @throws Will throw an error if the transaction fails.
 */
export const updateMultipleCartItemTimestamps = async (
  cartItemIds: (string | number)[]
) => {
  try {
    await knex.transaction(async (trx) => {
      const cartItems = await trx("cart_item")
        .whereIn("id", cartItemIds)
        .update({
          updated_at: knex.fn.now(),
        })
        .returning("cart_id");

      const uniqueCartIds = [...new Set(cartItems.map((item) => item.cart_id))];

      if (uniqueCartIds.length > 0) {
        await trx("cart").whereIn("id", uniqueCartIds).update({
          updated_at: knex.fn.now(),
        });
      }
    });
  } catch (error) {
    console.error("Error updating multiple cart item timestamps:", error);
    throw error;
  }
};
