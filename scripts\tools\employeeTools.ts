import { createEmployee } from "../../src/services/accessService/createEmployee";
import {
  createFirebaseAccount,
  deleteFirebaseAccount,
} from "../../src/services/accessService/authController";
import knex from "../../knex/knex";

const createEmployeeScript = async () => {
  // const employee1 = await createEmployee({
  //   name: "Driver R1",
  //   email: "",
  //   password: "CGsnacks!",
  //   phone: "",
  //   appAccess: true,
  //   dashboardAccess: false,
  //   roleIds: ["3"],
  //   supplierId: "31",
  // });
  // const employee2 = await createEmployee({
  //   name: "Driver Account 2",
  //   email: "<EMAIL>",
  //   password: "CGsnacks!",
  //   phone: "",
  //   appAccess: true,
  //   dashboardAccess: false,
  //   roleIds: ["3"],
  //   supplierId: "31",
  // });
  // const employee3 = await createEmployee({
  //   name: "Driver Account 3",
  //   email: "<EMAIL>",
  //   password: "CGsnacks!",
  //   phone: "",
  //   appAccess: true,
  //   dashboardAccess: false,
  //   roleIds: ["3"],
  //   supplierId: "31",
  // });
  // const employee6 = await createEmployee({
  //   name: "Driver R6",
  //   email: "<EMAIL>",
  //   password: "WhitestoneDriver#123",
  //   phone: "",
  //   appAccess: true,
  //   dashboardAccess: false,
  //   roleIds: ["3"],
  //   supplierId: "68",
  // });
  // console.log(employee1, employee2, employee3);
};

const initializeRoles = async () => {
  const roles = await knex("roles").insert([
    { name: "Admin", description: "Admin role" },
    { name: "Sales Rep", description: "Sales Rep role" },
    { name: "Driver", description: "Driver role" },
    { name: "Sales Supervisor", description: "Sales Supervisor role" },
  ]);
  console.log(roles);
};

const testAuthCreation = async () => {
  await createFirebaseAccount({
    email: "<EMAIL>",
    password: "attainingplanets!123",
    employeeId: "1",
    supplierId: "69",
  });
};

const testAuthDeletion = async () => {
  await deleteFirebaseAccount({
    employeeId: "1",
    supplierId: "69",
  });
};

// Run and close connection
const run = async () => {
  try {
    // await initializeRoles();
    // await createEmployeeScript();
    // await testAuthCreation();
    // await testAuthDeletion();
    // Add other operations here if needed
  } catch (error) {
    console.error("Error:", error);
  } finally {
    // Close the knex connection
    await knex.destroy();
    console.log("Database connection closed");
  }
};
