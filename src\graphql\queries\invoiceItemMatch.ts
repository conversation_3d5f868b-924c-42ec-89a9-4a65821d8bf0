import knex from "../../../knex/knex";
import {
  QueryInvoiceItemMatchArgs,
  MatchStatus,
  InvoiceItem,
} from "../../generated/graphql";

import {
  filterByUpc,
  modifyUpc,
  modifyUpcForSuggestedMatches,
  filterByUpcForInvoiceItems,
} from "../../util/upcs";

const invoiceItemMatch = async (_, args: QueryInvoiceItemMatchArgs) => {
  const { upc, invoiceId } = args.getInvoiceItemMatchInput;

  let modifiedUpc = modifyUpc(upc);
  const invoiceItems: InvoiceItem[] = await knex
    .select("*")
    .from("invoice_item")
    .where("invoice_id", invoiceId)
    .where((whereBuilder) =>
      filterByUpcForInvoiceItems(whereBuilder, modifiedUpc)
    );

  if (invoiceItems.length == 1) {
    return {
      itemMatches: invoiceItems,
      matchStatus: MatchStatus.Single,
    };
  } else if (invoiceItems.length > 1) {
    return {
      itemMatches: invoiceItems,
      matchStatus: MatchStatus.Multiple,
    };
  }
  modifiedUpc = modifyUpcForSuggestedMatches(upc);
  const suggestedItemMatches: InvoiceItem[] = await knex
    .select("*")
    .from("invoice_item")
    .where("invoice_id", invoiceId)
    .where((whereBuilder) =>
      filterByUpcForInvoiceItems(whereBuilder, modifiedUpc)
    );
  if (suggestedItemMatches.length > 0) {
    return {
      itemMatches: suggestedItemMatches,
      matchStatus: MatchStatus.Suggested,
    };
  } else {
    return {
      itemMatches: [],
      matchStatus: MatchStatus.None,
    };
  }
};

export default invoiceItemMatch;
