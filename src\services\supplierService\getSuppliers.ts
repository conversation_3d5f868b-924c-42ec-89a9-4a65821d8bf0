import knex from "../../../knex/knex";

export const getSuppliers = async (userId): Promise<string[]> => {
  const suppliers = await knex
    .select()
    .table("supplier_times")
    .join("supplier", "supplier_times.supplier_id", "supplier.id")
    .where("supplier_times.business_id", userId)
    .orderBy("supplier.id", "asc");
  return suppliers.map((supplier) => supplier.name);
};

export default getSuppliers;
