import { <PERSON><PERSON> } from "knex";
import { Credit, Order, UpdateCreditInput } from "../../generated/graphql";
import { getOrders } from "../orderService/orderService";
import {
  calculateCreditTotal,
  populateCreditItems,
  validateCreditItems,
} from "./createCredit";
import {
  CreditImageRow,
  CreditItemRow,
  CreditRow,
  UpdatableCreditRow,
} from "./db.types";
import getCredits from "./getCredits";

async function updateCredit(
  trx: Knex.Transaction,
  updateCreditInput: UpdateCreditInput
): Promise<Credit> {
  const {
    id,
    invoice_id,
    user_id,
    supplier_id,
    status,
    credit_items,
    archived,
    images,
  } = updateCreditInput;

  const existingCredit = (await getCredits({
    filters: {
      ids: [id],
    },
    supplier_id: supplier_id,
  }).then((res) => res.credits[0])) as Credit;

  if (!existingCredit) {
    throw new Error("Credit not found");
  }

  // TODO: remove these .toString calls once we move everything over to ints.
  const order = (await getOrders({
    supplierId: supplier_id.toString(),
    filters: {
      invoiceIds: [
        invoice_id?.toString() || existingCredit.invoice_id.toString(),
      ],
    },
  }).then((res) => res.orders[0])) as Order;

  try {
    // Update the credit record
    const updateData: UpdatableCreditRow = {
      updated_at: new Date(),
    };

    if (invoice_id) updateData.invoice_id = invoice_id;
    if (user_id) updateData.user_id = user_id;
    if (supplier_id) updateData.supplier_id = supplier_id;
    if (status) updateData.status = status;
    if (archived !== undefined) updateData.archived = archived;

    let total = existingCredit.total;
    // Always update the updated_at timestamp
    updateData.updated_at = new Date();

    // Handle credit items if provided
    if (credit_items && credit_items.length > 0) {
      const validatedItems = await validateCreditItems(
        order.invoice,
        credit_items
      );
      const populatedItems = await populateCreditItems(
        validatedItems,
        order.invoice
      );
      total = calculateCreditTotal(populatedItems);

      // Remove existing credit items for this credit
      await trx("credit_item").where({ credit_id: id }).del();

      // Insert new credit items
      const creditItemsToInsert = populatedItems.map((item) => ({
        credit_id: id,
        ...item,
      }));

      await trx<CreditItemRow>("credit_item").insert(creditItemsToInsert);
    }

    // Handle images if provided
    if (images !== undefined) {
      // Remove existing images
      await trx("credit_image").where({ credit_id: id }).del();

      // Insert new images if any
      if (images.length > 0) {
        const now = new Date();
        await trx<CreditImageRow>("credit_image").insert(
          images.map((image_url) => ({
            credit_id: id,
            url: image_url,
            created_at: now,
            updated_at: now,
          }))
        );
      }
    }

    // Only update if there's actual data to update
    if (Object.keys(updateData).length > 0) {
      await trx<CreditRow>("credit")
        .where({ id })
        .update({
          ...updateData,
          total: total,
        });
    }

    // Return the updated credit with its items
    const updatedCredit = await getCredits({
      filters: {
        ids: [id],
      },
      supplier_id: supplier_id,
    });
    if (updatedCredit.credits.length === 0) {
      throw new Error("Credit not found");
    }

    return updatedCredit.credits[0];
  } catch (error) {
    // Rollback transaction on error
    await trx.rollback();
    throw error;
  }
}

export default updateCredit;
