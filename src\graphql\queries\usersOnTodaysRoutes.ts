import { QueryUsersOnTodaysRoutesArgs, User } from "../../generated/graphql";
import { getUsersScheduledOnDay } from "../../services/userService/userService";

const usersOnTodaysRoutes = async (
  _: any,
  args: QueryUsersOnTodaysRoutesArgs
): Promise<User[]> => {
  const { supplierId } = args;
  const users = await getUsersScheduledOnDay(supplierId, new Date());
  return users;
};

export default usersOnTodaysRoutes;
