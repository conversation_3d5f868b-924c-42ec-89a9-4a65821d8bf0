import {
  mockKnex,
  queryBuilder as queryBuilderOriginal,
} from "../../../knex/mock";
import ItemsBySupplierQuery from "./itemsBySupplier";

const mockedAddLastOrderedDate = jest.fn();
const mockedUpcs = {
  filterByUpc: jest.fn(),
  logUpc: jest.fn(),
  modifyUpc: jest.fn(),
};
mockKnex();
jest.mock("../../services/addLastOrderedDate", () => mockedAddLastOrderedDate);
jest.mock("../../services/upcs", () => mockedUpcs);

describe("Items by Supplier query", () => {
  let itemsBySupplier: typeof ItemsBySupplierQuery;
  let queryBuilder: typeof queryBuilderOriginal;

  beforeAll(async () => {
    ({ default: itemsBySupplier } = await import("./itemsBySupplier"));
    queryBuilderOriginal.andWhere.mockImplementation((builderFn) => {
      builderFn.apply(queryBuilder, [queryBuilder]);
      return queryBuilder;
    });
    queryBuilder = { ...queryBuilderOriginal };
  });
  beforeEach(() => {
    queryBuilder = { ...queryBuilderOriginal };
  });
  const getItemsBySupplierInput = {
    userId: "1",
    supplierId: "5678",
  };
  const DEFAULT_SUPPLIER = "Pitco Foods";
  const TEST_ACC_BLOCKED = [
    "TOBACCO - TAXABLE",
    "TOBACCO ACCESSORIES - TAXABLE",
    "CIGARETTES - TAXABLE",
    "ALCOHOL - TAXABLE",
  ];
  const mockedSuppliers = [{ name: "supplier" }];
  const mockedUsers = [{ name: "users-name" }];
  const mockedItems = [];

  it("selects default supplier when no supplier by ID found", async () => {
    queryBuilder.where.mockResolvedValueOnce([]);

    await itemsBySupplier(undefined, { getItemsBySupplierInput });

    expect(queryBuilder.where).toHaveBeenCalledWith(
      "id",
      getItemsBySupplierInput.supplierId
    );
    expect(queryBuilder.where).toHaveBeenCalledWith(
      "supplier",
      DEFAULT_SUPPLIER
    );
  });

  it("selects returned supplier when supplier by ID found", async () => {
    const supplierName = "supplier";
    queryBuilder.where.mockResolvedValueOnce(mockedSuppliers);

    await itemsBySupplier(undefined, { getItemsBySupplierInput });

    expect(queryBuilder.where).toHaveBeenCalledWith(
      "id",
      getItemsBySupplierInput.supplierId
    );
    expect(queryBuilder.where).toHaveBeenCalledWith("supplier", supplierName);
  });

  it("does not modify, filter by, or log by UPC when no UPC passed", async () => {
    queryBuilder.where.mockResolvedValueOnce([]);

    await itemsBySupplier(undefined, { getItemsBySupplierInput });

    expect(mockedUpcs.filterByUpc).not.toHaveBeenCalled();
  });

  it("modifies, filters by, and logs UPC when UPC passed", async () => {
    const upc = "upc1";
    const modifiedUpc = "upc";
    queryBuilder.where
      .mockResolvedValueOnce([])
      .mockReturnValueOnce(queryBuilder)
      .mockReturnValueOnce(queryBuilder)
      .mockResolvedValueOnce(mockedUsers);
    mockedUpcs.modifyUpc.mockReturnValue(modifiedUpc);

    await itemsBySupplier(undefined, {
      getItemsBySupplierInput: { ...getItemsBySupplierInput, upc },
    });

    expect(mockedUpcs.modifyUpc).toHaveBeenCalledWith(upc);
    expect(mockedUpcs.filterByUpc).toHaveBeenCalledWith(
      expect.anything(),
      modifiedUpc
    );
    expect(mockedUpcs.logUpc).toHaveBeenCalled();
  });

  it("blocks tobacco products for test userId 1", async () => {
    await itemsBySupplier(undefined, {
      getItemsBySupplierInput: { ...getItemsBySupplierInput },
    });

    expect(queryBuilder.whereNotIn).toHaveBeenCalledWith(
      "nacs_category",
      TEST_ACC_BLOCKED
    );
    expect(queryBuilder.orWhereNull).toHaveBeenCalledWith("nacs_category");
  });

  it("does not block tobacco products for non-test users", async () => {
    await itemsBySupplier(undefined, {
      getItemsBySupplierInput: { ...getItemsBySupplierInput, userId: "1234" },
    });

    expect(queryBuilder.whereNotIn).toHaveBeenCalledWith("nacs_category", []);
    expect(queryBuilder.orWhereNull).toHaveBeenCalledWith("nacs_category");
  });

  it("uses default limit and offset when pagination option not passed", async () => {
    queryBuilder.where.mockResolvedValueOnce([]);

    await itemsBySupplier(undefined, { getItemsBySupplierInput });

    expect(queryBuilder.limit).toHaveBeenCalledWith(20);
    expect(queryBuilder.offset).toHaveBeenCalledWith(0);
  });

  it("use custom pagination options when pagination argument passed", async () => {
    const pagination = {
      limit: 5,
      offset: 1,
    };
    queryBuilder.where.mockResolvedValueOnce([]);

    await itemsBySupplier(undefined, {
      getItemsBySupplierInput: {
        ...getItemsBySupplierInput,
        pagination,
      },
    });

    expect(queryBuilder.limit).toHaveBeenCalledWith(pagination.limit);
    expect(queryBuilder.offset).toHaveBeenCalledWith(pagination.offset);
  });

  it("add's user's last ordered date", async () => {
    queryBuilderOriginal.orderBy.mockResolvedValueOnce(mockedItems);

    await itemsBySupplier(undefined, {
      getItemsBySupplierInput,
    });

    expect(mockedAddLastOrderedDate).toHaveBeenCalledWith(
      mockedItems,
      getItemsBySupplierInput.userId
    );
  });
});
