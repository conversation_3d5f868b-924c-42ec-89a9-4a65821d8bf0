import { <PERSON><PERSON> } from "knex";
import knex from "../../../knex/knex";
import { UpdatePromotionInput } from "../../generated/graphql";
import { getPromotion } from "./getPromotions";

export const updatePromotion = async (
  data: UpdatePromotionInput,
  trx?: Knex.Transaction
): Promise<any> => {
  const knexInstance = trx || knex;

  // Build update object with only defined fields
  const updateFields: any = {
    updated_at: knex.fn.now(),
  };

  if (data.name !== undefined) updateFields.name = data.name;
  if (data.promotion_type !== undefined)
    updateFields.promotion_type_id = data.promotion_type;
  if (data.start_date !== undefined) updateFields.start_date = data.start_date;
  if (data.end_date !== undefined) updateFields.end_date = data.end_date;
  if (data.applies_to_all_items !== undefined)
    updateFields.applies_to_all_items = data.applies_to_all_items;
  if (data.applies_to_all_users !== undefined)
    updateFields.applies_to_all_users = data.applies_to_all_users;
  if (data.max_uses_per_customer !== undefined)
    updateFields.max_uses_per_customer = data.max_uses_per_customer;
  if (data.total_usage_limit !== undefined)
    updateFields.total_usage_limit = data.total_usage_limit;
  if (data.buy_quantity !== undefined)
    updateFields.buy_quantity = data.buy_quantity;
  if (data.discount_amount !== undefined)
    updateFields.discount_amount = data.discount_amount;
  if (data.discount_percentage !== undefined)
    updateFields.discount_percentage = data.discount_percentage;
  if (data.free_quantity !== undefined)
    updateFields.free_quantity = data.free_quantity;
  if (data.min_order_amount !== undefined)
    updateFields.min_order_amount = data.min_order_amount;
  if (data.active !== undefined) updateFields.active = data.active;
  if (data.archived !== undefined) updateFields.archived = data.archived;

  const [promotion] = await knexInstance("promotions")
    .where("id", data.id)
    .update(updateFields)
    .returning("*");

  // Update items if specified
  if (data.items !== undefined) {
    await knexInstance("promotion_items")
      .where("promotion_id", data.id)
      .delete();
    if (!data.applies_to_all_items && data.items?.length) {
      await knexInstance("promotion_items").insert(
        data.items.map((item) => ({
          promotion_id: data.id,
          item_id: item.item_id,
        }))
      );
    }
  }

  // Update users if specified
  if (data.users !== undefined) {
    await knexInstance("promotion_users")
      .where("promotion_id", data.id)
      .delete();
    if (!data.applies_to_all_users && data.users?.length) {
      await knexInstance("promotion_users").insert(
        data.users.map((user) => ({
          promotion_id: data.id,
          user_id: user.user_id,
        }))
      );
    }
  }

  return getPromotion(data.supplier_id, data.id);
};