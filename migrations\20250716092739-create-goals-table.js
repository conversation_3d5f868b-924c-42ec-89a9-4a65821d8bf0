"use strict";

var dbm;
var type;
var seed;

/**
 * We receive the dbmigrate dependency from dbmigrate initially.
 * This enables us to not have to rely on NODE_PATH.
 */
exports.setup = function (options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

exports.up = function (db) {
  return db
    .createTable("goals", {
      id: { type: "int", primaryKey: true, autoIncrement: true },
      supplier_id: { type: "int", notNull: true },
      name: { type: "string", notNull: true },
      type: { type: "string", notNull: true },
      period: { type: "string", notNull: true },
      target_amount: { type: "decimal", notNull: true },
      start_date: { type: "date", notNull: true },
      end_date: { type: "date", notNull: false },
      is_active: { type: "boolean", notNull: true, defaultValue: true },
      created_at: {
        type: "datetime",
        notNull: true,
        defaultValue: new String("CURRENT_TIMESTAMP"),
      },
      updated_at: {
        type: "datetime",
        notNull: true,
        defaultValue: new String("CURRENT_TIMESTAMP"),
      },
    })
    .then(() => {
      return db.createTable("goal_assignments", {
        id: { type: "int", primaryKey: true, autoIncrement: true },
        goal_id: { type: "int", notNull: true },
        employee_id: { type: "int", notNull: false },
        target_amount: { type: "decimal", notNull: true },
        created_at: {
          type: "datetime",
          notNull: true,
          defaultValue: new String("CURRENT_TIMESTAMP"),
        },
        updated_at: {
          type: "datetime",
          notNull: true,
          defaultValue: new String("CURRENT_TIMESTAMP"),
        },
      });
    })
    .then(() => {
      return db.addForeignKey(
        "goals",
        "supplier",
        "goals_supplier_id_fk",
        {
          supplier_id: "id",
        },
        {
          onDelete: "CASCADE",
          onUpdate: "RESTRICT",
        }
      );
    })
    .then(() => {
      return db.addForeignKey(
        "goal_assignments",
        "goals",
        "goal_assignments_goal_id_fk",
        {
          goal_id: "id",
        },
        {
          onDelete: "CASCADE",
          onUpdate: "RESTRICT",
        }
      );
    })
    .then(() => {
      return db.addForeignKey(
        "goal_assignments",
        "employees",
        "goal_assignments_employee_id_fk",
        {
          employee_id: "id",
        },
        {
          onDelete: "CASCADE",
          onUpdate: "RESTRICT",
        }
      );
    })
    .then(() => {
      return db.addIndex("goals", "goals_supplier_id_active_idx", [
        "supplier_id",
        "is_active",
      ]);
    })
    .then(() => {
      return db.addIndex(
        "goal_assignments",
        "goal_assignments_goal_employee_idx",
        ["goal_id", "employee_id"]
      );
    });
};

exports.down = function (db) {
  return db.dropTable("goal_assignments").then(() => db.dropTable("goals"));
};

exports._meta = {
  version: 1,
};
