import {
  CreditMemoRet,
  InvoiceRet,
} from "conductor-node/dist/src/integrations/qbd/qbdTypes";
import knex from "../../../knex/knex";
import { env } from "../../config/environment";
import { Invoice, Item, User } from "../../generated/graphql";
import QuickBooksDesktopClient from "../../lib/quickBooksDesktop/client";
import { pullCustomersInfo } from "../../lib/quickBooksDesktop/customers";
import {
  pushCreditMemosInfo,
  pushInvoicesInfo,
} from "../../lib/quickBooksDesktop/invoices";
import { pullItemsInfo } from "../../lib/quickBooksDesktop/items";
import {
  deleteItems,
  updateOrAddItems,
} from "../integrationService/algoliaOperations";
import { getSupplierConfig } from "../supplierService";

export const syncItems = async (
  client: QuickBooksDesktopClient,
  supplierId: string,
  supplierName: string,
  skipPull = true,
  skipPush = true,
  includeArchivedItems = false,
  replaceDBItems = false
) => {
  const itemSets = await fetchItemSets(
    client,
    supplierId,
    supplierName,
    includeArchivedItems
  );
  console.error(
    "QB Items:",
    itemSets.qbItems.length,
    "DB Items:",
    itemSets.dbItems.length,
    "Common Newer QB Items:",
    itemSets.commonNewerQbItems.length,
    "Common Newer DB Items:",
    itemSets.commonNewerDbItems.length,
    "QB Only Items:",
    itemSets.qbOnlyItems.length,
    "DB Only Items:",
    itemSets.dbOnlyItems.length
  );
  console.log("Item Sets:", JSON.stringify(itemSets));
  if (!skipPull) {
    await pullItemsFromQB(itemSets, replaceDBItems);
  }
  // if (!skipPush) {
  //   await pushToQB(client, itemSets);
  // }
};

export const pullItemsFromQB = async (
  itemSets: ItemSets,
  replaceDBItems = false
) => {
  try {
    // console.log("Inserting/updating the following items in the database:");
    // console.log(JSON.stringify(itemSets.commonNewerQbItems));
    // console.log(JSON.stringify(itemSets.qbOnlyItems));
    await knex.transaction(async (trx) => {
      if (itemSets.commonNewerQbItems.length > 0) {
        const updatedItems = await trx("item")
          .insert(itemSets.commonNewerQbItems)
          .onConflict("id")
          .merge()
          .returning("*")
          .transacting(trx);
        updatedItems.forEach((item) => {
          item.objectID = item.id;
        });
        console.log(
          "[Pull from QB] Updated items:",
          JSON.stringify(updatedItems)
        );
        if (env.production) {
          await updateOrAddItems(updatedItems);
        }
      }
      if (itemSets.qbOnlyItems.length > 0) {
        const newItems = await trx("item")
          .insert(itemSets.qbOnlyItems)
          .returning("*")
          .transacting(trx);
        newItems.forEach((item) => {
          item.objectID = item.id;
        });
        console.log("[Pull from QB] New items:", newItems);
        if (env.production) {
          await updateOrAddItems(newItems);
        }
      }
      if (replaceDBItems) {
        const dbItemsToDelete = itemSets.dbOnlyItems.map((item) => item.id);
        if (dbItemsToDelete.length > 0) {
          await trx("item")
            .whereIn("id", dbItemsToDelete)
            .update({ archived: true })
            .transacting(trx);
          console.log(
            "[Pull from QB] Archived the following items from the database:",
            dbItemsToDelete
          );
          if (env.production) {
            await deleteItems(dbItemsToDelete);
          }
        }
        const dbItemsToRestore = itemSets.commonNewerDbItems.map((item) => ({
          name: item.name,
          id: item.id,
        }));
        if (dbItemsToRestore.length > 0) {
          const olderQBItems = itemSets.qbItems.reduce<Item[]>((acc, item) => {
            const foundItem = dbItemsToRestore.find(
              (i) => i.name === item.name
            );
            if (foundItem) {
              const itemWithoutQBDProps = {
                ...item,
                id: foundItem.id,
                objectID: foundItem.id,
              };
              delete itemWithoutQBDProps["incomeAccountRef"];
              delete itemWithoutQBDProps["assetAccountRef"];
              delete itemWithoutQBDProps["unitOfMeasureSetRef"];
              delete itemWithoutQBDProps["cogsAccountRef"];
              acc.push(itemWithoutQBDProps);
            }
            return acc;
          }, []);
          const updatedItems = await trx("item")
            .insert(olderQBItems)
            .onConflict("id")
            .merge()
            .returning("*")
            .transacting(trx);
          updatedItems.forEach((item) => {
            item.objectID = item.id;
          });
          console.log(
            "[Pull from QB] Restoring the following items in the database:",
            olderQBItems
          );
          if (env.production) {
            await updateOrAddItems(updatedItems);
          }
        }
      }
    });
  } catch (error) {
    console.error(error);
    const errorMessage = `Couldn't pull from QB: ${error}`;
    console.error(errorMessage);
    throw new Error(errorMessage);
  }
};

const fetchItemSets = async (
  client: QuickBooksDesktopClient,
  supplierId: string,
  supplierName: string,
  includeArchivedItems = false
): Promise<ItemSets> => {
  const qbItems = (
    (await pullItemsInfo(
      client,
      supplierId,
      undefined,
      true,
      true,
      false
    )) as (Item & Record<string, unknown>)[]
  ).map((item) => ({
    ...item,
    supplier: supplierName,
  }));
  const dbItemsQuery = knex<Item>("item")
    .select("*")
    .distinctOn("name")
    .where({ supplier: supplierName })
    .orderBy("name")
    .orderBy("updated_at", "desc");
  if (!includeArchivedItems) {
    dbItemsQuery.andWhere({ archived: false });
  }
  dbItemsQuery.orderBy([
    { column: "name" },
    { column: "updated_at", order: "desc" },
    { column: "id", order: "desc" },
  ]);
  const dbItems = await dbItemsQuery;
  const commonNewerQbItems = qbItems
    .filter((qbItem) =>
      dbItems.some(
        (dbItem) =>
          (dbItem.name === qbItem.name ||
            dbItem.metadata === qbItem.metadata) &&
          dbItem.updated_at < qbItem.updated_at
      )
    )
    .map((item) => {
      const cleanedItem = {
        ...item,
        id: dbItems.find(
          (i) => i.name === item.name || i.metadata === item.metadata
        )?.id,
        archived: false,
      };
      delete cleanedItem["incomeAccountRef"];
      delete cleanedItem["assetAccountRef"];
      delete cleanedItem["unitOfMeasureSetRef"];
      delete cleanedItem["cogsAccountRef"];
      return cleanedItem;
    });
  const commonNewerDbItems = dbItems
    .filter((dbItem) =>
      qbItems.some(
        (qbItem) =>
          qbItem.name === dbItem.name && qbItem.updated_at < dbItem.updated_at
      )
    )
    .map((item) => ({
      ...item,
      qb_id: qbItems.find(
        (i) => i.name === item.name || i.metadata === item.metadata
      )?.qb_id,
    }));
  const qbOnlyItems = qbItems
    .filter((qbItem) =>
      dbItems.every(
        (dbItem) =>
          dbItem.name !== qbItem.name && dbItem.metadata !== qbItem.metadata
      )
    )
    .map((item) => {
      const cleanedItem = {
        ...item,
        oos: false,
        local_item: false,
        outdated: false,
        archived: false,
      };
      delete cleanedItem["incomeAccountRef"];
      delete cleanedItem["assetAccountRef"];
      delete cleanedItem["unitOfMeasureSetRef"];
      delete cleanedItem["cogsAccountRef"];
      return cleanedItem;
    });
  const dbOnlyItems = dbItems
    .filter((dbItem) =>
      qbItems.every(
        (qbItem) =>
          qbItem.name !== dbItem.name && qbItem.metadata !== dbItem.metadata
      )
    )
    .map((item) => {
      const cleanedItem = { ...item };
      delete cleanedItem.qb_id;
      delete cleanedItem.qb_sync_token;
      return cleanedItem;
    });

  return {
    qbItems,
    dbItems,
    commonNewerQbItems,
    commonNewerDbItems,
    qbOnlyItems,
    dbOnlyItems,
  };
};

type ItemSets = {
  qbItems: (Item & Record<string, unknown>)[];
  dbItems: Item[];
  commonNewerQbItems: Item[];
  commonNewerDbItems: Item[];
  qbOnlyItems: Item[];
  dbOnlyItems: Item[];
};

export const syncCustomers = async (
  client: QuickBooksDesktopClient,
  supplierId: string,
  skipPull = true,
  includeArchivedCustomers = false,
  replaceDBCustomers = false
) => {
  const customerSets = await fetchCustomerSets(
    client,
    supplierId,
    includeArchivedCustomers
  );
  console.error(
    "QB Customers:",
    customerSets.qbCustomers.length,
    "DB Customers:",
    customerSets.dbCustomers.length,
    "Common Newer QB Customers:",
    customerSets.commonNewerQbCustomers.length,
    "Common Newer DB Customers:",
    customerSets.commonNewerDbCustomers.length,
    "QB Only Customers:",
    customerSets.qbOnlyCustomers.length,
    "DB Only Customers:",
    customerSets.dbOnlyCustomers.length
  );
  console.log("Customer Sets:", JSON.stringify(customerSets));
  if (!skipPull) {
    await pullCustomersFromQB(customerSets, supplierId, replaceDBCustomers);
  }
};

const pullCustomersFromQB = async (
  customerSets: CustomerSets,
  supplierId: string,
  replaceDBCustomers = false
) => {
  try {
    // console.log("Inserting/updating the following customers in the database:");
    // console.log(JSON.stringify(customerSets.commonNewerQbCustomers));
    // console.log(JSON.stringify(customerSets.qbOnlyCustomers));
    await knex.transaction(async (trx) => {
      if (customerSets.commonNewerQbCustomers.length > 0) {
        const updatedCustomers = await trx("attain_user")
          .insert(customerSets.commonNewerQbCustomers)
          .onConflict("id")
          .merge()
          .returning("*")
          .transacting(trx);
        console.log(
          "[Pull from QB] Updated customers:",
          JSON.stringify(updatedCustomers)
        );
      }
      if (customerSets.qbOnlyCustomers.length > 0) {
        const newCustomers = await trx("attain_user")
          .insert(customerSets.qbOnlyCustomers)
          .returning("*")
          .transacting(trx);

        const newCustomerCarts = newCustomers.map((customer) => ({
          id: customer.id,
          subtotal: 0,
          user_id: customer.id,
          created_at: new Date(),
          updated_at: new Date(),
        }));
        await knex("cart").insert(newCustomerCarts).transacting(trx);

        const newCustomerSupplierTimes = newCustomers.map((customer) => ({
          business_id: customer.id,
          supplier_id: supplierId,
        }));
        await knex("supplier_times")
          .insert(newCustomerSupplierTimes)
          .transacting(trx);

        console.log("[Pull from QB] New customers:", newCustomers);
      }
      if (replaceDBCustomers) {
        const dbCustomersToDelete = customerSets.dbOnlyCustomers.map(
          (customer) => customer.id
        );
        if (dbCustomersToDelete.length > 0) {
          await trx("attain_user")
            .whereIn("id", dbCustomersToDelete)
            .update({ archived: true })
            .transacting(trx);
          console.log(
            "[Pull from QB] Archived the following customers from the database:",
            dbCustomersToDelete
          );
        }
        const dbCustomersToRestore = customerSets.commonNewerDbCustomers.map(
          (customer) => ({
            name: customer.name,
            id: customer.id,
          })
        );
        if (dbCustomersToRestore.length > 0) {
          const olderQBCustomers = customerSets.qbCustomers.reduce<Item[]>(
            (acc, customer) => {
              const foundCustomer = dbCustomersToRestore.find(
                (i) => i.name === customer.name
              );
              if (foundCustomer) {
                const customerWithoutQBDProps = {
                  ...customer,
                  id: foundCustomer.id,
                };
                delete customerWithoutQBDProps["customerTypeRef"];
                delete customerWithoutQBDProps["salesRepRef"];
                acc.push(customerWithoutQBDProps);
              }
              return acc;
            },
            []
          );
          const updatedCustomers = await trx("attain_user")
            .insert(olderQBCustomers)
            .onConflict("id")
            .merge()
            .returning("*")
            .transacting(trx);
          updatedCustomers.forEach((customer) => {
            customer.objectID = customer.id;
          });
          console.log(
            "[Pull from QB] Restoring the following customers in the database:",
            olderQBCustomers
          );
        }
      }
    });
  } catch (error) {
    console.error(error);
    const errorMessage = `Couldn't pull from QB: ${error}`;
    console.error(errorMessage);
    throw new Error(errorMessage);
  }
};

const fetchCustomerSets = async (
  client: QuickBooksDesktopClient,
  supplierId: string,
  includeArchivedCustomers = false
): Promise<CustomerSets> => {
  const qbCustomers = (await pullCustomersInfo(client)).map((customer) => ({
    ...customer,
    created_by: "manual-qb-import-code",
    archived: false,
    supplier_beta: true,
    approved: true,
  }));
  const dbCustomersQuery = knex<User>("attain_user")
    .select("attain_user.*")
    .distinctOn("name")
    .innerJoin("supplier_times", "attain_user.id", "supplier_times.business_id")
    .where({ "supplier_times.supplier_id": supplierId })
    .orderBy("name")
    .orderBy("updated_at", "desc");
  if (!includeArchivedCustomers) {
    dbCustomersQuery.andWhere({ archived: false });
  }
  dbCustomersQuery.orderBy([
    { column: "name" },
    { column: "updated_at", order: "desc" },
    { column: "id", order: "desc" },
  ]);
  const dbCustomers = await dbCustomersQuery;
  const commonNewerQbCustomers = qbCustomers
    .filter((qbCustomer) =>
      dbCustomers.some(
        (dbCustomer) =>
          dbCustomer.name === qbCustomer.name &&
          dbCustomer.updated_at < qbCustomer.updated_at
      )
    )
    .map((customer) => {
      const cleanedCustomer = {
        ...customer,
        id: dbCustomers.find((i) => i.name === customer.name)?.id,
        approved: true,
      };
      delete cleanedCustomer["customerTypeRef"];
      delete cleanedCustomer["salesRepRef"];
      return cleanedCustomer;
    });
  const commonNewerDbCustomers = dbCustomers
    .filter((dbCustomer) =>
      qbCustomers.some(
        (qbCustomer) =>
          qbCustomer.name === dbCustomer.name &&
          qbCustomer.updated_at < dbCustomer.updated_at
      )
    )
    .map((customer) => ({
      ...customer,
      qb_id: qbCustomers.find((i) => i.name === customer.name)?.qb_id,
    }));
  const qbOnlyCustomers = qbCustomers
    .filter((qbCustomer) =>
      dbCustomers.every((dbCustomer) => dbCustomer.name !== qbCustomer.name)
    )
    .map((customer) => {
      const cleanedCustomer = {
        ...customer,
        archived: false,
        supplier_beta: true,
        approved: true,
      };
      delete cleanedCustomer["customerTypeRef"];
      delete cleanedCustomer["salesRepRef"];
      return cleanedCustomer;
    });
  const dbOnlyCustomers = dbCustomers
    .filter(
      (dbCustomer) =>
        qbCustomers.every(
          (qbCustomer) => qbCustomer.name !== dbCustomer.name
        ) && !dbCustomer.name.toLowerCase().includes("driver")
    )
    .map((customer) => {
      const cleanedCustomer = { ...customer };
      delete cleanedCustomer.qb_id;
      delete cleanedCustomer.qb_sync_token;
      return cleanedCustomer;
    });

  return {
    qbCustomers,
    dbCustomers,
    commonNewerQbCustomers,
    commonNewerDbCustomers,
    qbOnlyCustomers,
    dbOnlyCustomers,
  };
};

type CustomerSets = {
  qbCustomers: (Item & Record<string, unknown>)[];
  dbCustomers: Item[];
  commonNewerQbCustomers: Item[];
  commonNewerDbCustomers: Item[];
  qbOnlyCustomers: Item[];
  dbOnlyCustomers: Item[];
};

export const syncInvoices = async (
  client: QuickBooksDesktopClient,
  supplierId: string,
  skipPush = true
) => {
  const invoiceSets = await fetchInvoiceSets(supplierId);
  console.error(
    "Synced Invoices:",
    invoiceSets.syncedInvoices.length,
    "Unsynced Invoices:",
    invoiceSets.unsyncedInvoices.length
  );
  // console.log("Invoice Sets:", JSON.stringify(invoiceSets));
  if (!skipPush) {
    await pushInvoicesToQB(client, invoiceSets);
  }
};

const pushInvoicesToQB = async (
  client: QuickBooksDesktopClient,
  invoiceSets: InvoiceSets
) => {
  const qbInvoices = await pushInvoicesInfo(
    client,
    invoiceSets.unsyncedInvoices,
    async (invoice: InvoiceRet) => {
      if (invoice.RefNumber) {
        await knex("invoice")
          .update("qb_id", invoice.TxnID)
          .where("id", invoice.RefNumber)
          .catch((e) => {
            console.error(
              `Error updating invoice ${invoice.RefNumber} QB ID ${invoice.TxnID} in the database:`,
              e
            );
          });
        console.info(`Synced Invoice ${invoice.RefNumber}`);
      } else {
        console.error("Couldn't sync Invoice");
      }
    }
  );
  console.log("[Push to QB] Added Invoices:", JSON.stringify(qbInvoices));
  // await Promise.all(
  //   qbInvoices
  //     .filter((invoice) => invoice.RefNumber)
  //     .map((invoice) =>
  //       knex("invoice")
  //         .update("qb_id", invoice.TxnID)
  //         .where("id", invoice.RefNumber)
  //     )
  // );
  console.log("[Push to QB] Updated Invoice QB IDs in the database");
};

export const fetchInvoiceSets = async (supplierId: string) => {
  const supplierConfig = await getSupplierConfig(supplierId);

  const invoices: Invoice[] = await knex<Invoice>("invoice")
    .select(
      "invoice.*",
      knex.raw(
        `json_build_object('id', "attain_user".id, 'name', "attain_user".name, 'qb_id', "attain_user".qb_id) as "customerDetails"`
      ),
      knex.raw(
        `array_agg(json_build_object('id', "invoice_item".id, 'name', "invoice_item".name, 'price', "invoice_item".price, 'quantity', "invoice_item".quantity, 'qb_id', "invoice_item".qb_id)) as "invoiceItems"`
      )
    )
    .leftJoin("attain_user", "invoice.user_id", "attain_user.id")
    .leftJoin("invoice_item", "invoice.id", "invoice_item.invoice_id")
    .leftJoin("order_detail", "invoice.order_id", "order_detail.id")
    .orderBy("id", "desc")
    .groupBy("invoice.id", "attain_user.id")
    .where("invoice.supplier_id", "68")
    .whereNot("invoice.archived", true)
    .where("invoice.subtotal", ">=", 0)
    .where("order_detail.status", "Delivered")
    .modify((query) => {
      if (supplierConfig.qb_credit_memo_date_filter) {
        query.andWhere(
          "invoice.date_created",
          ">",
          supplierConfig.qb_credit_memo_date_filter
        );
      }
    });

  // for (let i = 0; i < invoices.length; i++) {
  //   const invoice = invoices[i];
  //   const invoiceItems = await knex("invoice_item")
  //     .select("price", "quantity", "qb_id")
  //     .where("invoice_id", invoice.id)
  //     .orderBy("id", "asc");
  //   // const orderDetails = await knex("order_detail")
  //   //   .select("*")
  //   //   .where("id", invoice.order_id);
  //   invoice["invoiceItems"] = invoiceItems;
  //   // invoice["orderDetails"] = orderDetails[0];
  // }

  return {
    syncedInvoices: invoices.filter((invoice) => invoice.qb_id),
    unsyncedInvoices: invoices.filter(
      (invoice) =>
        !invoice.qb_id && invoice.invoiceItems.filter((i) => i.id).length > 0
    ),
  };
};

type InvoiceSets = {
  syncedInvoices: Invoice[];
  unsyncedInvoices: Invoice[];
};

export const syncCreditMemos = async (
  client: QuickBooksDesktopClient,
  supplierId: string,
  skipPush = true,
  addInventoryAdjustments = false
) => {
  const creditMemoSets = await fetchCreditMemoSets(supplierId);
  console.error(
    "Synced Credit Memos:",
    creditMemoSets.syncedCreditMemos.length,
    "Unsynced Credit Memos:",
    creditMemoSets.unsyncedCreditMemos.length
  );
  console.log("Credit Memo Sets:", JSON.stringify(creditMemoSets));
  if (!skipPush) {
    await pushCreditMemosToQB(client, creditMemoSets, addInventoryAdjustments);
  }
};

const pushCreditMemosToQB = async (
  client: QuickBooksDesktopClient,
  creditMemoSets: CreditMemoSets,
  addInventoryAdjustments = false
) => {
  const [qbCreditMemos, qbInventoryAdjustments] = await pushCreditMemosInfo(
    client,
    creditMemoSets.unsyncedCreditMemos,
    addInventoryAdjustments,
    async (creditMemo: CreditMemoRet) => {
      if (creditMemo.RefNumber) {
        await knex("invoice")
          .update("qb_id", creditMemo.TxnID)
          .where("id", creditMemo.RefNumber)
          .catch((e) => {
            console.error(
              `Error updating creditMemo ${creditMemo.RefNumber} QB ID ${creditMemo.TxnID} in the database:`,
              e
            );
          });
        console.info(`Synced Credit Memo ${creditMemo.RefNumber}`);
      } else {
        console.error("Couldn't sync Credit Memo");
      }
    }
  );
  console.log("[Push to QB] Added Credit Memos", JSON.stringify(qbCreditMemos));
  if (addInventoryAdjustments) {
    console.log(
      "[Push to QB] Added Inventory Adjustments",
      JSON.stringify(qbInventoryAdjustments)
    );
  }
  // await Promise.all(
  //   qbCreditMemos
  //     .filter((creditMemo) => creditMemo.RefNumber)
  //     .map((creditMemo) =>
  //       knex("invoice")
  //         .update("qb_id", creditMemo.TxnID)
  //         .where("id", creditMemo.RefNumber)
  //     )
  // );
  console.log("[Push to QB] Updated Credit Memo QB IDs in the database");
};

const fetchCreditMemoSets = async (supplierId: string) => {
  const creditMemos: Invoice[] = await knex<Invoice>("invoice")
    .select(
      "invoice.*",
      knex.raw(
        `json_build_object('id', "attain_user".id, 'name', "attain_user".name, 'qb_id', "attain_user".qb_id) as "customerDetails"`
      ),
      knex.raw(
        `array_agg(json_build_object('id', "invoice_item".id, 'name', "invoice_item".name, 'price', "invoice_item".price, 'quantity', "invoice_item".quantity, 'qb_id', "invoice_item".qb_id)) as "invoiceItems"`
      )
    )
    .leftJoin("attain_user", "invoice.user_id", "attain_user.id")
    .leftJoin("invoice_item", "invoice.id", "invoice_item.invoice_id")
    .leftJoin("order_detail", "invoice.order_id", "order_detail.id")
    .orderBy("id", "desc")
    .groupBy("invoice.id", "attain_user.id")
    .where("invoice.supplier_id", "68")
    .whereNot("invoice.archived", true)
    .where("invoice.subtotal", "<", 0)
    .where("order_detail.status", "Delivered")
    .modify((query) => {
      if (supplierId === "68") {
        query.andWhere("invoice.date_created", ">=", "2024-08-30");
      }
    });

  return {
    syncedCreditMemos: creditMemos.filter((creditMemo) => creditMemo.qb_id),
    unsyncedCreditMemos: creditMemos.filter(
      (creditMemo) =>
        !creditMemo.qb_id &&
        creditMemo.invoiceItems.filter((i) => i.id).length > 0
    ),
  };
};

type CreditMemoSets = {
  syncedCreditMemos: Invoice[];
  unsyncedCreditMemos: Invoice[];
};
