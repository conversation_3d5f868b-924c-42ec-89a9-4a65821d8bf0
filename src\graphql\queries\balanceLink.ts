import knex from "../../../knex/knex";
import axios from "axios";
import { QueryBalanceLinkArgs } from "../../generated/graphql";

const BalanceLink = async (_, args: QueryBalanceLinkArgs) => {
  const { businessId } = args;
  const user = await knex("attain_user").select("*").where("id", businessId);
  const buyerId = user[0].buyer_id;
  const url = process.env.BALANCE_API_LINK + "/buyers/" + buyerId + "/token";
  const result = await axios.post(
    url,
    { scope: "ADD_PAYMENT_METHOD" },
    {
      headers: {
        accept: "application/json",
        "content-type": "application/json",
        "x-balance-key": process.env.BALANCE_API_KEY,
      },
    }
  );
  const data = result.data;
  if (data) {
    const token = data.token;
    return {
      link: `https://checkout-v2.getbalance.com/payment-method?token=${token}&allowedPaymentMethods=creditCard,achDebit`,
    };
  }
};

export default BalanceLink;
// export {Orders, UserOrders}
