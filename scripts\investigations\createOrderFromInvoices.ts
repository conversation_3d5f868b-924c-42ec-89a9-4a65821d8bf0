import { createOrderFromInvoice } from "../../src/services/orderService/createOrder";

// Create multiple orders from a list of invoice IDs
async function main() {
  try {
    const invoiceIds = process.argv.slice(2);

    if (invoiceIds.length === 0) {
      console.error("Error: Please provide at least one invoice ID");
      console.error(
        "Usage: npx ts-node scripts/createOrderFromInvoice.ts [invoiceId1] [invoiceId2] ..."
      );
      process.exit(1);
    }

    console.log(`Processing ${invoiceIds.length} invoice(s)...`);

    const results = {
      success: [],
      failed: [],
    };

    for (let i = 0; i < invoiceIds.length; i++) {
      const invoiceId = invoiceIds[i];
      console.log(
        `[${i + 1}/${
          invoiceIds.length
        }] Creating order from invoice ID: ${invoiceId}`
      );

      try {
        const order = await createOrderFromInvoice(invoiceId);
        console.log(
          `✅ Order created successfully for invoice ${invoiceId}: Order ID ${order.id}`
        );
        results.success.push({ invoiceId, orderId: order.id });
      } catch (error) {
        console.error(
          `❌ Failed to create order for invoice ${invoiceId}:`,
          error.message
        );
        results.failed.push({ invoiceId, error: error.message });
      }
    }

    // Summary
    console.log("\n===== SUMMARY =====");
    console.log(`Total processed: ${invoiceIds.length}`);
    console.log(`Successful: ${results.success.length}`);
    console.log(`Failed: ${results.failed.length}`);

    if (results.failed.length > 0) {
      console.log("\n===== FAILED INVOICE IDS =====");
      console.log(results.failed.map((f) => f.invoiceId).join(", "));

      console.log("\n===== DETAILED FAILURES =====");
      results.failed.forEach((failure) => {
        console.log(`Invoice ID ${failure.invoiceId}: ${failure.error}`);
      });

      process.exit(1);
    }
    process.exit(0);
  } catch (error) {
    console.error("Error in script execution:", error);
    process.exit(1);
  }
}
