import knex from "../../../knex/knex";
import { MutationDeleteRouteArgs } from "../../generated/graphql";

const DeleteRoute = async (_, args: MutationDeleteRouteArgs) => {
  const { id: routeToDelete } = args;

  await knex("attain_user")
    .update({ route_id: null })
    .where("route_id", routeToDelete)
    .then(() => {
      return knex("route").delete().where("id", routeToDelete);
    });

  return routeToDelete;
};

export default DeleteRoute;
