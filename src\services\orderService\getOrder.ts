import knex from "../../../knex/knex";
import axios from "axios";
import { Order } from "../../generated/graphql";
import modifyToCustomPricing from "../itemService/modifyToCustomPricing";

// TODO: Remove this function, it was moved to orderService
const getOrders = async (orders) => {
  const orderIds = orders.map((order) => order.id);
  const orderItems = await knex
    .select("*")
    .from("item")
    .join("order_item", "item.id", "order_item.item_id")
    .whereIn("order_id", orderIds)
    .orderBy("sorting_order", "asc");
  const invoices = await knex
    .select("*")
    .from("invoice")
    .whereIn("order_id", orderIds);

  const itemsByOrderId = {};
  orderItems.forEach((item) => {
    if (!itemsByOrderId[item.order_id]) {
      itemsByOrderId[item.order_id] = [];
    }
    itemsByOrderId[item.order_id].push(item);
  });

  const result = Promise.all(
    orders.map(async (order) => {
      if (order.user_id && order.id in itemsByOrderId) {
        await modifyToCustomPricing(itemsByOrderId[order.id], order.user_id);
      }
      return {
        id: order.id,
        order_number: order.order_number,
        status: order.status,
        orderItems: itemsByOrderId[order.id] || [],
        orderName: order.order_name,
        subtotal: order.subtotal,
        discount: order.discount,
        date_submitted: order.date_submitted,
        delivery_date: order.delivery_date,
        supplier: order.single_supplier,
        supplier_logo: order.logo,
        notification_number: order.notification_number,
        notification_email: order.notification_email,
        customerDetails: order.customerDetails,
        invoice:
          invoices.find((invoice) => invoice.order_id === order.id) || null,
        totalQuantity: (itemsByOrderId[order.id] || []).reduce(
          (totalQty, item) => totalQty + item.quantity,
          0
        ),
        notes: order.notes,
        config: order.config,
      };
    })
  );

  return result;
};

export default getOrders;
