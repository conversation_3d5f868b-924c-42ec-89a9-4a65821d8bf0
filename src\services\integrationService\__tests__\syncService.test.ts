import axios from "axios";
import knex from "../../../../knex/knex";
import { startSync, getSyncStatus } from "../syncService";
import { SyncStatus } from "../../../generated/graphql";

jest.mock("axios");

const mockAxios = axios as jest.Mocked<typeof axios>;

describe("syncService", () => {
  beforeEach(async () => {
    jest.clearAllMocks();
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    jest.spyOn(console, "log").mockImplementation(() => {});
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    jest.spyOn(console, "error").mockImplementation(() => {});

    await knex("sync_status").del();
    await knex("supplier")
      .insert([
        { id: 1, name: "Whitestone Foods" },
        { id: 2, name: "Invalid Supplier" },
      ])
      .onConflict("id")
      .merge();
  });

  afterEach(async () => {
    await knex("sync_status").del();
    jest.restoreAllMocks();
  });

  afterAll(async () => {
    await knex("sync_status").del();
    // await knex("supplier").del();
    await knex.destroy();
  });

  describe("startSync", () => {
    it("should return false when supplier is not found", async () => {
      const result = await startSync("999", ["items"]);

      expect(result).toBe(false);
      expect(console.error).toHaveBeenCalledWith("Supplier not found", "999");
    });

    it("should return false for invalid supplier name", async () => {
      const result = await startSync("2", ["items"]);

      expect(result).toBe(false);
      expect(console.error).toHaveBeenCalledWith(
        "Error starting sync:",
        expect.any(Error)
      );
    });

    it("should return true when sync is already in progress", async () => {
      await knex("sync_status").insert({
        supplier_id: 1,
        type: "items",
        status: SyncStatus.InProgress,
        error: null,
        start_time: new Date("2024-01-01T10:00:00Z"),
        end_time: null,
        duration_ms: null,
        last_synced: new Date("2024-01-01T10:00:00Z"),
      });

      const result = await startSync("1", ["items", "customers"]);

      expect(result).toBe(true);
      expect(console.log).toHaveBeenCalledWith(
        "Sync already in progress for:",
        ["items"],
        "supplier:",
        "1"
      );
    });

    it("should successfully start sync for Whitestone Foods", async () => {
      mockAxios.post.mockResolvedValue({ data: "success" });

      const result = await startSync("1", ["items", "customers"]);

      expect(result).toBe(true);

      const pendingRecords = await knex("sync_status").where({
        supplier_id: 1,
        status: SyncStatus.InProgress,
      });

      expect(pendingRecords).toHaveLength(2);
      expect(pendingRecords.map((r) => r.type).sort()).toEqual([
        "customers",
        "items",
      ]);
    });

    it("should handle sync request success", async () => {
      mockAxios.post.mockResolvedValue({ data: "success" });

      await startSync("1", ["items"]);

      // Wait for async operations to complete
      await new Promise((resolve) => setTimeout(resolve, 100));

      expect(mockAxios.post).toHaveBeenCalledWith(
        "https://attain-server.onrender.com/api/sync/items/whitestone"
      );

      // Verify the sync status was updated to COMPLETED
      const completedRecord = await knex("sync_status")
        .where({ supplier_id: 1, type: "items", status: SyncStatus.Completed })
        .first();

      expect(completedRecord).toBeDefined();
    });

    it("should handle sync request failure", async () => {
      const mockError = new Error("Network timeout");
      mockAxios.post.mockRejectedValue(mockError);

      await startSync("1", ["items"]);

      // Wait for async operations to complete
      await new Promise((resolve) => setTimeout(resolve, 100));

      expect(mockAxios.post).toHaveBeenCalledWith(
        "https://attain-server.onrender.com/api/sync/items/whitestone"
      );

      // Verify the sync status was updated to FAILED
      const failedRecord = await knex("sync_status")
        .where({ supplier_id: 1, type: "items", status: SyncStatus.Failed })
        .first();

      expect(failedRecord).toBeDefined();
      expect(failedRecord?.error).toBe("Network timeout");
    });

    it("should handle multiple types sequentially", async () => {
      mockAxios.post.mockResolvedValue({ data: "success" });

      await startSync("1", ["items", "customers"]);

      // Wait for async operations to complete
      await new Promise((resolve) => setTimeout(resolve, 200));

      expect(mockAxios.post).toHaveBeenCalledTimes(2);
      expect(mockAxios.post).toHaveBeenCalledWith(
        "https://attain-server.onrender.com/api/sync/items/whitestone"
      );
      expect(mockAxios.post).toHaveBeenCalledWith(
        "https://attain-server.onrender.com/api/sync/customers/whitestone"
      );

      // Verify both syncs completed
      const completedRecords = await knex("sync_status").where({
        supplier_id: 1,
        status: SyncStatus.Completed,
      });

      expect(completedRecords).toHaveLength(2);
    });

    it("should handle partial in-progress types", async () => {
      // Insert existing sync statuses
      await knex("sync_status").insert([
        {
          supplier_id: 1,
          type: "items",
          status: SyncStatus.InProgress,
          error: null,
          start_time: new Date("2024-01-01T10:00:00Z"),
          end_time: null,
          duration_ms: null,
          last_synced: new Date("2024-01-01T10:00:00Z"),
        },
        {
          supplier_id: 1,
          type: "customers",
          status: SyncStatus.Completed,
          error: null,
          start_time: new Date("2024-01-01T09:45:00Z"),
          end_time: new Date("2024-01-01T09:50:00Z"),
          duration_ms: 300000,
          last_synced: new Date("2024-01-01T09:50:00Z"),
        },
      ]);

      const result = await startSync("1", ["items", "customers", "orders"]);

      expect(result).toBe(true);
      expect(console.log).toHaveBeenCalledWith(
        "Sync already in progress for:",
        ["items"],
        "supplier:",
        "1"
      );
    });

  });

  describe("getSyncStatus", () => {
    it("should return sync status for a supplier", async () => {
      // Insert test sync status data
      await knex("sync_status").insert([
        {
          supplier_id: 1,
          type: "items",
          status: SyncStatus.Completed,
          error: null,
          start_time: new Date("2024-01-01T10:00:00Z"),
          end_time: new Date("2024-01-01T10:05:00Z"),
          duration_ms: 300000,
          last_synced: new Date("2024-01-01T10:05:00Z"),
        },
        {
          supplier_id: 1,
          type: "customers",
          status: SyncStatus.Failed,
          error: "Network error",
          start_time: new Date("2024-01-01T10:02:00Z"),
          end_time: new Date("2024-01-01T10:03:00Z"),
          duration_ms: 60000,
          last_synced: new Date("2024-01-01T10:03:00Z"),
        },
      ]);

      const result = await getSyncStatus("1");

      expect(result).toHaveLength(2);
      expect(result[0]).toMatchObject({
        type: "customers",
        status: SyncStatus.Failed,
        error: "Network error",
        durationMs: 60000,
      });
      expect(result[1]).toMatchObject({
        type: "items",
        status: SyncStatus.Completed,
        error: undefined,
        durationMs: 300000,
      });
    });

    it("should handle empty status list", async () => {
      const result = await getSyncStatus("1");
      expect(result).toEqual([]);
    });
  });
});
