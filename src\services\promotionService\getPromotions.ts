import { Knex } from "knex";
import knex from "../../../knex/knex";
import {
  Ordering,
  PaginationInput,
  PromotionFilters,
  SortBy,
  Promotion,
} from "../../generated/graphql";

export const getPromotion = async (supplierId: string, promotionId: string) => {
  const promotion = await getPromotions({
    supplierId: supplierId,
    filters: { ids: [promotionId], includeArchived: true },
  });
  if (promotion.promotions.length === 0) {
    return null;
  }
  return promotion.promotions[0];
};

export const getPromotions = async ({
  supplierId,
  filters = {},
  pagination = { offset: 0, limit: 50 },
  sortBy = { field: "created_at", ordering: Ordering.Desc },
}: {
  supplierId: string;
  filters?: PromotionFilters;
  pagination?: PaginationInput;
  sortBy?: SortBy;
}): Promise<{
  promotions: Promotion[];
  totalCount: number;
}> => {
  if (!supplierId) {
    throw new Error("Supplier ID is required");
  }

  const query = knex
    .select(
      "promotions.*",
      "promotion_types.id as type_id",
      "promotion_types.name as type_name",
      "promotion_types.code as type_code",
      "promotion_types.description as type_description",
      "promotion_types.active as type_active",
      "promotion_types.created_at as type_created_at",
      "promotion_types.updated_at as type_updated_at",
      knex.raw(`(
        SELECT COUNT(*)::integer 
        FROM promotion_usage 
        WHERE promotion_usage.promotion_id = promotions.id
      ) as usage_count`)
    )
    .from("promotions")
    .leftJoin(
      "promotion_types",
      "promotions.promotion_type_id",
      "promotion_types.id"
    )
    .where("promotions.supplier_id", supplierId);

  // Don't show archived promotions by default
  if (!filters.includeArchived) {
    query.where("promotions.archived", false);
  }

  // Apply filters
  if (filters.ids?.length) {
    query.whereIn("promotions.id", filters.ids);
  }

  if (filters.name) {
    query.where("promotions.name", "ilike", `%${filters.name}%`);
  }

  if (filters.type) {
    query.where("promotion_types.code", filters.type);
  }

  if (filters.active !== undefined) {
    query.where("promotions.active", filters.active);
  }

  if (filters.dateRange) {
    query
      .where("promotions.start_date", "<=", filters.dateRange[1])
      .where("promotions.end_date", ">=", filters.dateRange[0]);
  }

  if (filters.query) {
    query.where((builder) => {
      builder
        .where("promotions.name", "ilike", `%${filters.query}%`)
        .orWhere("promotion_types.name", "ilike", `%${filters.query}%`);
    });
  }

  if (filters.itemIds?.length) {
    query
      .leftJoin(
        "promotion_items",
        "promotions.id",
        "promotion_items.promotion_id"
      )
      .where((builder) => {
        builder
          .where("promotions.applies_to_all_items", true)
          .orWhereIn("promotion_items.item_id", filters.itemIds);
      });
  }

  if (filters.userIds?.length) {
    query
      .leftJoin(
        "promotion_users",
        "promotions.id",
        "promotion_users.promotion_id"
      )
      .where((builder) => {
        builder
          .where("promotions.applies_to_all_users", true)
          .orWhereIn("promotion_users.user_id", filters.userIds);
      });
  }

  // Get total count
  const countQuery = query
    .clone()
    .clearSelect()
    .clearOrder()
    .count("promotions.id as count")
    .first();
  const totalCount = parseInt((await countQuery).count as string, 10);

  // Apply sorting
  const allowedSortFields = [
    "id",
    "name",
    "start_date",
    "end_date",
    "created_at",
    "updated_at",
  ];
  if (allowedSortFields.includes(sortBy.field)) {
    query.orderBy(`promotions.${sortBy.field}`, sortBy.ordering);
  } else {
    query.orderBy("promotions.created_at", Ordering.Desc);
  }

  // Apply pagination
  query.offset(pagination.offset).limit(pagination.limit);

  const promotions = await query;

  // Transform raw DB results to match GraphQL types
  const populatedPromotions = await Promise.all(
    promotions.map(async (promotion) => {
      // Transform promotion type
      const promotionType = {
        id: promotion.type_id,
        name: promotion.type_name,
        code: promotion.type_code,
        description: promotion.type_description,
        active: promotion.type_active,
        created_at: promotion.type_created_at,
        updated_at: promotion.type_updated_at,
      };

      let items = undefined;
      let users = undefined;

      if (!promotion.applies_to_all_items) {
        items = await knex
          .select("item.*")
          .from("promotion_items")
          .join("item", "promotion_items.item_id", "item.id")
          .where("promotion_items.promotion_id", promotion.id);
      }

      if (!promotion.applies_to_all_users) {
        users = await knex
          .select("attain_user.*")
          .from("promotion_users")
          .join("attain_user", "promotion_users.user_id", "attain_user.id")
          .where("promotion_users.promotion_id", promotion.id);
      }

      return {
        id: promotion.id,
        name: promotion.name,
        supplier_id: promotion.supplier_id,
        promotion_type: promotionType,
        start_date: promotion.start_date,
        end_date: promotion.end_date,
        applies_to_all_items: promotion.applies_to_all_items,
        applies_to_all_users: promotion.applies_to_all_users,
        max_uses_per_customer: promotion.max_uses_per_customer,
        total_usage_limit: promotion.total_usage_limit,
        buy_quantity: promotion.buy_quantity,
        discount_amount: promotion.discount_amount,
        discount_percentage: promotion.discount_percentage,
        free_quantity: promotion.free_quantity,
        min_order_amount: promotion.min_order_amount,
        active: promotion.active,
        archived: promotion.archived,
        created_at: promotion.created_at,
        updated_at: promotion.updated_at,
        items,
        users,
        usage_count: promotion.usage_count,
      };
    })
  );

  return { promotions: populatedPromotions, totalCount };
};