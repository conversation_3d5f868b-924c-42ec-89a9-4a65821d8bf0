import { QueryCategoriesArgs } from "../../generated/graphql";
import knex from "../../../knex/knex";
const Categories = async (_, args: QueryCategoriesArgs) => {
  const { userId, supplierId } = args.getCategoriesInput;

  const categoryMap = [
    // {
    //   name: "Tobacco",
    //   value: "tobacco",
    //   image: "https://attain-app-resources-bucket.s3.amazonaws.com/tobacco.png",
    // },
    // {
    //   name: "Alcohol",
    //   value: "alcohol",
    //   image: "https://attain-app-resources-bucket.s3.amazonaws.com/alcohol.png",
    // },
    {
      name: "Snacks",
      value: "snacks",
      image: "https://attain-app-resources-bucket.s3.amazonaws.com/snacks.png",
    },
    {
      name: "Bars",
      value: "bars",
      image: "https://attain-app-resources-bucket.s3.amazonaws.com/bars.png",
    },
    {
      name: "Breakfast",
      value: "breakfast",
      image:
        "https://attain-app-resources-bucket.s3.amazonaws.com/breakfast.png",
    },
    {
      name: "Hot Meals",
      value: "hot meals",
      image:
        "https://attain-app-resources-bucket.s3.amazonaws.com/hot_meals.png",
    },

    {
      name: "Pastries",
      value: "bakery",
      image: "https://attain-app-resources-bucket.s3.amazonaws.com/pastry.png",
    },
    {
      name: "Gum",
      value: "gum & mints",
      image: "https://attain-app-resources-bucket.s3.amazonaws.com/gum.png",
    },
    {
      name: "Beverages",
      value: "beverages",
      image: "https://attain-app-resources-bucket.s3.amazonaws.com/drinks.png",
    },
    {
      name: "Health",
      value: "health & beauty",
      image: "https://attain-app-resources-bucket.s3.amazonaws.com/healthy.png",
    },
    {
      name: "Energy",
      value: "energy",
      image: "https://attain-app-resources-bucket.s3.amazonaws.com/energy.png",
    },
  ];

  let supplier;
  if (supplierId) {
    supplier = await knex("supplier")
      .where("id", supplierId)
      .select("*")
      .first();
  } else {
    supplier = await knex("supplier_times")
      .where("business_id", userId)
      .join("supplier", "supplier_times.supplier_id", "supplier.id")
      .select("supplier.*")
      .first();
  }

  const categoriesWithImages = await knex("item")
    .select("nacs_category")
    .select(knex.raw("MIN(image) as image")) // This selects the first image found for each category
    .whereNotNull("image") // Ensure the item has an image
    .andWhere("supplier", supplier.name)
    .groupBy("nacs_category");

  const categories = categoriesWithImages.map((category) => {
    if (!category.nacs_category) {
      return {
        name: "All",
        value: null,
        image: category.image,
      };
    }
    const configCategoryImage =
      supplier.config &&
      supplier.config.categories &&
      supplier.config.categories[category.nacs_category] &&
      supplier.config.categories[category.nacs_category].image;

    return {
      name: category.nacs_category,
      value: category.nacs_category,
      image: configCategoryImage ?? category.image, // Include the image in your category object
    };
  });

  if (!categories.length) {
    return [
      {
        name: "All",
        value: null,
        image: "",
      },
    ];
  }
  return categories;
};

export default Categories;
