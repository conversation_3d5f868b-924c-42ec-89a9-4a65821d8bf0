import { QueryActivityLogArgs } from "../../../generated/graphql";
import { getActivityLog } from "../../../services/accessService/activityLog";

const ActivityLog = async (_, args: QueryActivityLogArgs) => {
  const { supplierId, filters, pagination, sortBy } = args.activityLogInput;

  const result = await getActivityLog({
    supplierId,
    filters,
    pagination,
    sortBy,
  });
  return result;
};

export default ActivityLog;
