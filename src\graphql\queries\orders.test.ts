import {
  mockKnex,
  queryBuilder as queryBuilderOriginal,
} from "../../../knex/mock";
import getOrderService from "../../services/orderService/getOrder";
import OrdersQuery from "./orders";

mockKnex();
const mockedGetOrder = jest.fn((order) => ({
  id: order.id,
  name: `Order ${order.id}`,
}));
jest.mock("../../services/getOrder", () => mockedGetOrder);

describe("Orders query", () => {
  let Orders: typeof OrdersQuery;
  let getOrder: typeof getOrderService;
  let queryBuilder: typeof queryBuilderOriginal;
  beforeAll(async () => {
    ({ default: Orders } = await import("./orders"));
    ({ default: getOrder } = await import(
      "../../services/orderService/getOrder"
    ));
    queryBuilderOriginal.andWhere.mockImplementation((builderFn) => {
      builderFn.apply(queryBuilder, [queryBuilder]);
      return queryBuilder;
    });
    queryBuilderOriginal.modify.mockImplementation((filter) => {
      filter.apply(queryBuilder, [queryBuilder]);
      return queryBuilder;
    });
    queryBuilder = { ...queryBuilderOriginal };
  });
  beforeEach(() => {
    queryBuilder = { ...queryBuilderOriginal };
  });
  const getOrdersInput = {
    ids: null,
    userId: "1234",
    query: "query54321",
    status: "Submitted",
  };
  const trimmedQuery = getOrdersInput.query.trim();
  const orders = [{ id: 1 }, { id: 2 }, { id: 1234 }];

  it("returns all orders when no ids passed", async () => {
    queryBuilder.offset.mockResolvedValueOnce(orders);

    const result = await Orders(undefined, { getOrdersInput });

    expect(queryBuilder.where).toHaveBeenCalledWith(
      "user_id",
      getOrdersInput.userId
    );
    expect(queryBuilder.limit).toHaveBeenCalledWith(5);
    expect(queryBuilder.offset).toHaveBeenCalledWith(0);
    expect(queryBuilder.whereIn).not.toHaveBeenCalled();
    expect(queryBuilder.whereILike).toHaveBeenCalledWith(
      "order_name",
      `%${trimmedQuery}%`
    );
    expect(queryBuilder.orWhereILike).toHaveBeenCalledWith(
      "status",
      `%${trimmedQuery}%`
    );
    expect(queryBuilder.orWhere).toHaveBeenCalledWith(
      "id",
      parseInt(trimmedQuery.match(/\d+/)[0])
    );
    expect(queryBuilder.where).toHaveBeenCalledWith(
      "status",
      getOrdersInput.status
    );
    expect(queryBuilder.orWhere).toHaveBeenCalledWith(
      "status",
      getOrdersInput.status.toLowerCase()
    );
    expect(getOrder).toHaveBeenCalledTimes(orders.length);
    expect(result).toEqual(orders.map(mockedGetOrder));
  });

  it("returns some orders when ids passed", async () => {
    const filteredOrders = [orders[0], orders[1]];
    queryBuilder.orderBy.mockResolvedValueOnce(filteredOrders);
    const ids = ["1", "2"];

    const result = await Orders(undefined, {
      getOrdersInput: { ...getOrdersInput, ids },
    });

    expect(queryBuilder.whereIn).toHaveBeenCalledWith("order_detail.id", ids);
    expect(queryBuilder.where).toHaveBeenCalledWith(
      "user_id",
      getOrdersInput.userId
    );
    expect(queryBuilder.whereILike).toHaveBeenCalledWith(
      "order_name",
      `%${trimmedQuery}%`
    );
    expect(queryBuilder.orWhereILike).toHaveBeenCalledWith(
      "status",
      `%${trimmedQuery}%`
    );
    expect(queryBuilder.orWhere).toHaveBeenCalledWith(
      "id",
      parseInt(trimmedQuery.match(/\d+/)[0])
    );
    expect(queryBuilder.where).toHaveBeenCalledWith(
      "status",
      getOrdersInput.status
    );
    expect(queryBuilder.orWhere).toHaveBeenCalledWith(
      "status",
      getOrdersInput.status.toLowerCase()
    );
    expect(getOrder).toHaveBeenCalledTimes(filteredOrders.length);
    expect(result).toEqual(filteredOrders.map(mockedGetOrder));
  });

  it("skips id filtering when no number in query passed", async () => {
    queryBuilder.offset.mockResolvedValueOnce(orders);
    const modifiedQuery = "query";
    const modifiedTrimmedQuery = modifiedQuery.trim();

    const result = await Orders(undefined, {
      getOrdersInput: { ...getOrdersInput, query: modifiedQuery },
    });

    expect(queryBuilder.where).toHaveBeenCalledWith(
      "user_id",
      getOrdersInput.userId
    );
    expect(queryBuilder.limit).toHaveBeenCalledWith(5);
    expect(queryBuilder.offset).toHaveBeenCalledWith(0);
    expect(queryBuilder.whereIn).not.toHaveBeenCalled();
    expect(queryBuilder.whereILike).toHaveBeenCalledWith(
      "order_name",
      `%${modifiedTrimmedQuery}%`
    );
    expect(queryBuilder.orWhereILike).toHaveBeenCalledWith(
      "status",
      `%${modifiedTrimmedQuery}%`
    );
    expect(queryBuilder.where).toHaveBeenCalledWith(
      "status",
      getOrdersInput.status
    );
    expect(queryBuilder.orWhere).toHaveBeenCalledWith(
      "status",
      getOrdersInput.status.toLowerCase()
    );
    expect(queryBuilder.orWhere).toHaveBeenCalledTimes(1);
    expect(getOrder).toHaveBeenCalledTimes(orders.length);
    expect(result).toEqual(orders.map(mockedGetOrder));
  });

  it("skips query filtering if invalid query is passed", async () => {
    queryBuilder.offset.mockResolvedValueOnce(orders);

    const result = await Orders(undefined, {
      getOrdersInput: { ...getOrdersInput, query: "" },
    });

    expect(queryBuilder.where).toHaveBeenCalledWith(
      "user_id",
      getOrdersInput.userId
    );
    expect(queryBuilder.whereILike).not.toHaveBeenCalled();
    expect(queryBuilder.orWhereILike).not.toHaveBeenCalledWith();
    expect(queryBuilder.orWhere).not.toHaveBeenCalledWith(
      "id",
      parseInt(trimmedQuery.match(/\d+/)[0])
    );
    expect(queryBuilder.where).toHaveBeenCalledWith(
      "status",
      getOrdersInput.status
    );
    expect(queryBuilder.orWhere).toHaveBeenCalledWith(
      "status",
      getOrdersInput.status.toLowerCase()
    );
    expect(getOrder).toHaveBeenCalledTimes(orders.length);
    expect(result).toEqual(orders.map(mockedGetOrder));
  });

  it("skips status filtering if invalid status is passed", async () => {
    queryBuilder.offset.mockResolvedValueOnce(orders);
    const modifiedStatus = "Invalid";

    const result = await Orders(undefined, {
      getOrdersInput: { ...getOrdersInput, status: modifiedStatus },
    });

    expect(queryBuilder.where).toHaveBeenCalledWith(
      "user_id",
      getOrdersInput.userId
    );
    expect(queryBuilder.whereILike).toHaveBeenCalledWith(
      "order_name",
      `%${trimmedQuery}%`
    );
    expect(queryBuilder.orWhereILike).toHaveBeenCalledWith(
      "status",
      `%${trimmedQuery}%`
    );
    expect(queryBuilder.orWhere).toHaveBeenCalledWith(
      "id",
      parseInt(trimmedQuery.match(/\d+/)[0])
    );
    expect(queryBuilder.where).not.toHaveBeenCalledWith(
      "status",
      modifiedStatus
    );
    expect(queryBuilder.orWhere).not.toHaveBeenCalledWith(
      "status",
      modifiedStatus.toLowerCase()
    );
    expect(getOrder).toHaveBeenCalledTimes(orders.length);
    expect(result).toEqual(orders.map(mockedGetOrder));
  });

  it("skips all filtering if invalid status and query are passed", async () => {
    queryBuilder.offset.mockResolvedValueOnce(orders);
    const modifiedStatus = "Invalid";

    const result = await Orders(undefined, {
      getOrdersInput: { ...getOrdersInput, query: "", status: modifiedStatus },
    });

    expect(queryBuilder.where).toHaveBeenCalledWith(
      "user_id",
      getOrdersInput.userId
    );
    expect(queryBuilder.whereILike).not.toHaveBeenCalled();
    expect(queryBuilder.orWhereILike).not.toHaveBeenCalled();
    expect(queryBuilder.orWhere).not.toHaveBeenCalledWith(
      "id",
      parseInt(trimmedQuery.match(/\d+/)[0])
    );
    expect(queryBuilder.where).not.toHaveBeenCalledWith(
      "status",
      modifiedStatus
    );
    expect(queryBuilder.orWhere).not.toHaveBeenCalledWith(
      "status",
      modifiedStatus.toLowerCase()
    );
    expect(getOrder).toHaveBeenCalledTimes(orders.length);
    expect(result).toEqual(orders.map(mockedGetOrder));
  });
});
