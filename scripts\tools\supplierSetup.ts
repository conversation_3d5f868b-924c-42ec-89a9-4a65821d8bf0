import { createSupplier } from "../../src/services/supplierService/supplierService";
import { createEmployee } from "../../src/services/accessService/createEmployee";

const setupSupplier = async () => {
  const supplier = await createSupplier({
    name: "Test Supplier",
    email: "<EMAIL>",
    phone_number: "**********",
    password: "password",
  });

  const supplierId = supplier.id;

  const adminAccount = await createEmployee({
    name: "Admin Account",
    email: "<EMAIL>",
    password: "trt-attain-444",
    supplierId,
    phone: "**********",
    appAccess: true,
    dashboardAccess: true,
    roleIds: ["1"],
  });

  console.log("Supplier created:", supplier);
  console.log("Admin account created:", adminAccount);
};
