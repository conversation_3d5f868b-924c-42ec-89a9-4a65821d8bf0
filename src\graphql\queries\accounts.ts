import knex from "../../../knex/knex";
import axios from "axios";
import { Account, QueryAccountsArgs } from "../../generated/graphql";

const Accounts = async (_, args: QueryAccountsArgs) => {
  const { businessId } = args;
  const user = await knex("attain_user").select("*").where("id", businessId);
  const buyerId = user[0].buyer_id;
  if (!buyerId) {
    return [];
  }
  const url = process.env.BALANCE_API_LINK + "/buyers/" + buyerId;
  let data = undefined;
  try {
    const result = await axios.get(url, {
      headers: {
        accept: "application/json",
        "x-balance-key": process.env.BALANCE_API_KEY,
      },
    });
    data = result.data;
  } catch (error) {
    console.log(error);
  }
  if (!data) {
    return [];
  }

  const banks = data.paymentData.banks;
  const creditCards = data.paymentData.creditCards;
  const accounts = await knex
    .select("*")
    .from("account")
    .whereIn(
      "external_id",
      banks.map((bank) => bank.id).concat(creditCards.map((cc) => cc.id))
    )
    .where("is_active", true)
    .orderBy("id", "asc");

  const res: Account[] = [];
  for (let i = 0; i < accounts.length; i++) {
    const cur = accounts[i];
    if (cur.type === "cc") {
      cur["balanceCreditCard"] = creditCards.find(
        (cc) => cc.id === cur.external_id
      );
    }
    if (cur.type === "bank") {
      cur["balanceBankAccount"] = banks.find(
        (bank) => bank.id === cur.external_id
      );
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { external_id, is_active, ...account } = cur;
    res.push(account);
  }
  if (data) {
    return res;
  }
};

export default Accounts;
// export {Orders, UserOrders}
