import { queryBuilder } from "../../../knex/mock";
import modifyToCustomPricing from "./modifyToCustomPricing";
import { CartItem } from "../../generated/graphql";

const knex = jest.requireActual("../../../knex/knex").default;

jest.mock("../../../knex/knex", () => ({
  __esModule: true,
  default: jest.fn().mockReturnValue(queryBuilder),
}));

describe("modifyToCustomPricing (current behavior)", () => {
  const userId = "user-123";

  afterAll(async () => {
    await knex.destroy();
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("updates price and discounted_price for matching CartItem entries", async () => {
    const items: CartItem[] = [
      {
        item_id: "a",
        name: "A",
        price: 100,
        discounted_price: 90,
        id: "1",
        quantity: 1,
      },
      {
        item_id: "b",
        name: "B",
        price: 200,
        discounted_price: null,
        id: "2",
        quantity: 1,
      },
    ];

    const customPrices = [
      { item_id: "a", price: 80 },
      { item_id: "b", price: 150 },
    ];

    queryBuilder.where.mockResolvedValueOnce(customPrices);
    await modifyToCustomPricing(items, userId);

    expect(items[0].price).toBe(80);
    expect(items[0].discounted_price).toBe(80);

    expect(items[1].price).toBe(150);
    expect(items[1].discounted_price).toBe(null); // unchanged
  });

  it("does not modify CartItem if no custom price exists", async () => {
    const items: CartItem[] = [
      {
        item_id: "c",
        name: "C",
        price: 300,
        discounted_price: null,
        id: "3",
        quantity: 1,
      },
    ];

    const customPrices = [{ item_id: "a", price: 80 }];

    queryBuilder.where.mockResolvedValueOnce(customPrices);
    await modifyToCustomPricing(items, userId);

    expect(items[0].price).toBe(300);
    expect(items[0].discounted_price).toBe(null);
  });

  it("uses first matching custom price when duplicates exist", async () => {
    const items: CartItem[] = [
      {
        item_id: "x",
        name: "X",
        price: 500,
        discounted_price: 450,
        id: "4",
        quantity: 1,
      },
    ];

    const customPrices = [
      { item_id: "x", price: 400 },
      { item_id: "x", price: 350 },
    ];

    queryBuilder.where.mockResolvedValueOnce(customPrices);
    await modifyToCustomPricing(items, userId);

    expect(items[0].price).toBe(400);
    expect(items[0].discounted_price).toBe(400);
  });

  it("does nothing when customPrices is empty", async () => {
    const items: CartItem[] = [
      {
        item_id: "y",
        name: "Y",
        price: 100,
        discounted_price: 90,
        id: "5",
        quantity: 1,
      },
    ];

    queryBuilder.where.mockResolvedValueOnce([]);
    await modifyToCustomPricing(items, userId);

    expect(items[0].price).toBe(100);
    expect(items[0].discounted_price).toBe(90);
  });
});
