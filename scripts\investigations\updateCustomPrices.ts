import knex from "../../knex/knex";
import fs from "fs/promises";
import path from "path";

/*

Some tools for managing all custom prices for an item. This is mainly used when
something goes wrong with the custom price records for an item.

The general process is:
1. Export the custom prices for an item from backup
2. Delete the custom prices for an item from the database
3. Restore the custom prices for an item to the database from the backup
*/

const BACKUP_FILE = path.join(__dirname, "custom_prices_backup.json");

const updateCustomPrices = async (item_id: number, price: number) => {
  try {
    // Begin transaction
    await knex.transaction(async (trx) => {
      const updatedRows = await trx("custom_item_price")
        .where("item_id", "=", item_id)
        .update({ price: price });

      console.log(`Updated ${updatedRows} custom price records to ${price}`);
    });

    console.log("Transaction completed successfully");
  } catch (error) {
    console.error("Error updating custom prices:", error);
  }
};

const deleteCustomPrices = async (item_id: number) => {
  try {
    // Begin transaction
    await knex.transaction(async (trx) => {
      // First query
      const distIds = await knex("attain_user")
        .select("id")
        .where("route_id", "like", "%62%");

      // Second query using the results
      const customPrices = await knex("custom_item_price")
        .whereIn(
          "user_id",
          distIds.map((row) => row.id)
        )
        .delete();

      console.log(`Deleted ${customPrices} custom price records`);
    });

    console.log("Delete transaction completed successfully");
  } catch (error) {
    console.error("Error deleting custom prices:", error);
  }
};

const exportCustomPrices = async (item_id: number) => {
  try {
    // First query
    const distIds = await knex("attain_user")
      .select("id")
      .where("route_id", "like", "%62%");

    // Second query using the results
    const customPrices = await knex("custom_item_price")
      .whereIn(
        "user_id",
        distIds.map((row) => row.id)
      )
      .select("*");

    await fs.writeFile(BACKUP_FILE, JSON.stringify(customPrices, null, 2));
    console.log(
      `Exported ${customPrices.length} custom price records to ${BACKUP_FILE}`
    );
  } catch (error) {
    console.error("Error exporting custom prices:", error);
  }
};

const restoreCustomPrices = async () => {
  try {
    const fileContent = await fs.readFile(BACKUP_FILE, "utf-8");
    const rows = JSON.parse(fileContent);

    await knex.transaction(async (trx) => {
      for (const row of rows) {
        await trx("custom_item_price").insert(row);
      }
      console.log(
        `Restored ${rows.length} custom price records from ${BACKUP_FILE}`
      );
    });

    console.log("Restore transaction completed successfully");
  } catch (error) {
    console.error("Error restoring custom prices:", error);
  }
};

// // Execute the scripts in sequence
// const runOperations = async () => {
//   const item_id = 393488;
//   // await exportCustomPrices(item_id); //k First backup the data
//   // await deleteCustomPrices(item_id); // Then delete the records
//   await restoreCustomPrices(); // Uncomment to restore the data
// };

// runOperations()
//   .then(() => process.exit(0))
//   .catch((error) => {
//     console.error("Script failed:", error);
//     process.exit(1);
//   });
