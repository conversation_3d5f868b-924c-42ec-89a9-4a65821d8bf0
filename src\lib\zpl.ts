import { ZplImage } from "zpl-image";

class ZPL {
  private totalVerticalOffset: number;
  private zplContents: string;
  private pageHorizontalPadding: number;
  private heightScale: number;
  private widthScale: number;

  constructor(
    initialVerticalOffset = 0,
    initialContents = "",
    overrideHeightScale = 1,
    overrideWidthScale = 1,
    overridePageHorizontalPadding = 40
  ) {
    this.totalVerticalOffset = initialVerticalOffset;
    this.zplContents = "^XA\n";
    this.zplContents += initialContents;
    this.pageHorizontalPadding = overridePageHorizontalPadding;
    this.heightScale = overrideHeightScale;
    this.widthScale = overrideWidthScale;
  }

  text = (
    content: string,
    lineHeight = 25,
    customFontAndSize?: string,
    verticalOffset = 0,
    horizontalOffset = 0
  ) => {
    this.totalVerticalOffset += verticalOffset;
    let zplContent = "";
    zplContent += `${customFontAndSize ? "^A" + customFontAndSize + "\n" : ""}`;
    zplContent += `^FO${Math.floor(
      (horizontalOffset + this.pageHorizontalPadding) * this.widthScale
    )},${this.totalVerticalOffset}`;
    zplContent += `^FD${content}^FS\n`;
    this.totalVerticalOffset += lineHeight * this.heightScale;
    this.zplContents += zplContent;
  };
  changeDefaultFontAndSize = (font = "D", size = 20) => {
    this.zplContents += `^CF${font},${size * this.heightScale}\n`;
  };
  barcode = (
    content: string,
    verticalOffset = 0,
    horizontalOffset = 0,
    height = 50,
    lineHeight = 100,
    orientation = "N",
    type = "BU"
  ) => {
    this.totalVerticalOffset += verticalOffset * this.heightScale;
    let zplContent = "";
    zplContent += `^FO${Math.floor(
      (horizontalOffset + this.pageHorizontalPadding) * this.widthScale
    )},${this.totalVerticalOffset}`;
    zplContent += `^${type}${orientation},${height}`;
    zplContent += `^FD${content}^FS\n`;
    this.totalVerticalOffset += lineHeight * this.heightScale;
    this.zplContents += zplContent;
  };
  image = (z64: ZplImage, verticalOffset = 0, horizontalOffset = 0) => {
    this.totalVerticalOffset += verticalOffset * this.heightScale;
    let zplContent = "";
    zplContent += `^FO${Math.floor(
      (horizontalOffset + this.pageHorizontalPadding) * this.widthScale
    )},${this.totalVerticalOffset}`;
    zplContent += `^GFA,${z64.length},${z64.length},${z64.rowlen},${z64.z64}\n`;
    this.zplContents += zplContent;
  };

  rawZPL = (content: string) => {
    this.zplContents += content;
  };

  getZPLContents = () => {
    return this.zplContents
      .replace("^XA\n", `^XA\n^MNN\n^LL${this.totalVerticalOffset}`)
      .concat("^XZ");
  };

  addVerticalOffset = (offset: number) => {
    this.totalVerticalOffset += offset * this.heightScale;
  };
}

export default ZPL;
