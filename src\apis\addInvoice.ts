import { Request, Response } from "express";
import RestEndpoint from "./_restEndpoint";
import { Mutation } from "../generated/graphql";

export default class AddInvoice extends RestEndpoint {
  public async handler(req: Request, res: Response) {
    const invoiceData = req.body;
    const dateReceived = new Date().getTime();
    try {
      switch (invoiceData.supplier) {
        case "coremark":
          await this.worker(
            this.processCoremarkInvoice(invoiceData),
            dateReceived
          );
          break;
        case "hla":
          await this.worker(this.processHlaInvoice(invoiceData), dateReceived);
          break;
        default:
          throw new SupplierNotSupportedError(invoiceData.supplier);
      }
      res.status(200).send("OK");
    } catch (err) {
      console.log(err);
      if (err instanceof SupplierNotSupportedError) {
        res.status(400).send(err.message);
      } else if (err instanceof InvoiceProcessingError) {
        res.status(400).send(err.message);
      } else {
        res.status(500).send("Error processing invoice.");
      }
    }
  }

  protected async worker(sendData, dateReceived: number) {
    if (!sendData.items || sendData.items.length === 0) {
      throw new InvoiceProcessingError(
        sendData.supplier_id,
        "No items to process."
      );
    }

    const variables = {
      addInvoiceInput: {
        order_id: sendData.order_id,
        supplier_id: sendData.supplier_id,
        date_received: new Date(dateReceived),
        invoice_id: sendData.invoice_id,
        total: sendData.total,
        invoice_items: sendData.items,
      },
    };

    const query = /* GraphQL */ `
      mutation AddInvoice($addInvoiceInput: AddInvoiceInput!) {
        addInvoice(addInvoiceInput: $addInvoiceInput) {
          id
          order_id
          total
          date_received
          supplier_id
          invoice_id
        }
      }
    `;

    await this.apolloHttpPost(query, variables).then(
      ({ addInvoice }: Mutation) =>
        console.log(
          `AddInvoice(order_id: ${sendData.order_id}) returned:`,
          addInvoice
        )
    );
  }

  private processCoremarkInvoice(data) {
    const items = data.items.map((item) => ({
      name: item.description,
      quantity: item.quantityShipped,
      upc1: item.upc,
      upc2: item.upc2,
      price: item.price,
      unit_size: item.unitSize.toString(),
    }));
    return {
      order_id: data.order_id,
      supplier_id: data.supplier_id,
      invoice_id: data.invoice_id,
      total: data.total,
      items,
    };
  }

  private processHlaInvoice(data) {
    const items = data.items.map((item) => ({
      name: item.ItemName,
      quantity: item.TSAQUANTITY,
      upc1: item.ItemTSAUPCCode1,
      upc2: item.ItemTSAUPCCode2,
      price: item.TSACost,
      unit_size: item.TSASplitPackUnit,
    }));

    return {
      order_id: data.order_id,
      supplier_id: data.supplier_id,
      invoice_id: data.invoice_id,
      total: data.total,
      items,
    };
  }
}

export class SupplierNotSupportedError extends Error {
  supplier: string;
  constructor(supplier: string) {
    super(`Supplier ${supplier} not supported.`);
    this.name = "SupplierNotSupported";
    this.supplier = supplier;
  }
}

export class InvoiceProcessingError extends Error {
  supplier: string | number;
  constructor(supplier: string | number, error: string) {
    super(`Error processing invoice from supplier ${supplier}: ${error}.`);
    this.name = "InvoiceProcessingError";
    this.supplier = supplier;
  }
}
