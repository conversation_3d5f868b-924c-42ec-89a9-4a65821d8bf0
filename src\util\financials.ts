import { Order } from "../generated/graphql";

export const orderItemsSubtotal = (orderItems): number => {
  const subtotal = orderItems.reduce(
    (acc, item) => acc + item.price_purchased_at * item.quantity,
    0.0
  );
  return Math.round(subtotal * 100) / 100; // round to 2 decimal places
};

export const orderSubtotal = (order: Order): number => {
  return orderItemsSubtotal(order.orderItems);
};

export const orderTotal = (order: Order): number => {
  return orderSubtotal(order) - order.discount;
};
