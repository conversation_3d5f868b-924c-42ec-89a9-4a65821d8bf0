import knex from "../../../knex/knex";
import { MutationUpsertCategoryArgs } from "../../generated/graphql";

const UpsertCategory = async (_, args: MutationUpsertCategoryArgs) => {
  const {
    skipItems,
    categoryInput: { items: itemsToUpsert, ...categoryToUpsert },
  } = args;

  const [category] = await knex("category")
    .insert(categoryToUpsert)
    .onConflict("id")
    .merge()
    .returning("*");

  if (skipItems) return category;

  if (categoryToUpsert.id) {
    await knex("category_item")
      .delete()
      .where("category_id", categoryToUpsert.id)
      .andWhere(
        "item_id",
        "not in",
        itemsToUpsert.map((item) => item.id)
      );
  }

  const itemIds = itemsToUpsert.length
    ? await knex("category_item")
        .insert(
          itemsToUpsert.map((item) => ({
            category_id: category.id,
            item_id: item.id,
          }))
        )
        .onConflict("category_id_item_id_unique")
        .merge()
        .returning("item_id")
    : [];

  const items = await knex("item")
    .select("*")
    .whereIn(
      "id",
      itemIds.map((item) => item.item_id)
    );
  category.items = items;

  return category;
};

export default UpsertCategory;
