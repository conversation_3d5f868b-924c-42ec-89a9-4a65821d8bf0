"use strict";

exports.up = function (db) {
  return db
    .createTable("promotion_types", {
      id: { type: "int", primaryKey: true, autoIncrement: true },
      name: { type: "string", notNull: true },
      code: { type: "string", notNull: true, unique: true },
      description: { type: "string" },
      active: { type: "boolean", notNull: true, defaultValue: true },
      created_at: {
        type: "timestamp",
        notNull: true,
        defaultValue: new String("NOW()"),
      },
      updated_at: {
        type: "timestamp",
        notNull: true,
        defaultValue: new String("NOW()"),
      },
    })
    .then(() =>
      db.runSql(`
    INSERT INTO promotion_types (name, code, description) VALUES
    ('Buy X Get Price', 'BUY_X_GET_PRICE', 'Buy a specific quantity and get a special price'),
    ('Buy X Get Discount', 'BUY_X_GET_DISCOUNT', 'Buy a specific quantity and get a percentage discount'),
    ('Buy X Get Y Free', 'BUY_X_GET_Y_FREE', 'Buy a specific quantity and get additional items free'),
    ('Fixed Discount', 'FIXED_DISCOUNT', 'Get a fixed amount off the total'),
    ('Percentage Off', 'PERCENTAGE_OFF', 'Get a percentage off the total')
  `)
    )
    .then(() =>
      db.createTable("promotions", {
        id: { type: "int", primaryKey: true, autoIncrement: true },
        name: { type: "string", notNull: true },
        supplier_id: { type: "int", notNull: true },
        promotion_type_id: { type: "int", notNull: true },
        start_date: { type: "timestamp", notNull: true },
        end_date: { type: "timestamp", notNull: true },
        applies_to_all_items: {
          type: "boolean",
          notNull: true,
          defaultValue: false,
        },
        applies_to_all_users: {
          type: "boolean",
          notNull: true,
          defaultValue: false,
        },
        max_uses_per_customer: { type: "int" },
        total_usage_limit: { type: "int" },
        // Fields for different promotion types
        buy_quantity: { type: "int" }, // For BUY_X type promotions
        discount_amount: { type: "decimal" }, // For fixed price/amount off
        discount_percentage: { type: "decimal" }, // For percentage discounts
        free_quantity: { type: "int" }, // For BUY_X_GET_Y_FREE
        min_order_amount: { type: "decimal" }, // Optional minimum order amount
        active: { type: "boolean", notNull: true, defaultValue: false },
        archived: { type: "boolean", notNull: true, defaultValue: false },
        created_at: {
          type: "timestamp",
          notNull: true,
          defaultValue: new String("NOW()"),
        },
        updated_at: {
          type: "timestamp",
          notNull: true,
          defaultValue: new String("NOW()"),
        },
      })
    )
    .then(() =>
      db.createTable("promotion_items", {
        promotion_id: { type: "int", notNull: true },
        item_id: { type: "int", notNull: true },
        created_at: {
          type: "timestamp",
          notNull: true,
          defaultValue: new String("NOW()"),
        },
      })
    )
    .then(() =>
      db.createTable("promotion_users", {
        promotion_id: { type: "int", notNull: true },
        user_id: { type: "int", notNull: true },
        created_at: {
          type: "timestamp",
          notNull: true,
          defaultValue: new String("NOW()"),
        },
      })
    )
    .then(() =>
      db.createTable("promotion_usage", {
        id: { type: "int", primaryKey: true, autoIncrement: true },
        promotion_id: { type: "int", notNull: true },
        user_id: { type: "int", notNull: true },
        order_id: { type: "int", notNull: true },
        used_at: {
          type: "timestamp",
          notNull: true,
          defaultValue: new String("NOW()"),
        },
        discount_amount: { type: "decimal", notNull: true }, // Actual discount applied
        created_at: {
          type: "timestamp",
          notNull: true,
          defaultValue: new String("NOW()"),
        },
      })
    )
    .then(() =>
      db.addIndex(
        "promotion_items",
        "promotion_items_idx",
        ["promotion_id", "item_id"],
        true
      )
    )
    .then(() =>
      db.addIndex(
        "promotion_users",
        "promotion_users_idx",
        ["promotion_id", "user_id"],
        true
      )
    )
    .then(() =>
      db.addIndex("promotion_usage", "promotion_usage_user_idx", [
        "promotion_id",
        "user_id",
      ])
    )
    .then(() =>
      db.addIndex("promotions", "promotions_date_idx", [
        "start_date",
        "end_date",
      ])
    )
    .then(() =>
      db.addIndex("promotions", "promotions_supplier_idx", ["supplier_id"])
    )
    .then(() =>
      db.addIndex("promotion_types", "promotion_types_code_idx", ["code"], true)
    )
    .then(() =>
      db.addForeignKey(
        "promotions",
        "promotion_types",
        "promotions_type_fk",
        { promotion_type_id: "id" },
        { onDelete: "RESTRICT", onUpdate: "CASCADE" }
      )
    )
    .then(() =>
      db.addForeignKey(
        "promotions",
        "supplier",
        "promotions_supplier_fk",
        { supplier_id: "id" },
        { onDelete: "CASCADE", onUpdate: "CASCADE" }
      )
    )
    .then(() =>
      db.addForeignKey(
        "promotion_items",
        "promotions",
        "promotion_items_promotion_fk",
        { promotion_id: "id" },
        { onDelete: "CASCADE", onUpdate: "CASCADE" }
      )
    )
    .then(() =>
      db.addForeignKey(
        "promotion_items",
        "item",
        "promotion_items_item_fk",
        { item_id: "id" },
        { onDelete: "CASCADE", onUpdate: "CASCADE" }
      )
    )
    .then(() =>
      db.addForeignKey(
        "promotion_users",
        "promotions",
        "promotion_users_promotion_fk",
        { promotion_id: "id" },
        { onDelete: "CASCADE", onUpdate: "CASCADE" }
      )
    )
    .then(() =>
      db.addForeignKey(
        "promotion_users",
        "attain_user",
        "promotion_users_user_fk",
        { user_id: "id" },
        { onDelete: "CASCADE", onUpdate: "CASCADE" }
      )
    )
    .then(() =>
      db.addForeignKey(
        "promotion_usage",
        "promotions",
        "promotion_usage_promotion_fk",
        { promotion_id: "id" },
        { onDelete: "CASCADE", onUpdate: "CASCADE" }
      )
    )
    .then(() =>
      db.addForeignKey(
        "promotion_usage",
        "attain_user",
        "promotion_usage_user_fk",
        { user_id: "id" },
        { onDelete: "CASCADE", onUpdate: "CASCADE" }
      )
    )
    .then(() =>
      db.addForeignKey(
        "promotion_usage",
        "order_detail",
        "promotion_usage_order_fk",
        { order_id: "id" },
        { onDelete: "CASCADE", onUpdate: "CASCADE" }
      )
    );
};

exports.down = function (db) {
  return db
    .dropTable("promotion_usage")
    .then(() => db.dropTable("promotion_items"))
    .then(() => db.dropTable("promotion_users"))
    .then(() => db.dropTable("promotions"))
    .then(() => db.dropTable("promotion_types"));
};
