import knex from "../../../knex/knex";

interface SalesRepInfo {
  id: number | null;
  name: string;
  email: string | null;
  source: "employee" | "route_driver" | "unassigned";
}

export const addSalesRepToOrders = async (orders: any[]) => {
  console.log("addSalesRepToOrders", orders);

  if (!orders.length) return [];

  const userIds = [
    ...new Set(orders.map((order) => order.customerDetails.id)),
  ].filter((id) => id != null && id !== undefined);

  if (!userIds.length) {
    return orders.map((order) => ({
      ...order,
      sales_rep: {
        id: null,
        name: "",
        email: null,
        source: "unassigned",
      },
    }));
  }

  const salesRepLookup = await knex
    .select([
      "au.id as user_id",
      "au.name as customer_name",
      "au.route_id as old_route_field",
      "au.primary_route_id",
      "r.id as route_id",
      "r.name as route_name",
      "r.driver as route_driver",
      "e.id as employee_id",
      "e.name as employee_name",
      "e.email as employee_email",
      "e.archived as employee_archived",
    ])
    .from("attain_user as au")
    .leftJoin(
      knex.raw(`route as r ON (
      r.id = au.primary_route_id 
      OR r.id::text = ANY(string_to_array(au.route_id, ','))
    )`)
    )
    .leftJoin("route_assignment as ra", "r.id", "ra.route_id")
    .leftJoin("employees as e", function () {
      this.on("ra.employee_id", "e.id").andOn("e.archived", knex.raw("false"));
    })
    .whereIn("au.id", userIds);

  const userSalesRepMap = new Map();

  salesRepLookup.forEach((row) => {
    if (!userSalesRepMap.has(row.user_id)) {
      let salesRep: SalesRepInfo;

      if (row.employee_id && row.employee_name && !row.employee_archived) {
        salesRep = {
          id: row.employee_id,
          name: row.employee_name,
          email: row.employee_email,
          source: "employee",
        };
      } else if (row.route_driver) {
        salesRep = {
          id: null,
          name: row.route_driver,
          email: null,
          source: "route_driver",
        };
      } else {
        salesRep = {
          id: null,
          name: "",
          email: null,
          source: "unassigned",
        };
        console.log(`DEBUG: User ${row.user_id} -> Unassigned`);
      }
      userSalesRepMap.set(row.user_id, salesRep);
    }
  });

  userIds.forEach((userId) => {
    if (!userSalesRepMap.has(userId)) {
      userSalesRepMap.set(userId, {
        id: null,
        name: "",
        email: null,
        source: "unassigned",
      });
    }
  });

  return orders.map((order) => {
    if (!order.customerDetails.id) {
      return {
        ...order,
        sales_rep: {
          id: null,
          name: "",
          email: null,
          source: "unassigned",
        },
      };
    }

    const salesRep = userSalesRepMap.get(order.customerDetails.id) || {
      id: null,
      name: "Unknown User",
      email: null,
      source: "unassigned",
    };

    return {
      ...order,
      sales_rep: salesRep,
    };
  });
};

export default {
  addSalesRepToOrders,
};
