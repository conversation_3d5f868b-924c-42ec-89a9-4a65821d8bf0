import {
  getApplicablePromotions,
  calculatePromotionDiscount,
} from "../../services/promotionService";
import { QueryCalculatePromotionsArgs } from "../../generated/graphql";

const calculatePromotions = async (
  _,
  { supplier_id, user_id, items }: QueryCalculatePromotionsArgs
) => {
  // Calculate order subtotal
  const orderSubtotal = items.reduce(
    (sum, item) => sum + item.price * item.quantity,
    0
  );

  // Get applicable promotions
  const promotions = await getApplicablePromotions(supplier_id, user_id, items);

  console.log("promotions", promotions);

  // Calculate discounts for each applicable promotion
  const promotionResults = await Promise.all(
    promotions.map(async (promotion) => {
      const result = await calculatePromotionDiscount(
        supplier_id,
        promotion.id,
        items,
        orderSubtotal
      );
      return {
        promotion,
        ...result,
      };
    })
  );

  // Filter out promotions that didn't result in any discount
  const validPromotions = promotionResults.filter(
    (result) => result.discountAmount > 0
  );

  // Calculate total discount and collect affected items
  const total_discount = validPromotions.reduce(
    (sum, result) => sum + result.discountAmount,
    0
  );

  // Transform affected items to include promotion IDs
  const affected_items = validPromotions.flatMap((result) =>
    result.affectedItems.map((item) => ({
      item_id: item.item_id,
      promotion_id: result.promotion.id,
      discount_amount: item.discountAmount,
    }))
  );

  return {
    total_discount,
    affected_items,
    applied_promotions: validPromotions.map((result) => result.promotion),
  };
};

export default calculatePromotions;
