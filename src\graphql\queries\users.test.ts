import { mockKnex, queryBuilder } from "../../../knex/mock";
import { User } from "../../generated/graphql";
import UsersQuery from "./users";

mockKnex();

describe("Users query", () => {
  let Users: typeof UsersQuery;
  beforeAll(async () => {
    ({ default: Users } = await import("./users"));
  });
  const mockedUser = { id: 1 };
  const mockedSupplier = { id: 2, name: "mockedSupplier" };

  it("returns all users when no ids passed", async () => {
    queryBuilder.from.mockResolvedValueOnce([]);
    await Users(undefined, { getUsersInput: { ids: null } });
  });

  it("returns some users when ids passed", async () => {
    queryBuilder.whereIn.mockResolvedValueOnce([]);
    await Users(undefined, {
      getUsersInput: { ids: [mockedUser.id.toString()] },
    });
  });

  it("uses default list of suppliers when no specific user suppliers found", async () => {
    queryBuilder.whereIn.mockResolvedValueOnce([mockedUser]);
    queryBuilder.orderBy.mockResolvedValueOnce([]);
    queryBuilder.whereIn.mockResolvedValueOnce([]);
    const defaultSupplierIds = [15, 12, 21, 22, 23, 24, 25];

    await Users(undefined, {
      getUsersInput: { ids: [mockedUser.id.toString()] },
    });

    expect(queryBuilder.whereIn).toHaveBeenLastCalledWith(
      "id",
      defaultSupplierIds
    );
  });

  it("uses combined list of suppliers when user suppliers found", async () => {
    queryBuilder.whereIn.mockResolvedValueOnce([mockedUser]);
    queryBuilder.orderBy.mockResolvedValueOnce([mockedSupplier]);
    const additionalSupplierIds = [12, 21, 22, 23, 24, 25];
    queryBuilder.whereIn.mockResolvedValueOnce(
      additionalSupplierIds.map((id) => ({ id }))
    );

    const users: User[] = await Users(undefined, {
      getUsersInput: { ids: [mockedUser.id.toString()] },
    });

    expect(queryBuilder.whereIn).toHaveBeenLastCalledWith(
      "id",
      additionalSupplierIds
    );
    expect(users[0].suppliers.length).toEqual(1 + additionalSupplierIds.length);
  });
});
