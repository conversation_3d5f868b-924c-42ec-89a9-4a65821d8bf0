import { queryBuilder } from "../../../knex/mock";

const knexMock = jest.fn(() => queryBuilder);
Object.assign(knexMock, queryBuilder);

(knexMock as any).fn = {};

jest.mock("../../../knex/knex", () => ({
  __esModule: true,
  default: knexMock,
}));

describe("getCart", () => {
  let getCart: typeof import("./getCart").default;

  beforeAll(async () => {
    getCart = (await import("./getCart")).default;
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("returns cart with grouped subCarts by supplier", async () => {
    const mockCartItems = [
      { id: 1, name: "Item 1", supplier: "Supplier A", item_id: "item-1" },
      { id: 2, name: "Item 2", supplier: "Supplier B", item_id: "item-2" },
      { id: 3, name: "Item 3", supplier: "Supplier A", item_id: "item-3" },
    ];
    const mockUser = { user_id: "user-123" };

    // Mock the resolution of the entire query chain.
    queryBuilder.then.mockImplementationOnce((onfulfilled) =>
      onfulfilled(mockCartItems)
    );

    queryBuilder.first.mockResolvedValueOnce(mockUser);

    const result = await getCart("cart-123");

    expect(result.userId).toBe("user-123");
    expect(result.cartItems.length).toBe(3);
    expect(result.subCarts).toHaveLength(2);

    const supplierA = result.subCarts.find((s) => s.supplier === "Supplier A");
    const supplierB = result.subCarts.find((s) => s.supplier === "Supplier B");

    expect(supplierA?.cartItems).toHaveLength(2);
    expect(supplierB?.cartItems).toHaveLength(1);
  });

  it("returns empty cartItems and subCarts if no items found", async () => {
    // Mock the item query to resolve with an empty array.
    queryBuilder.then.mockImplementationOnce((onfulfilled) => onfulfilled([]));
    queryBuilder.first.mockResolvedValueOnce({ user_id: "user-999" });

    const result = await getCart("empty-cart");

    expect(result.userId).toBe("user-999");
    expect(result.cartItems).toEqual([]);
    expect(result.subCarts).toEqual([]);
  });

  it("handles missing user_id gracefully", async () => {
    // Mock the item query to resolve with one item.
    queryBuilder.then.mockImplementationOnce((onfulfilled) =>
      onfulfilled([{ id: 1, supplier: "S1", item_id: "i1" }])
    );
    //

    queryBuilder.first.mockResolvedValueOnce({});

    const result = await getCart("cart-without-user");

    expect(result.userId).toBeUndefined();
    expect(result.cartItems.length).toBe(1);
    expect(result.subCarts).toHaveLength(1);
  });

  it("throws if knex throws (DB error)", async () => {
    const dbError = new Error("DB connection failed");

    // Mock the query chain to reject instead of resolve.
    queryBuilder.then.mockImplementationOnce((_, onrejected) =>
      onrejected(dbError)
    );

    await expect(getCart("crash-cart")).rejects.toThrow("DB connection failed");
  });

  it("groups correctly when all items are from one supplier", async () => {
    const mockCartItems = [
      { id: 1, supplier: "SameSupplier", item_id: "item-1" },
      { id: 2, supplier: "SameSupplier", item_id: "item-2" },
    ];

    // Mock the two separate queries correctly.
    queryBuilder.then.mockImplementationOnce((onfulfilled) =>
      onfulfilled(mockCartItems)
    );
    queryBuilder.first.mockResolvedValueOnce({ user_id: "user-1" });

    const result = await getCart("single-supplier-cart");

    expect(result.subCarts).toHaveLength(1);
    expect(result.subCarts[0].supplier).toBe("SameSupplier");
    expect(result.subCarts[0].cartItems?.length).toBe(2);
  });
});
