import knex from "../../../knex/knex";

/**
 * Toggles an item's favorite status for a user.
 * If the item is already a favorite, it is removed. If not, it is added.
 * @param userId - The ID of the user performing the action.
 * @param itemId - The ID of the item to be favorited or unfavorited.
 * @param employeeId - An optional employee ID.
 * @returns A promise that resolves to an object indicating the outcome and the new favorite status.
 */
export const toggleFavoriteItem = async (
  userId: number,
  itemId: number,
  employeeId?: number
): Promise<{ success: boolean; isFavorited: boolean; message?: string }> => {
  try {
    const effectiveEmployeeId = null;

    const existingFavorite = await knex("user_item_favorites")
      .where({
        user_id: userId,
        item_id: itemId,
        employee_id: effectiveEmployeeId,
      })
      .first();

    if (existingFavorite) {
      // Remove favorite
      await knex("user_item_favorites")
        .where({ id: existingFavorite.id })
        .delete();

      return { success: true, isFavorited: false };
    } else {
      // Add favorite
      await knex("user_item_favorites").insert({
        user_id: userId,
        item_id: itemId,
        employee_id: effectiveEmployeeId,
      });

      return { success: true, isFavorited: true };
    }
  } catch (error) {
    console.error("Error toggling favorite:", error);
    return {
      success: false,
      isFavorited: false,
      message: "Failed to toggle favorite",
    };
  }
};

/**
 * Retrieves a paginated list of a user's favorite items.
 * This fetches full item details for items that are not archived.
 * @param userId - The ID of the user whose favorites are being retrieved.
 * @param employeeId - An optional employee ID.
 * @param offset - The number of records to skip for pagination.
 * @param limit - The maximum number of records to return.
 * @returns A promise that resolves to an array of the user's favorite item objects.
 */
export const getFavoriteItems = async (
  userId: number,
  employeeId?: number,
  offset: number = 0,
  limit: number = 20
): Promise<any[]> => {
  console.log("user id", userId);

  const query = knex("user_item_favorites")
    .join("item", "user_item_favorites.item_id", "item.id")
    .where("user_item_favorites.user_id", userId)
    .where("item.archived", false)
    .select("item.*", "user_item_favorites.created_at as favorited_at");

  return query
    .orderBy("user_item_favorites.created_at", "desc")
    .limit(limit)
    .offset(offset);
};

/**
 * Retrieves an array of item IDs that the user has favorited.
 * @param userId - The ID of the user.
 * @param employeeId - An optional employee ID.
 * @returns A promise that resolves to an array of favorite item IDs.
 */
export const getFavoriteIds = async (
  userId: number,
  employeeId?: number
): Promise<number[]> => {
  const query = knex("user_item_favorites")
    .where("user_id", userId)
    .select("item_id");

  const favorites = await query;
  return favorites.map((f) => f.item_id);
};

/**
 * Annotates a list of items with the user's favorite status.
 * It adds an `isFavorited` boolean property to each item object in the provided array.
 * @param items - An array of item objects. Each object must have an `id` property.
 * @param userId - The ID of the user to check the favorite status against.
 * @param employeeId - An optional employee ID, passed to `getFavoriteIds`.
 * @returns A promise that resolves to the list of items, each annotated with `isFavorited`.
 */
export const addFavoriteStatusToItems = async (
  items: any[],
  userId: number,
  employeeId?: number
): Promise<any[]> => {
  if (!items.length || !userId) return items;

  const favoriteIds = await getFavoriteIds(userId, null);

  return items.map((item) => ({
    ...item,
    isFavorited: favoriteIds.includes(item.id),
  }));
};
