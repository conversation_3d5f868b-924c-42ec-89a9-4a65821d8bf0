import knex from "../../../../knex/knex";
import { QueryGroupPricesV2Args } from "../../../generated/graphql";
import { getGroups, getUsers } from "../../../services/userService";
import { getGroupCustomPrices } from "../../../services/userService";

const GroupPricesV2 = async (_, args: QueryGroupPricesV2Args) => {
  const { supplierId, groupName } = args.groupPricesInput;

  const groups = [];

  if (groupName) {
    groups.push(groupName);
  } else {
    groups.push(...(await getGroups(supplierId)));
  }

  const groupPricesResult = [];
  for (const group of groups) {
    groupPricesResult.push({
      group,
      prices: await getGroupCustomPrices(group),
    });
  }

  return {
    groupPrices: groupPricesResult,
    totalCount: groups.length,
  };
};
export default GroupPricesV2;
