# Attain Server

This is the Node server for Attain. We run it on Heroku.

### Ongoing Changes

We are undergoing a reorganization of the server. This includes the addition of:

- v2 endpoints that use filtering, pagination, and sorting. These should be prefered over other endpoints.
  - unused v1 functions/files (considered deprecated)
- new service directories for related entities

## Architecture Overview

The server follows a layered architecture:

1. **API Layer** - REST endpoints in `apis/` and GraphQL resolvers in `graphql/`
2. **Service Layer** - Business logic in domain-specific services in `services/`
3. **Data Access Layer** - Database interactions via Knex

We utilize REST endpoints for certain functions (`apis/`) and pass all other requests into the root (`/`) path, which connects to the Apollo GraphQL server.

## Project Layout

```
attain-server/
├── src/                      # Main source code directory
│   ├── apis/                 # REST API endpoints
│   ├── config/               # Configuration files and settings
│   ├── constants/            # Application constants and enums
│   ├── generated/            # Auto-generated files (GraphQL types, etc.)
│   ├── graphql/              # GraphQL schema definitions and resolvers
│   │   ├── mutations/        # GraphQL mutation resolvers
│   │   ├── queries/          # GraphQL query resolvers
│   │   │   └── v2/           # New v2 query resolvers with filtering, pagination, and sorting
│   ├── lib/                  # Shared libraries and utilities
│   ├── services/             # Service layer for business logic
│   │   ├── cartService/      # Cart-related functionality
│   │   ├── invoiceService/   # Invoice-related functionality
│   │   ├── itemService/      # Item-related functionality
│   │   ├── notificationService/ # Notification-related functionality
│   │   ├── orderService/     # Order-related functionality
│   │   ├── routeService/     # Route-related functionality
│   │   ├── supplierService/  # Supplier-related functionality
│   │   ├── userService/      # User-related functionality
│   │   ├── v1/               # Legacy v1 services (deprecated)
│   │   └── integrationService/ # Third-party integrations
│   └── util/                 # Utility functions and helpers
├── migrations/               # Database migration files
├── knex/                     # Knex.js database configurations and seeds
├── server.ts                 # Main server entry point
├── schema.graphql            # GraphQL schema definition
└── knexfile.ts               # Knex configuration for migrations
```

### Development

It is recommended you get a copy of the production database running in a local postgres db (instructions on this are in the Notion).

To set up your development environment:

1. Clone the repository
2. Install dependencies with `yarn install`
3. Set up environment variables (copy `.env.example` to `.env` and fill in values)
4. Run the development server with `yarn run dev`
