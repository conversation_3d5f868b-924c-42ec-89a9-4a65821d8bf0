import {
  applyPromotion as applyPromotionService,
  calculatePromotionDiscount,
} from "../../services/promotionService";
import { MutationApplyPromotionArgs } from "../../generated/graphql";
import knex from "../../../knex/knex";

const applyPromotion = async (
  _,
  { promotion_id, order_id, supplier_id }: MutationApplyPromotionArgs
) => {
  // Get order details
  const order = await knex("order_detail")
    .select("*")
    .where("id", order_id)
    .first();

  if (!order) {
    throw new Error(`Order not found: ${order_id}`);
  }

  // Get order items
  const orderItems = await knex("order_item")
    .select("*")
    .where("order_id", order_id);

  // Calculate the promotion discount
  const { discountAmount } = await calculatePromotionDiscount(
    supplier_id,
    promotion_id,
    orderItems.map((item) => ({
      item_id: item.item_id,
      quantity: item.quantity,
      price: item.price_purchased_at,
    })),
    order.subtotal
  );

  // Apply the promotion
  await applyPromotionService(
    promotion_id,
    order_id,
    order.user_id,
    discountAmount
  );

  // Return the updated order
  return knex("order_detail").select("*").where("id", order_id).first();
};

export default applyPromotion;
