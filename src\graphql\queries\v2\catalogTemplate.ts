import { QueryGetCatalogTemplatesArgs } from "../../../generated/graphql";
import { getCatalogsTemplate as getCatalogsTemplateService } from "../../../services/catalogTemplateService";

const getCatalogTemplates = async (_, args: QueryGetCatalogTemplatesArgs) => {
  const { id, supplier_id } = args.getCatalogTemplatesInput;
  const result = await getCatalogsTemplateService(supplier_id, id);
  return result;
};

export default getCatalogTemplates;
