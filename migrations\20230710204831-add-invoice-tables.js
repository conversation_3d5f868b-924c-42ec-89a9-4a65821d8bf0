"use strict";

var dbm;
var type;
var seed;

/**
 * We receive the dbmigrate dependency from dbmigrate initially.
 * This enables us to not have to rely on NODE_PATH.
 */
exports.setup = function (options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

exports.up = function (db) {
  return db
    .createTable("invoice", {
      // Create "invoice" table
      id: { type: "int", primaryKey: true, autoIncrement: true },
      date_received: { type: "timestamp" },
      total: { type: "numeric" },
      order_id: { type: "int" },
      supplier: { type: "string", length: 255 },
      invoice_id: { type: "string", length: 255 },
    })
    .then(() =>
      db.createTable("invoice_item", {
        // Create "invoice_item" table, depends on "invoice" table FK
        id: { type: "int", primaryKey: true, autoIncrement: true },
        invoice_id: {
          type: "int",
          foreignKey: {
            name: "fk_invoice_item_to_invoice",
            table: "invoice",
            mapping: "id",
            rules: {},
          },
        },
        name: { type: "string", length: 255 },
        quantity: { type: "int" },
        unit_size: { type: "string", length: 255 },
        size: { type: "text" },
        upc1: { type: "string", length: 255 },
        upc2: { type: "string", length: 255 },
        price: { type: "numeric" },
        checked_in: { type: "boolean" },
        checked_in_quantity: { type: "int" },
        is_mispick: { type: "boolean" },
      })
    );
};

exports.down = function (db) {
  return db.dropTable("invoice_item").then(
    () => db.dropTable("invoice") // Drop "invoice" table after "invoice_item" table due to dependency
  );
};

exports._meta = {
  version: 1,
};
