import { ApolloServer } from "@apollo/server";
import {
  S3Client,
  CompleteMultipartUploadCommandOutput,
} from "@aws-sdk/client-s3";
import { Upload } from "@aws-sdk/lib-storage";
import { Request, Response } from "express";
import mime from "mime-types";
import { v4 as uuidv4 } from "uuid";
import { awsS3Config } from "../config/environment";
import Expo from "expo-server-sdk";
import RestEndpoint from "./_restEndpoint";
import { getSupplier } from "../services/supplierService/supplierService";
import { ImageProcessor } from "../lib/imageProcessor";

export default class UploadImage extends RestEndpoint {
  private s3Client: S3Client;
  private imageProcessor: ImageProcessor;

  constructor(
    apolloServerInit: ApolloServer,
    expoClient: Expo,
    s3Client: S3Client
  ) {
    super(apolloServerInit, expoClient);
    this.s3Client = s3Client;
    this.imageProcessor = new ImageProcessor(s3Client);
  }

  protected async worker(): Promise<void> {
    // Implementation not needed for this endpoint
  }

  public async handler(req: Request, res: Response) {
    try {
      if (!req.file) {
        throw {
          code: 400,
          message: "No image file provided",
        };
      }

      const supplierId = req.body.supplierId;
      if (!supplierId || typeof supplierId !== "string") {
        throw {
          code: 400,
          message: "supplierId is required in the form data",
        };
      }

      const supplier = await getSupplier(supplierId);
      if (!supplier) {
        throw {
          code: 400,
          message: `No supplier found with ID ${supplierId}`,
        };
      }

      let supplierName = supplier.name;
      switch (supplierName) {
        case "Whitestone Foods":
          supplierName = "whitestone";
          break;
        case "C&G Snacks":
          supplierName = "cgsnacks";
          break;
        case "TRT":
          supplierName = "trt";
          break;
        default:
          throw {
            code: 400,
            message: `Invalid supplier name: ${supplierName}`,
          };
      }

      const file = req.file;
      // Validate file type
      if (!file.mimetype.startsWith("image/")) {
        throw {
          code: 400,
          message: "File must be an image",
        };
      }

      const baseUuid = uuidv4();
      const originalExtension = mime.extension(file.mimetype);

      // Upload original image
      const originalKey = `images/${supplierName}/${baseUuid}.${originalExtension}`;
      const originalParams = {
        Bucket: awsS3Config.bucket,
        Key: originalKey,
        Body: file.buffer,
        ContentType: file.mimetype,
      };

      const originalUpload = new Upload({
        client: this.s3Client,
        params: originalParams,
      }).done() as Promise<CompleteMultipartUploadCommandOutput>;

      // Upload compressed image
      const compressedFileName = `${baseUuid}-sm.png`;
      const compressedUpload = this.imageProcessor.processFileUpload(
        file,
        compressedFileName,
        {
          processing: {
            targetHeight: 180,
            format: "png",
            quality: 100,
            compressionLevel: 9,
            effort: 10,
          },
          s3Upload: {
            bucket: awsS3Config.bucket,
            keyPrefix: `images/${supplierName}`,
          },
        }
      );

      // Wait for both uploads to complete
      const [originalResult, compressedResult] = await Promise.all([
        originalUpload,
        compressedUpload,
      ]);

      const originalUrl = `https://${awsS3Config.bucket}.s3.amazonaws.com/${originalKey}`;

      res.status(200).json({
        url: originalUrl,
        sm_rul: compressedResult.s3Url,
        metadata: compressedResult.metadata,
      });
    } catch (err) {
      const errorMessage = `Failed to upload image: ${err.message || err}`;
      console.error(errorMessage);
      res.status(err.code || 500).send(errorMessage);
    }
  }
}
