NODE_ENV=development
DATABASE_URL=postgres://localhost:5432/attain-dev
SCRAPER_URL=https://attain-scrapers-2d4e13a2ac82.herokuapp.com
SCRAPER_PROJECT=default
SELF_URL=https://attain-local.loca.lt # This is the URL of this server (you can host on this URL using NPM package localtunnel and command: lt --port 4000 --print-requests --subdomain=attain-local)
AWS_S3_ACCESS_KEY_ID=s3_access_key_id
AWS_S3_SECRET_ACCESS_KEY=s3_secret_access_key
AWS_S3_RESOURCES_BUCKET=attain-app-resources-bucket
AWS_S3_RESOURCES_REGION=us-east-1
SENDGRID_API_KEY=SG.ND_abcdefgHIjklmnoP12Q.RS34uvWX5678Yz90aBcDE12FGHI34jkLmn56op7qRsT
ALGOLIA_APP_ID=12ABCD1A12
ALGOLIA_API_KEY=********************************

# Temporary variables for passing scraping credentials
SCRAPER_USERNAME=test_username
SCRAPER_PASSWORD=test_password
QUICKBOOKS_CLIENT_ID=ABcDeFgHiJKLMNopqRSTuVWxYz1ab23cdef45GHIjk6789lmn0
QUICKBOOKS_CLIENT_SECRET=ABcDeFgHiJKLMNopqRSTu1VW23xy45z67a8b9c0D
QUICKBOOKS_CLIENT_ID_SANDBOX=ABcDeFgHiJKLMNopqRSTuVWxYz1ab23cdef45GHIjk6789lmn0
QUICKBOOKS_CLIENT_SECRET_SANDBOX=ABcDeFgHiJKLMNopqRSTu1VW23xy45z67a8b9c0D
QUICKBOOKS_DESKTOP_CLIENT_SECRET=sk_live_aBCdEFGh1JKl2m