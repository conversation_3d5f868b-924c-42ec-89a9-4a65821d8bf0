{"defaultEnv": "test", "test": {"driver": "pg", "url": "postgres://testuser:testpass@localhost:5432/testdb"}, "development": {"driver": "pg", "url": "postgres://localhost:5432/testdb"}, "staging": {"driver": "pg", "url": {"ENV": "postgres://uedsab9lup2k36:<EMAIL>:5432/d58ioo6dhivng8"}, "ssl": {"rejectUnauthorized": false}}, "production": {"driver": "pg", "url": {"ENV": "postgres://u3d7j5ntplut92:<EMAIL>:5432/d5q5ie21b3ce27"}, "ssl": {"rejectUnauthorized": false}}}