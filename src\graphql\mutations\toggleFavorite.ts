import { MutationResolvers } from "../../generated/graphql";
import { toggleFavoriteItem } from "../../services/favoriteService";
import knex from "../../../knex/knex";

const toggleFavorite: MutationResolvers["toggleFavorite"] = async (
  _,
  { input }
) => {
  const { userId, itemId, employeeId } = input;

  try {
    const item = await knex("item")
      .where({ id: itemId, archived: false })
      .first();

    if (!item) {
      return {
        success: false,
        isFavorited: false,
        message: "Item not found or archived",
      };
    }

    const result = await toggleFavoriteItem(userId, itemId, employeeId);

    return result;
  } catch (error) {
    console.error("Error in toggleFavorite resolver:", error);
    return {
      success: false,
      isFavorited: false,
      message: "An error occurred while toggling favorite",
    };
  }
};

export default toggleFavorite;
