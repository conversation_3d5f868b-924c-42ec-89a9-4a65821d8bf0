import { Request, Response } from "express";
import RestEndpoint from "./_restEndpoint";
import knex from "../../knex/knex";
import QuickBooksClient from "../lib/quickBooks/client";
import { processQBItemUpdates } from "../services/integrationService/quickBooks";

export default class UpdateQuickBooksData extends RestEndpoint {
  public async handler(req: Request, res: Response) {
    await this.worker(req.body);
    res.status(200).send("Server OK");
  }

  protected async worker(body) {
    console.log("(Update) QB Data:", JSON.stringify(body));
    await Promise.all(
      body.eventNotifications.map(async (notification) => {
        try {
          const supplier = await knex("supplier")
            .select(
              "name",
              "qb_realm_id",
              "qb_access_token",
              "qb_refresh_token"
            )
            .where({ qb_realm_id: notification.realmId })
            .first();
          if (!supplier || !supplier.qb_realm_id) return;
          console.log("Running updates through supplier", supplier.name);

          const qClient = new QuickBooksClient(
            supplier.qb_access_token,
            supplier.qb_realm_id,
            supplier.qb_refresh_token
          );
          await processQBItemUpdates(
            qClient,
            supplier.name,
            notification,
            true
          );
        } catch (error) {
          console.error(
            `Couldn't process QB update data for notification ${JSON.stringify(
              notification,
              undefined,
              2
            )}: ${error} (${JSON.stringify(error, undefined, 2)}`
          );
        }
      })
    );
    return;
  }
}
