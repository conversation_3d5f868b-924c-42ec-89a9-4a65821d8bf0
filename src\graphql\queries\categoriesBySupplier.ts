import { QueryCategoriesBySupplierArgs } from "../../generated/graphql";
import { getCategories } from "../../services/itemService/categoryService";

const CategoriesBySupplier = async (_, args: QueryCategoriesBySupplierArgs) => {
  const { supplierId, fillItemsData } = args.getCategoriesBySupplierInput;
  const categories = await getCategories(supplierId, fillItemsData);
  return categories.sort((a, b) => a.ordering - b.ordering);
};

export default CategoriesBySupplier;
