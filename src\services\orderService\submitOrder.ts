import knex from "../../../knex/knex";
import axios from "axios";
import getCart from "../cartService/getCart";
import { sendOrderDetailsMessage } from "../notificationService/sendTextMessages";
import modifyToCustomPricing from "../itemService/modifyToCustomPricing";
import { dsdSupplierConfigMap } from "../../constants/suppliers";
import { getNextOrderNumber } from "./createOrder";

const submitOrder = async (
  userId: string,
  cartId: string,
  supplier: string
) => {
  const cartDetails = await getCart(cartId);

  let cartItems = cartDetails.cartItems;
  if (supplier) {
    const subCart = cartDetails.subCarts.find(
      (sub) => sub.supplier === supplier
    );
    cartItems = subCart.cartItems;
  }
  await modifyToCustomPricing(cartItems, userId);
  const subtotal = cartItems.reduce(
    (partialSum, a) =>
      partialSum +
      (parseFloat(
        (a.custom_price as never) ??
          (a.discounted_price as never) ??
          (a.price as never)
      ) || 0.0) *
        a.quantity,
    0.0
  );
  const selectSupplierId = await knex
    .select("id")
    .from("supplier")
    .where("name", supplier);
  const supplierId = selectSupplierId.length
    ? selectSupplierId[0].id.toString()
    : "";

  let orderDetails, orderId, csvExists, cartCSV;

  try {
    const orderNumber = await getNextOrderNumber(supplier);

    // Get user's net terms
    const user = await knex("attain_user")
      .select("net_terms_days")
      .where("id", userId)
      .first();
    const netTermsDays = user?.net_terms_days;

    await knex.transaction(async (trx) => {
      // Insert order and get ID
      const [orderResult] = await trx("order_detail")
        .insert({
          user_id: userId,
          status: "submitted",
          subtotal: subtotal,
          date_submitted: new Date().toUTCString(),
          single_supplier: supplier,
          order_number: orderNumber,
          discount: 0,
          net_terms_days: netTermsDays,
        })
        .returning("*");
      orderDetails = orderResult;
      orderId = orderResult.id;
      // Prepare bulk insert for order items
      const orderItems = cartItems.map((item) => ({
        order_id: orderId,
        item_id: item.item_id,
        quantity: item.quantity,
        price_purchased_at:
          item.custom_price ?? item.discounted_price ?? item.price,
        notes: item.notes || null,
        item_uom_id: item.item_uom_id,
      }));

      // Bulk insert order items
      await trx("order_item").insert(orderItems);

      // Delete cart items and reset cart
      const cartItemIds = cartItems.map((item) => item.id);
      await Promise.all([
        trx("cart_item").whereIn("id", cartItemIds).del(),
        trx("cart").where("id", cartId).update({ csv: "", subtotal: 0.0 }),
      ]);

      // Check for cart CSV and insert into duffl_order if exists
      cartCSV = await trx
        .select()
        .table("cart_csv")
        .where("cart_id", cartId)
        .where("supplier", supplier);

      csvExists =
        cartCSV.length > 0 &&
        typeof cartCSV[0].csv === "string" &&
        cartCSV[0].csv.length > 0;

      if (csvExists) {
        await trx("duffl_order").insert({
          csv: cartCSV[0].csv,
          order_id: orderId,
          status: "open",
          user_id: userId,
        });

        // Delete from cart_csv
        await trx("cart_csv")
          .where("cart_id", cartId)
          .where("supplier", supplier)
          .del();
      }
      // Create Order Status
    });

    const business = await knex("attain_user").where("id", userId).first();

    let slackUrl = process.env.SUBMITTED_ORDERS_SLACK_CHANNEL;
    if (dsdSupplierConfigMap[supplierId]?.orderNotificationSlackURL) {
      slackUrl = dsdSupplierConfigMap[supplierId].orderNotificationSlackURL;
    }
    if (userId === "1") {
      slackUrl =
        "*********************************************************************************";
    }

    try {
      await axios.post(
        slackUrl,
        {
          text:
            `Order #${orderId} for ${supplier} submitted by ${business.name} for $${subtotal}` +
            (csvExists ? `: \n${cartCSV[0].csv}` : ""),
        },
        {
          headers: {
            accept: "application/json",
            "content-type": "application/json",
          },
        }
      );
    } catch (error) {
      console.log("Error sending Slack message", error);
    }

    if (supplier === "A&I Beverage") {
      try {
        await sendOrderDetailsMessage(orderId);
      } catch (error) {
        console.log("Error sending order details message", error);
      }
    }

    return orderDetails;
  } catch (error) {
    console.log(error);
    // Handle error, transaction rolled back
  }
};
export default submitOrder;
