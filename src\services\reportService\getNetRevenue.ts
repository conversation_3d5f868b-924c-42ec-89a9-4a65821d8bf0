import { getGMV } from "./getGMV";
import { getCredits } from "./getCredits";

export interface NetRevenueFilters {
  lastNDays?: number;
  dateRange?: Date[];
  routeIds?: string[];
  driver?: string;
  serviceType?: string;
}

export const getNetRevenue = async (
  supplierId: string,
  supplierName: string,
  filters: NetRevenueFilters = {}
): Promise<number> => {
  // Calculate total revenue (GMV - positive invoices only)
  const totalRevenue = await getGMV(supplierId, supplierName, filters);

  // Calculate total credits (negative invoices)
  const totalCredits = await getCredits(supplierId, supplierName, filters);

  // Net revenue = total revenue - absolute value of credits
  const netRevenue = totalRevenue - Math.abs(totalCredits);

  return isNaN(netRevenue) ? 0 : netRevenue;
};
