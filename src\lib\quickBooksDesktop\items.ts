import QuickBooksDesktopClient from "./client";

const QuickBooksToDBItemMap = {
  Name: "name",
  ManufacturerPartNumber: "upc1",
  "Number:SalesPrice": "price",
  ListID: "qb_id",
  EditSequence: "qb_sync_token",
  "new Date:TimeCreated": "created_at",
  "new Date:TimeModified": "updated_at",
  IncomeAccountRef: "incomeAccountRef",
  AssetAccountRef: "assetAccountRef",
  UnitOfMeasureSetRef: "unitOfMeasureSetRef",
  COGSAccountRef: "cogsAccountRef",
  "Math_round:QuantityOnHand": "qty_on_hand",
  "Number:PurchaseCost": "cog_price",
};

const QuickBooksToDBItemMap_Whitestone = {
  Name: "metadata",
  SalesDesc: "name",
};

const DBToQuickBooksItemMap = {
  qb_id: "ListID",
  qb_sync_token: "EditSequence",
  name: "Name",
  upc1: "ManufacturerPartNumber",
  price: "SalesPrice",
  qty_on_hand: "QuantityOnHand",
};

export const pullItemInfo = async (
  client: QuickBooksDesktopClient,
  supplierId: string,
  id: string,
  includeCharge = false,
  includeNonInventory = false
) => {
  try {
    const item = (
      await client.getItems({ ListID: id }, includeCharge, includeNonInventory)
    )[0];
    console.error(item);
    const QuickBooksToDBItemMapCustom = {
      ...QuickBooksToDBItemMap,
      ...(supplierId === "68" ? QuickBooksToDBItemMap_Whitestone : {}),
    };

    const itemToSave = {};
    Object.keys(QuickBooksToDBItemMapCustom).forEach((key) => {
      const keyContainsType = key.indexOf(":") !== -1;
      const keyContainsSubParts = key.indexOf(".") !== -1;

      itemToSave[QuickBooksToDBItemMapCustom[key]] = !keyContainsSubParts
        ? item[keyContainsType ? key.split(":")[1] : key]
        : (keyContainsType ? key.split(":")[1] : key)
            .split(".")
            .reduce((obj, i) => obj[i], item);
      if (keyContainsType) {
        itemToSave[QuickBooksToDBItemMapCustom[key]] = eval(
          `${key.split(":")[0].replace("_", ".")}("${
            itemToSave[QuickBooksToDBItemMapCustom[key]]
          }")`
        );
      }
      if (QuickBooksToDBItemMapCustom[key] === "qty_on_hand") {
        itemToSave["qty_on_hand"] = itemToSave["qty_on_hand"] || 0;
      }
      if (QuickBooksToDBItemMapCustom[key] === "cog_price") {
        itemToSave["cog_price"] = itemToSave["cog_price"] || 0;
      }
      if (QuickBooksToDBItemMapCustom[key] === "price") {
        itemToSave["price"] = Number(
          item["SalesOrPurchase"]?.SalesPrice ||
            item["SalesOrPurchase"]?.Price ||
            itemToSave["price"]
        );
        if (!itemToSave["price"]) {
          itemToSave["price"] = 0;
        }
      }
      if (QuickBooksToDBItemMapCustom[key] === "name") {
        itemToSave["name"] =
          item["SalesOrPurchase"]?.SalesDesc || itemToSave["name"];
        if (!itemToSave["name"]) {
          itemToSave["name"] =
            item["SalesDesc"] || item["Name"] || item["ListID"];
        }
      }
    });
    if (item["SalesOrPurchase"] || item["SalesAndPurchase"]) {
      itemToSave["brand"] = "QBD_CHARGE";
    }
    return itemToSave;
  } catch (error) {
    console.error(error);
    const errorMessage = `Couldn't pull item with id ${id} from QBD: ${JSON.stringify(
      error,
      undefined,
      2
    )}`;
    console.error(errorMessage);
    throw new Error(errorMessage);
  }
};

export const pullItemsInfo = async (
  client: QuickBooksDesktopClient,
  supplierId: string,
  ids: string[] = [],
  includeInventoryAssembly = false,
  includeCharge = false,
  includeNonInventory = false
) => {
  try {
    const items = await client.getItems(
      ids.length > 0 ? { ListID: ids } : undefined,
      includeInventoryAssembly,
      includeCharge,
      includeNonInventory
    );
    const QuickBooksToDBItemMapCustom = {
      ...QuickBooksToDBItemMap,
      ...(supplierId === "68" ? QuickBooksToDBItemMap_Whitestone : {}),
    };

    const itemsToSave = items.map((item) => {
      const itemToSave = {};
      Object.keys(QuickBooksToDBItemMapCustom).forEach((key) => {
        const keyContainsType = key.indexOf(":") !== -1;
        const keyContainsSubParts = key.indexOf(".") !== -1;

        itemToSave[QuickBooksToDBItemMapCustom[key]] = !keyContainsSubParts
          ? item[keyContainsType ? key.split(":")[1] : key]
          : (keyContainsType ? key.split(":")[1] : key)
              .split(".")
              .reduce((obj, i) => obj[i], item);
        if (keyContainsType) {
          itemToSave[QuickBooksToDBItemMapCustom[key]] = eval(
            `${key.split(":")[0].replace("_", ".")}("${
              itemToSave[QuickBooksToDBItemMapCustom[key]]
            }")`
          );
        }
        if (QuickBooksToDBItemMapCustom[key] === "qty_on_hand") {
          itemToSave["qty_on_hand"] = itemToSave["qty_on_hand"] || 0;
        }
        if (QuickBooksToDBItemMapCustom[key] === "cog_price") {
          itemToSave["cog_price"] = Number(
            item["SalesOrPurchase"]?.PurchaseCost ||
              item["SalesAndPurchase"]?.PurchaseCost ||
              itemToSave["cog_price"] ||
              0
          );
        }
        if (QuickBooksToDBItemMapCustom[key] === "price") {
          itemToSave["price"] = Number(
            item["SalesOrPurchase"]?.SalesPrice ||
              item["SalesOrPurchase"]?.Price ||
              item["SalesAndPurchase"]?.SalesPrice ||
              item["SalesAndPurchase"]?.Price ||
              itemToSave["price"] ||
              0
          );
        }
        if (QuickBooksToDBItemMapCustom[key] === "name") {
          itemToSave["name"] =
            item["SalesOrPurchase"]?.SalesDesc ||
            item["SalesAndPurchase"]?.SalesDesc ||
            itemToSave["name"];
          if (!itemToSave["name"]) {
            itemToSave["name"] =
              item["SalesDesc"] || item["Name"] || item["ListID"];
          }
        }
      });
      if (item["SalesOrPurchase"] || item["SalesAndPurchase"]) {
        itemToSave["brand"] = "QBD_CHARGE";
      }
      return itemToSave;
    });
    return itemsToSave;
  } catch (error) {
    console.error(error);
    const errorMessage = `Couldn't pull items from QBD: ${JSON.stringify(
      error,
      undefined,
      2
    )}`;
    console.error(errorMessage);
    throw new Error(errorMessage);
  }
};
