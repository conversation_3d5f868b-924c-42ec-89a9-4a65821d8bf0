"use strict";

var dbm;
var type;
var seed;

/**
 * We receive the dbmigrate dependency from dbmigrate initially.
 * This enables us to not have to rely on NODE_PATH.
 */
exports.setup = function (options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

exports.up = function (db) {
  return db
    .addColumn("cart", "created_at", {
      type: "timestamp",
      notNull: false,
      defaultValue: new String("CURRENT_TIMESTAMP"),
    })
    .then(() => {
      return db.addColumn("cart", "updated_at", {
        type: "timestamp",
        notNull: false,
        defaultValue: new String("CURRENT_TIMESTAMP"),
      });
    })
    .then(() => {
      return db.addColumn("cart_item", "created_at", {
        type: "timestamp",
        notNull: false,
        defaultValue: new String("CURRENT_TIMESTAMP"),
      });
    })
    .then(() => {
      return db.addColumn("cart_item", "updated_at", {
        type: "timestamp",
        notNull: false,
        defaultValue: new String("CURRENT_TIMESTAMP"),
      });
    });
};

exports.down = function (db) {
  return db
    .removeColumn("cart", "created_at")
    .then(() => db.removeColumn("cart", "updated_at"))
    .then(() => db.removeColumn("cart_item", "created_at"))
    .then(() => db.removeColumn("cart_item", "updated_at"));
};

exports._meta = {
  version: 1,
};
