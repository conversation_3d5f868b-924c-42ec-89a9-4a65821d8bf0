import { K<PERSON> } from "knex";
import knex from "../../../knex/knex";
import { PaginationInput, SortBy, Ordering } from "../../generated/graphql";

export interface Employee {
  id: string;
  name: string;
  phone: string;
  email: string;
  app_access: boolean;
  dashboard_access: boolean;
  created_at: Date;
  updated_at: Date;
  last_login?: Date;
  archived: boolean;
  roles?: Array<{
    id: string;
    name: string;
    description: string;
  }>;
  routes?: Array<{
    id: string;
    name: string;
    color: string;
    day_of_week: string;
    driver?: string;
    supplier_id: string;
    config?: any;
  }>;
}

export interface EmployeeFilters {
  supplierId: string;
  ids?: string[];
  name?: string;
  email?: string;
  phone?: string;
  appAccess?: boolean;
  dashboardAccess?: boolean;
  roleIds?: string[];
  includeArchived?: boolean;
  query?: string; // For searching across name, email, phone
  createdAfter?: Date;
  createdBefore?: Date;
  lastLoginAfter?: Date;
  lastLoginBefore?: Date;
}

/**
 * Get employees with optional filtering, sorting, and pagination
 */
export const getEmployees = async ({
  filters,
  pagination = { offset: 0, limit: 50 },
  sortBy = { field: "id", ordering: Ordering.Desc },
}: {
  filters?: EmployeeFilters;
  pagination?: PaginationInput;
  sortBy?: SortBy;
}): Promise<{ employees: Employee[]; totalCount: number }> => {
  // Build the base query
  const baseQuery = knex
    .select(
      "employees.*",
      knex.raw(
        `COALESCE(
          json_agg(
            DISTINCT jsonb_build_object(
              'id', roles.id,
              'name', roles.name,
              'description', roles.description
            )
          ) FILTER (WHERE roles.id IS NOT NULL),
          '[]'
        ) as roles`
      )
    )
    .from("employees")
    .leftJoin("role_assignment", "employees.id", "role_assignment.employee_id")
    .leftJoin("roles", "role_assignment.role_id", "roles.id")
    .where("employees.supplier_id", filters?.supplierId)
    .groupBy("employees.id");

  // Apply filters
  if (filters) {
    if (filters.ids && filters.ids.length > 0) {
      baseQuery.whereIn("employees.id", filters.ids);
    }

    if (filters.name) {
      baseQuery.where("employees.name", "ilike", `%${filters.name}%`);
    }

    if (filters.email) {
      baseQuery.where("employees.email", "ilike", `%${filters.email}%`);
    }

    if (filters.phone) {
      baseQuery.where("employees.phone", "ilike", `%${filters.phone}%`);
    }

    if (filters.appAccess !== undefined) {
      baseQuery.where("employees.app_access", filters.appAccess);
    }

    if (filters.dashboardAccess !== undefined) {
      baseQuery.where("employees.dashboard_access", filters.dashboardAccess);
    }

    if (filters.roleIds && filters.roleIds.length > 0) {
      baseQuery.whereExists(function () {
        this.select(knex.raw(1))
          .from("role_assignment")
          .whereRaw("role_assignment.employee_id = employees.id")
          .whereIn("role_assignment.role_id", filters.roleIds);
      });
    }

    // By default, exclude archived employees unless explicitly included
    if (!filters.includeArchived) {
      baseQuery.where("employees.archived", false);
    }

    // Generic search query across multiple fields
    if (filters.query) {
      baseQuery.where(function () {
        this.where("employees.name", "ilike", `%${filters.query}%`)
          .orWhere("employees.email", "ilike", `%${filters.query}%`)
          .orWhere("employees.phone", "ilike", `%${filters.query}%`);
      });
    }

    // Date range filters
    if (filters.createdAfter) {
      baseQuery.where("employees.created_at", ">=", filters.createdAfter);
    }

    if (filters.createdBefore) {
      baseQuery.where("employees.created_at", "<=", filters.createdBefore);
    }

    if (filters.lastLoginAfter) {
      baseQuery.where("employees.last_login", ">=", filters.lastLoginAfter);
    }

    if (filters.lastLoginBefore) {
      baseQuery.where("employees.last_login", "<=", filters.lastLoginBefore);
    }
  }

  // Count total (before pagination)
  const countQuery = knex
    .from(baseQuery.as("filtered_employees"))
    .count("* as count")
    .first();
  const countResult = await countQuery;
  const totalCount = parseInt(
    countResult ? (countResult as any).count : "0",
    10
  );

  // Apply sorting and pagination
  const paginatedQuery = baseQuery
    .orderBy(`employees.${sortBy.field}`, sortBy.ordering.toLowerCase())
    .offset(pagination.offset || 0)
    .limit(pagination.limit || 50);

  const rows = await paginatedQuery;

  // Map rows to typed Employee objects with roles
  const employees: Employee[] = rows.map((row) => ({
    id: row.id,
    name: row.name,
    phone: row.phone,
    email: row.email,
    app_access: row.app_access,
    dashboard_access: row.dashboard_access,
    created_at: row.created_at,
    updated_at: row.updated_at,
    last_login: row.last_login,
    archived: row.archived,
    roles: row.roles,
  }));

  // Now fetch routes for each employee
  if (employees.length > 0) {
    const employeeIds = employees.map((emp) => emp.id);

    // Get all routes assigned to these employees
    const routesData = await knex
      .select(
        "route_assignment.employee_id",
        "route.id",
        "route.name",
        "route.color",
        "route.day_of_week",
        "route.driver",
        "route.supplier_id",
        "route.config"
      )
      .from("route_assignment")
      .join("route", "route_assignment.route_id", "route.id")
      .whereIn("route_assignment.employee_id", employeeIds);

    // Group routes by employee
    const routesByEmployee: Record<string, Array<any>> = {};
    routesData.forEach((route) => {
      if (!routesByEmployee[route.employee_id]) {
        routesByEmployee[route.employee_id] = [];
      }

      routesByEmployee[route.employee_id].push({
        id: route.id,
        name: route.name,
        color: route.color,
        day_of_week: route.day_of_week,
        driver: route.driver,
        supplier_id: route.supplier_id,
        config: route.config,
      });
    });

    // Add routes to employees
    employees.forEach((employee) => {
      employee.routes = routesByEmployee[employee.id] || [];
    });
  }

  return { employees, totalCount };
};

export default getEmployees;
