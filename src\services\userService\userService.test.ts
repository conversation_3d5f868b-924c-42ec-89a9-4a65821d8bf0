import { queryBuilder } from "../../../knex/mock";

const knexMock = jest.fn(() => queryBuilder);
Object.assign(knexMock, queryBuilder);

(knexMock as any).fn = {};
// Mock the transaction function
(knexMock as any).transaction = jest.fn(async (callback) => {
  const trx = jest.fn(() => queryBuilder) as any;
  Object.assign(trx, queryBuilder, {
    commit: jest.fn(),
    rollback: jest.fn(),
  });
  await callback(trx);
});

jest.mock("../../../knex/knex", () => ({
  __esModule: true,
  default: knexMock,
}));

// Mock external dependencies
jest.mock("../routeService/routeService", () => ({
  getRoutesOnDay: jest.fn(),
}));

describe("User Service", () => {
  let getUser: typeof import("../userService").getUser;
  let getUsers: typeof import("../userService").getUsers;
  let updateUser: typeof import("../userService").updateUser;
  let getUsersInGroup: typeof import("../userService").getUsersInGroup;
  let populateUsersCustomPrices: typeof import("../userService").populateUsersCustomPrices;
  let getUserCustomPrices: typeof import("../userService").getUserCustomPrices;
  let getGroupCustomPrices: typeof import("../userService").getGroupCustomPrices;
  let getGroups: typeof import("../userService").getGroups;
  let getUsersScheduledOnDay: typeof import("../userService").getUsersScheduledOnDay;
  let updateUserCustomPrices: typeof import("../userService").updateUserCustomPrices;
  let updateGroupCustomPrices: typeof import("../userService").updateGroupCustomPrices;
  let getRoutesOnDay: jest.Mock;

  beforeAll(async () => {
    const userModule = await import("../userService");
    const routeServiceModule = await import("../routeService/routeService");
    getUser = userModule.getUser;
    getUsers = userModule.getUsers;
    updateUser = userModule.updateUser;
    getUsersInGroup = userModule.getUsersInGroup;
    populateUsersCustomPrices = userModule.populateUsersCustomPrices;
    getUserCustomPrices = userModule.getUserCustomPrices;
    getGroupCustomPrices = userModule.getGroupCustomPrices;
    getGroups = userModule.getGroups;
    getUsersScheduledOnDay = userModule.getUsersScheduledOnDay;
    updateUserCustomPrices = userModule.updateUserCustomPrices;
    updateGroupCustomPrices = userModule.updateGroupCustomPrices;
    getRoutesOnDay = routeServiceModule.getRoutesOnDay as jest.Mock;
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("getUser", () => {
    it("returns user if found", async () => {
      const mockUser = { id: "user-1", name: "John" };
      queryBuilder.first.mockResolvedValueOnce(mockUser);

      const result = await getUser("user-1");

      expect(queryBuilder.where).toHaveBeenCalledWith("id", "user-1");
      expect(result).toEqual(mockUser);
    });

    it("returns null if not found", async () => {
      queryBuilder.first.mockResolvedValueOnce(null);
      const result = await getUser("not-found");
      expect(result).toBeNull();
    });
  });

  describe("getUsers", () => {
    it("returns users with supplierId", async () => {
      const mockUsers = [{ id: "u1" }, { id: "u2" }];
      queryBuilder.first.mockResolvedValueOnce({ count: mockUsers.length });
      queryBuilder.then.mockImplementationOnce((resolve) => resolve(mockUsers));

      const result = await getUsers({ supplierId: "supplier-123" });

      expect(queryBuilder.where).toHaveBeenCalledWith(
        "st.supplier_id",
        "supplier-123"
      );
      expect(result.users).toEqual(mockUsers);
      expect(result.totalCount).toBe(mockUsers.length);
    });

    it("returns users with filters", async () => {
      const mockUsers = [{ id: "u1" }];
      queryBuilder.first.mockResolvedValueOnce({ count: mockUsers.length });
      queryBuilder.then.mockImplementationOnce((resolve) => resolve(mockUsers));

      const result = await getUsers({
        filters: {
          ids: ["u1"],
          searchTerm: "john",
        },
      });

      expect(queryBuilder.whereIn).toHaveBeenCalledWith("u.id", ["u1"]);

      expect(queryBuilder.where).toHaveBeenCalledWith(expect.any(Function));
      expect(result.users).toEqual(mockUsers);
      expect(result.totalCount).toBe(1);
    });

    it("returns empty array with no filters", async () => {
      queryBuilder.first.mockResolvedValueOnce({ count: 0 });
      queryBuilder.then.mockImplementationOnce((resolve) => resolve([]));
      const result = await getUsers({});
      expect(result.users).toEqual([]);
      expect(result.totalCount).toBe(0);
    });
  });

  describe("updateUser", () => {
    it("updates user and returns updated info", async () => {
      const mockTxn = jest.fn(() => queryBuilder) as any;
      Object.assign(mockTxn, queryBuilder);

      const mockUserInput = {
        id: "user-1",
        name: "John Doe",
        address: " 123 Main St.  ",
        store_group: "GroupA",
      };
      const updatedUser = {
        ...mockUserInput,
        address: "123 Main St.",
        custom_prices: [],
      };

      // Mock calls in order of execution within updateUser
      queryBuilder.first.mockResolvedValueOnce({
        id: "user-1",
        store_group: "GroupA",
      });
      queryBuilder.first.mockResolvedValueOnce({ supplier_id: "sup-123" });
      queryBuilder.then.mockImplementationOnce((cb) =>
        cb([{ store_group: "GroupA" }])
      );
      queryBuilder.returning.mockResolvedValueOnce([updatedUser]);
      queryBuilder.then.mockImplementationOnce((cb) => cb([]));

      const result = await updateUser(mockTxn, mockUserInput);

      expect(mockTxn).toHaveBeenCalledWith("attain_user");
      expect(queryBuilder.where).toHaveBeenCalledWith("id", "user-1");
      // The service code passes the userData object to update() before modifying the address. The test must expect the original, untrimmed address.
      expect(queryBuilder.update).toHaveBeenCalledWith({
        address: " 123 Main St.  ",
        name: "John Doe",
        store_group: "GroupA",
      });
      expect(result).toEqual(updatedUser);
    });

    it("throws error when user not found", async () => {
      const mockTxn = jest.fn(() => queryBuilder) as any;
      const mockInput = { id: "user-x" };

      queryBuilder.first.mockResolvedValueOnce(undefined);

      await expect(updateUser(mockTxn, mockInput)).rejects.toThrow(
        "User not found with ID: user-x"
      );
    });
  });

  describe("getUsersInGroup", () => {
    it("should return users for a given group name", async () => {
      const mockUsers = [{ id: "user-1", name: "Test User" }];
      queryBuilder.then.mockImplementationOnce((cb) => cb(mockUsers));
      const result = await getUsersInGroup("GroupA");
      expect(queryBuilder.where).toHaveBeenCalledWith({
        store_group: "GroupA",
      });
      expect(result).toEqual(mockUsers);
    });
  });

  describe("populateUsersCustomPrices", () => {
    it("should populate custom prices for a list of users", async () => {
      const users = [{ id: "user-1" }, { id: "user-2" }] as any[];
      const prices = [{ user_id: "user-1", item_id: "item-1", price: 10 }];
      queryBuilder.then.mockImplementationOnce((cb) => cb(prices));

      const result = await populateUsersCustomPrices(users);

      expect(queryBuilder.whereIn).toHaveBeenCalledWith("user_id", [
        "user-1",
        "user-2",
      ]);
      expect(result[0].custom_prices).toEqual(prices);
      expect(result[1].custom_prices).toEqual([]);
    });
  });

  describe("getUserCustomPrices", () => {
    it("should return custom prices for a user", async () => {
      const user = { id: "user-1" } as any;
      const prices = [{ item_id: "item-1", price: 10 }];
      queryBuilder.then.mockImplementationOnce((cb) => cb(prices));

      const result = await getUserCustomPrices(user);

      expect(queryBuilder.where).toHaveBeenCalledWith("user_id", "user-1");
      expect(result).toEqual(prices);
    });
  });

  describe("getGroupCustomPrices", () => {
    it("should return custom prices for a group", async () => {
      const user = { id: "user-1" };
      const prices = [{ item_id: "item-1", price: 10 }];
      queryBuilder.first.mockResolvedValueOnce(user);
      // This mocks the call inside getUserCustomPrices
      queryBuilder.then.mockImplementationOnce((cb) => cb(prices));

      const result = await getGroupCustomPrices("GroupA");

      expect(queryBuilder.where).toHaveBeenCalledWith({
        store_group: "GroupA",
      });
      expect(result).toEqual(prices);
    });

    it("should throw an error if group does not exist", async () => {
      queryBuilder.first.mockResolvedValueOnce(null);
      await expect(getGroupCustomPrices("NonExistentGroup")).rejects.toThrow(
        "Group does not exist: NonExistentGroup"
      );
    });
  });

  describe("getGroups", () => {
    it("should return a list of distinct group names for a supplier", async () => {
      const groups = [{ store_group: "GroupA" }, { store_group: "GroupB" }];
      queryBuilder.then.mockImplementationOnce((cb) => cb(groups));

      const result = await getGroups("sup-1");

      expect(queryBuilder.where).toHaveBeenCalledWith(
        "st.supplier_id",
        "sup-1"
      );
      expect(result).toEqual(["GroupA", "GroupB"]);
    });
  });

  describe("getUsersScheduledOnDay", () => {
    it("should return users scheduled for a specific day", async () => {
      const routes = [{ id: 1 }, { id: 2 }];
      const users = { users: [{ id: "user-1" }], totalCount: 1 };
      getRoutesOnDay.mockResolvedValue(routes);
      // Mock the getUsers call
      queryBuilder.first.mockResolvedValueOnce({ count: 1 });
      queryBuilder.then.mockImplementationOnce((cb) => cb(users.users));

      const result = await getUsersScheduledOnDay("sup-1", new Date());

      expect(getRoutesOnDay).toHaveBeenCalled();
      expect(queryBuilder.whereIn).toHaveBeenCalledWith("u.route_id", [
        "1",
        "2",
      ]);
      expect(result).toEqual(users.users);
    });

    it("should return empty array if no routes are scheduled", async () => {
      getRoutesOnDay.mockResolvedValue([]);
      const result = await getUsersScheduledOnDay("sup-1", new Date());
      expect(result).toEqual([]);
    });
  });

  describe("updateUserCustomPrices", () => {
    it("should delete and insert custom prices", async () => {
      const mockTxn = jest.fn(() => queryBuilder) as any;
      Object.assign(mockTxn, queryBuilder);
      const prices = [{ item_id: "item-1", price: 100 }];
      queryBuilder.returning.mockResolvedValue(prices);

      await updateUserCustomPrices(mockTxn, "user-1", prices);

      expect(mockTxn).toHaveBeenCalledWith("custom_item_price");
      expect((queryBuilder as any).delete).toHaveBeenCalled();
      expect(queryBuilder.insert).toHaveBeenCalledWith([
        { item_id: "item-1", price: 100, user_id: "user-1" },
      ]);
    });
  });

  describe("updateGroupCustomPrices", () => {
    it("should update custom prices for all users in a group", async () => {
      const mockTxn = jest.fn(() => queryBuilder) as any;
      Object.assign(mockTxn, queryBuilder);
      const usersInGroup = [{ id: "user-1" }, { id: "user-2" }];
      const prices = [{ item_id: "item-1", price: 50 }];

      // Mock getUsersInGroup call
      queryBuilder.then.mockImplementationOnce((cb) => cb(usersInGroup));
      queryBuilder.returning.mockResolvedValue(prices);

      await updateGroupCustomPrices(mockTxn, "GroupA", prices);

      expect((queryBuilder as any).delete).toHaveBeenCalledTimes(2);
      expect(queryBuilder.insert).toHaveBeenCalledTimes(2);
    });
  });
});
