import { ApolloServer } from "@apollo/server";
import { MailService } from "@sendgrid/mail";
import Expo from "expo-server-sdk";
import { Request, Response } from "express";
import { Supplier } from "../generated/graphql";
import RestEndpoint from "./_restEndpoint";
import { getItems } from "../services/itemService/itemService";
import { generateCatalogPDF, CatalogData } from "../lib/catalogPDF";
import { CatalogTemplateConfig } from "../services/catalogTemplateService/db.types";

export default class SendCatalog extends RestEndpoint {
  private sendgridClient: MailService;

  constructor(
    apolloServerInit: ApolloServer,
    expoClient: Expo,
    sgMail: MailService
  ) {
    super(apolloServerInit, expoClient);
    this.sendgridClient = sgMail;
  }

  public async handler(req: Request, res: Response) {
    const { supplierId, config, email, subject, message, userId } = req.body;

    try {
      console.log(`Sending Catalog - Supplier ID: ${supplierId}`);
      console.log(`To Email: ${email}`);
      console.log(`Subject: ${subject}`);

      if (!supplierId || !config || !email || !subject) {
        throw {
          code: 400,
          message: "supplierId, config, email, and subject are required",
        };
      }

      const result = await this.worker(
        supplierId,
        config,
        email,
        subject,
        message,
        userId
      );

      res.status(200).json(result);
    } catch (err) {
      const errorMessage = `Failed to send catalog PDF via email: ${
        err.message || err
      }`;
      console.error(errorMessage);
      res.status(err.code || 500).send(errorMessage);
    }
  }

  protected async worker(
    supplierId: string,
    configData: CatalogTemplateConfig | string,
    email: string,
    subject: string,
    message?: string,
    userId?: string
  ) {
    // Parse config if it's a string
    const config =
      typeof configData === "string"
        ? (JSON.parse(configData) as CatalogTemplateConfig)
        : (configData as CatalogTemplateConfig);

    // Get supplier information
    const supplier = await this.getSupplier(supplierId);

    // Get items based on config
    const { items } = await getItems({
      supplierId,
      userId: userId || "", // No user-specific filtering for catalog generation
      filters: {
        archived: false,
        ...(config.category && { category: config.category }),
      },
      pagination: { offset: 0, limit: 1000 }, // Get all items
      sortBy: { field: "name", ordering: "asc" },
    });

    // Prepare catalog data
    const catalogData: CatalogData = {
      items,
      config: {
        category: config.category,
        sort_by: config.sort_by as any, // Map string to enum
        title: config.title,
        subtitle: config.subtitle || "",
        company_info: config.company_info,
        pricing_enabled: config.pricing_enabled,
        display_options: {
          show_sku: config.display_options.show_sku,
          show_case_size: config.display_options.show_case_size,
          show_upc: config.display_options.show_upc,
          show_stock: config.display_options.show_stock,
        },
      },
    };

    // Generate PDF using jsPDF
    const pdfBuffer = await generateCatalogPDF(catalogData);

    // Send email
    await this.sendEmail(
      email,
      subject,
      message || "",
      supplier.name,
      pdfBuffer
    );

    return {
      success: true,
      message: "Catalog PDF sent successfully",
      sentTo: email,
    };
  }

  private async sendEmail(
    email: string,
    subject: string,
    message: string,
    supplierName: string,
    pdfBuffer: Buffer
  ) {
    const emailMessage = {
      from: "<EMAIL>",
      to: email,
      subject: subject,
      text:
        message ||
        `Please find attached the catalog from ${supplierName}, generated by Attain.`,
      attachments: [
        {
          content: pdfBuffer.toString("base64"),
          filename: `${supplierName}-Catalog-${
            new Date().toISOString().split("T")[0]
          }.pdf`,
          type: "application/pdf",
          disposition: "attachment",
        },
      ],
    };

    await this.sendgridClient.send(emailMessage);
  }

  private async getSupplier(supplierId: string): Promise<Supplier> {
    const variables = {
      getSuppliersInput: {
        ids: [supplierId],
      },
    };
    const query = /* GraphQL */ `
      query SuppliersQuery($getSuppliersInput: GetSuppliersInput) {
        suppliers(getSuppliersInput: $getSuppliersInput) {
          id
          name
          logo
          address
          phone_number
          email
        }
      }
    `;
    const result = (
      (await this.apolloHttpPost(query, variables)) as { suppliers: Supplier[] }
    ).suppliers;

    if (!result.length) {
      throw { code: 404, message: `Supplier with ID ${supplierId} not found` };
    }

    return result[0];
  }
}
