import { queryBuilder } from "../../../knex/mock";
import { CreateSupplierInput } from "../../generated/graphql";

const knexMockFn = jest.fn().mockReturnValue(queryBuilder);

// Use Object.assign to build the final mock. This combines the callable
// function with the extra properties (`fn`) in a way TypeScript understands.
const knexMock = Object.assign(knexMockFn, {
  fn: {
    now: jest.fn().mockReturnValue(new Date().toISOString()),
  },
});

jest.mock("../../../knex/knex", () => ({
  __esModule: true,
  default: knexMock,
}));

describe("Supplier Config Service", () => {
  let service: typeof import("./supplierService");

  const supplierId = "supplier-123";

  beforeAll(async () => {
    service = await import("./supplierService");
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("getSupplierConfig", () => {
    it("parses JSON values", async () => {
      queryBuilder.select.mockResolvedValueOnce([
        { key: "foo", value: JSON.stringify({ a: 1 }) },
        { key: "bar", value: "plain string" },
      ]);

      const result = await service.getSupplierConfig(supplierId);

      expect(result).toEqual({
        foo: { a: 1 },
        bar: "plain string",
      });
    });

    it("returns empty object if no config found", async () => {
      queryBuilder.select.mockResolvedValueOnce([]);

      const result = await service.getSupplierConfig(supplierId);
      expect(result).toEqual({});
    });
  });

  describe("createSupplierConfig", () => {
    it("inserts stringified values and returns updated config", async () => {
      queryBuilder.returning.mockResolvedValueOnce([{ id: 1 }]);
      queryBuilder.select.mockResolvedValueOnce([
        { key: "timezone", value: '"UTC"' },
      ]);

      const result = await service.createSupplierConfig(
        supplierId,
        "timezone",
        "UTC"
      );

      expect(queryBuilder.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          supplier_id: supplierId,
          key: "timezone",
          value: "UTC",
        })
      );

      expect(result).toEqual({
        timezone: "UTC",
      });
    });

    it("stringifies non-string values", async () => {
      queryBuilder.returning.mockResolvedValueOnce([{ id: 1 }]);
      queryBuilder.select.mockResolvedValueOnce([
        { key: "config", value: JSON.stringify({ a: 1 }) },
      ]);

      const result = await service.createSupplierConfig(supplierId, "config", {
        a: 1,
      });

      expect(result).toEqual({
        config: { a: 1 },
      });
    });
  });

  describe("updateSupplierConfig", () => {
    it("updates existing config and returns latest", async () => {
      queryBuilder.returning.mockResolvedValueOnce([{ id: 1 }]);
      queryBuilder.select.mockResolvedValueOnce([
        { key: "feature", value: "enabled" },
      ]);

      const result = await service.updateSupplierConfig(
        supplierId,
        "feature",
        "enabled"
      );

      expect(result).toEqual({
        feature: "enabled",
      });
    });

    it("deletes config if value is null", async () => {
      queryBuilder.del.mockResolvedValueOnce(1);
      queryBuilder.select.mockResolvedValueOnce([]);

      const result = await service.updateSupplierConfig(
        supplierId,
        "feature",
        null
      );

      expect(queryBuilder.del).toHaveBeenCalled();
      expect(result).toEqual({});
    });
  });

  describe("upsertSupplierConfig", () => {
    it("updates config if already exists", async () => {
      queryBuilder.first.mockResolvedValueOnce({ key: "setting" });
      queryBuilder.returning.mockResolvedValueOnce([{ id: 1 }]);
      queryBuilder.select.mockResolvedValueOnce([
        { key: "setting", value: '"new"' },
      ]);

      const result = await service.upsertSupplierConfig(
        supplierId,
        "setting",
        "new"
      );

      expect(result).toEqual({ setting: "new" });
    });

    it("creates config if it does not exist", async () => {
      queryBuilder.first.mockResolvedValueOnce(null);
      queryBuilder.returning.mockResolvedValueOnce([{ id: 2 }]);
      queryBuilder.select.mockResolvedValueOnce([
        { key: "setting", value: '"default"' },
      ]);

      const result = await service.upsertSupplierConfig(
        supplierId,
        "setting",
        "default"
      );

      expect(result).toEqual({ setting: "default" });
    });
  });

  describe("getSupplier", () => {
    it("returns supplier if found", async () => {
      queryBuilder.first.mockResolvedValueOnce({
        id: supplierId,
        name: "TestCo",
      });

      const result = await service.getSupplier(supplierId);

      expect(result).toEqual({ id: supplierId, name: "TestCo" });
    });
  });

  describe("createSupplier", () => {
    it("creates a supplier and returns it", async () => {
      const input: CreateSupplierInput = {
        name: "New Supplier",
        email: "<EMAIL>",
        password: "secret",
      };

      queryBuilder.returning.mockResolvedValueOnce([
        {
          id: "new-supplier-id",
          name: "New Supplier",
          email: "<EMAIL>",
        },
      ]);

      const result = await service.createSupplier(input);

      expect(queryBuilder.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          name: "New Supplier",
          email: "<EMAIL>",
        })
      );

      expect(queryBuilder.returning).toHaveBeenCalledWith("*");

      expect(result).toEqual({
        id: "new-supplier-id",
        name: "New Supplier",
        email: "<EMAIL>",
      });
    });

    it("throws on insert failure", async () => {
      const input = {
        name: "Oops Co",
        password: "secret",
      } as CreateSupplierInput;

      queryBuilder.returning.mockRejectedValueOnce(new Error("DB error"));

      await expect(service.createSupplier(input)).rejects.toThrow(
        "Error creating supplier"
      );
    });
  });
});

console.log(
  "🚨🚨🚨 An error was expected for this test case, and it was caught successfully. 🚨🚨🚨"
);
