import { Request, Response } from "express";
import RestEndpoint from "./_restEndpoint";
import knex from "../../knex/knex";
import { getEmployees } from "../services/accessService/getEmployees";
import { getSupplierConfig } from "../services/supplierService";
import { sendNotification } from "../services/notificationService/sendNotification";
import { env } from "../config/environment";

export default class DailyBackInStockNotification extends RestEndpoint {
  public async handler(req: Request, res: Response) {
    try {
      if (env.production) {
        this.worker();
      }

      res
        .status(200)
        .send("Daily back in stock notification processing started");
    } catch (e) {
      console.error("Error starting daily back in stock notification:", e);
      res.status(500).send("Server Error");
    }
  }

  protected async worker() {
    try {
      const suppliers = await knex("supplier").select("id", "name");

      const promises = suppliers.map((supplier) =>
        this.processSupplier(supplier)
      );

      const results = await Promise.allSettled(promises);

      // Log failed suppliers for debugging
      results.forEach((result, index) => {
        if (result.status === "rejected") {
          console.error(
            `Failed to process supplier ${suppliers[index].name} (ID: ${suppliers[index].id}):`,
            result.reason
          );
        }
      });
    } catch (error) {
      console.error(
        "Critical error in daily back in stock notification worker:",
        error
      );
    }
  }

  private async processSupplier(supplier: { id: string; name: string }) {
    try {
      const supplierConfig = await getSupplierConfig(supplier.id);

      if (!supplierConfig.send_daily_back_in_stock_notification) {
        return;
      }

      // Find items that went back in stock in the last 24 hours for this supplier
      const twentyFourHoursAgo = new Date();
      twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);

      const backInStockItems = await knex("item")
        .select("id", "name", "qty_on_hand")
        .where("supplier", supplier.name)
        .where("back_in_stock_date", ">=", twentyFourHoursAgo)
        .where("back_in_stock_date", "<=", new Date())
        .where("archived", false);

      if (backInStockItems.length === 0) {
        return;
      }

      const { employees } = await getEmployees({
        filters: {
          supplierId: supplier.id,
          includeArchived: false,
        },
      });

      const targetEmployees = employees.filter((emp) =>
        emp.roles?.some(
          (role) => role.name === "Sales Rep" || role.name === "Driver"
        )
      );

      if (targetEmployees.length === 0) {
        return;
      }

      const userIds = targetEmployees.map(
        (targetEmployee) => targetEmployee.id
      );

      // Create summary notification
      const itemNames = backInStockItems.map((item) => item.name).join(", ");
      const itemCount = backInStockItems.length;

      const notification = {
        title: "New Items Back In Stock",
        subtitle: "",
        body: `${itemCount} item${
          itemCount > 1 ? "s" : ""
        } added back in stock: ${itemNames}`,
        data: {
          type: "daily_back_in_stock_summary",
          supplierId: supplier.id,
          supplierName: supplier.name,
          itemCount: itemCount,
          items: backInStockItems.map((item) => ({
            id: item.id,
            name: item.name,
            qty_on_hand: item.qty_on_hand,
          })),
        },
      };

      await sendNotification(userIds, notification);
    } catch (error) {
      console.error(
        `Error processing supplier ${supplier.name} (ID: ${supplier.id}):`,
        error
      );
      // Don't throw - let other suppliers continue processing
    }
  }
}
