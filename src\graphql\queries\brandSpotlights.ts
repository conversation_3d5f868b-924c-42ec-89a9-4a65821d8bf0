import knex from "../../../knex/knex";
import { QuerySpotlightsArgs } from "../../generated/graphql";
const BrandSpotlights = async (_, args: QuerySpotlightsArgs) => {
  const { userId } = args;
  // const { offset, limit } = pagination;
  const supplier = await knex
    .select("*")
    .from("supplier_times")
    .where("business_id", userId)
    .first();

  const spotlightsQuery = knex
    .select("*")
    .from("brand_spotlight")
    .where("supplier_id", supplier.supplier_id);

  const spotlights = await spotlightsQuery;
  return spotlights;
};

export default BrandSpotlights;
