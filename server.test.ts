import { ApolloServer } from "@apollo/server";
import http from "http";
import { AddressInfo, ListenOptions } from "net";
import request from "supertest";
import knex from "./knex/knex";
import { createServer } from "./server";
import * as ApolloServerExpress from "@apollo/server/express4";

describe("Backend Server", () => {
  let server: http.Server;
  let serverUrl: string;
  const OLD_ENV = process.env; // Save current environment

  beforeEach(() => {
    jest.resetModules(); // Most important - it clears the cache
    process.env = { ...OLD_ENV }; // Make a copy of the environment
  });

  // Before the tests we will spin up a new Apollo Server
  beforeAll(async () => {
    // We pass in the port as 0 to let the server pick its own ephemeral port for testing
    const opts: ListenOptions = { port: 0 };
    ({ server, serverUrl } = await createServer(opts));
  });

  // After the tests we will stop our server
  afterAll(async () => {
    knex.destroy();
    server.close();
    process.env = OLD_ENV; // Restore old environment
  });

  it("prints HTTP IPv4 or IPv6 URL in non-test environments", async () => {
    const consoleSpy = jest.spyOn(console, "log");
    const opts: ListenOptions = { port: 12345678 };
    const serverUrlFromAddress = (addressInfo: AddressInfo) => {
      const serverUrl = `http://${
        addressInfo.family === "IPv6"
          ? "[" + addressInfo.address + "]"
          : addressInfo.address
      }:${addressInfo.port}`;
      return serverUrl;
    };
    const mockAddressInfo: AddressInfo = {
      address: "::",
      family: "IPv6",
      port: opts.port,
    };
    const mockHttpServerAddress = jest.fn().mockReturnValue(mockAddressInfo);
    const mockHttpServerListen = jest.fn().mockImplementation((_, resolve) => {
      resolve();
      return { address: mockHttpServerAddress };
    });
    const httpServerCreateSpy = jest
      .spyOn(http, "createServer")
      .mockImplementation(
        () =>
          ({
            listen: mockHttpServerListen,
            on: jest.fn(),
          } as never)
      );
    const apolloServerStartSpy = jest
      .spyOn(ApolloServer.prototype, "start")
      .mockImplementation();
    jest
      .spyOn(ApolloServerExpress, "expressMiddleware")
      .mockReturnValue(jest.fn());
    let serverUrl = serverUrlFromAddress(mockAddressInfo);

    process.env.NODE_ENV = "some-environment";

    let serverInfo = await createServer(opts);

    expect(apolloServerStartSpy).toHaveBeenCalled();
    expect(mockHttpServerListen).toHaveBeenCalledWith(opts, expect.anything());
    expect(consoleSpy).toHaveBeenCalledWith(
      `Server listening at ${serverInfo.serverUrl}`
    );
    expect(serverInfo.serverUrl).toEqual(serverUrl);

    mockAddressInfo.family = "IPv4";
    mockAddressInfo.address = "0.0.0.0";
    serverUrl = serverUrlFromAddress(mockAddressInfo);

    serverInfo = await createServer(opts);

    expect(apolloServerStartSpy).toHaveBeenCalled();
    expect(mockHttpServerListen).toHaveBeenCalledWith(opts, expect.anything());
    expect(consoleSpy).toHaveBeenCalledWith(
      `Server listening at ${serverInfo.serverUrl}`
    );
    expect(serverInfo.serverUrl).toEqual(serverUrl);

    apolloServerStartSpy.mockRestore();
    httpServerCreateSpy.mockRestore();
  }, 30000);

  it("queries all users' ids and names", async () => {
    // Query the server for users' ids and names
    const queryData = {
      query: `query Users($getUsersInput: GetUsersInput) {
        users(getUsersInput: $getUsersInput) {
          id
          name
        }
      }`,
      variables: {
        getUsersInput: {
          ids: null,
        },
      },
    };

    // Send our request to the url of the test server
    const response = await request(`${serverUrl}/graphql`)
      .post("/")
      .send(queryData);

    // Verify expected behavior (no errors and correct data for 2 users)
    expect(response.error).toBeFalsy();
    expect(response.body.data).toEqual({
      users: [
        {
          id: "1",
          name: "test1",
        },
        {
          id: "2",
          name: "test2",
        },
      ],
    });
  });

  it("updates an order's details", async () => {
    const updateOrderInput = {
      orderName: "order 2",
      orderId: "2",
      userId: "2",
    };
    // Update an order's details on the server
    const queryData = {
      query: `mutation UpdateOrder($updateOrderInput: UpdateOrderInput) {
        updateOrder(updateOrderInput: $updateOrderInput)
      }`,
      variables: {
        updateOrderInput,
      },
    };

    // Send our request to the url of the test server
    const response = await request(`${serverUrl}/graphql`)
      .post("/")
      .send(queryData);
    const data = await knex("order_detail")
      .select("order_name")
      .where("id", updateOrderInput.orderId)
      .where("user_id", updateOrderInput.userId);

    // Verify expected behavior (no errors and "success" response)
    expect(response.error).toBeFalsy();
    expect(response.body.data).toEqual({ updateOrder: "success" });
    expect(data[0].order_name).toEqual(updateOrderInput.orderName);
  });
});
