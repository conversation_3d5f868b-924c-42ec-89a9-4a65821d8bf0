import knex from "../../../knex/knex";
import {
  GetBestSellersInput,
  GetBestSellersOutput,
} from "../../generated/graphql";

async function getBestSellers(
  getBestSellersInput: GetBestSellersInput
): Promise<GetBestSellersOutput> {
  const {
    supplier_id,
    filters,
    pagination = {},
    limit = 10,
  } = getBestSellersInput;

  const { offset = 0, limit: paginationLimit } = pagination;
  const effectiveLimit = paginationLimit || limit;

  const supplier = await knex("supplier")
    .where("id", supplier_id)
    .select("name")
    .first();

  if (!supplier) {
    throw new Error(`Supplier with id ${supplier_id} not found`);
  }

  let query = knex({ od: "order_detail" })
    .select(
      "i.name",
      "i.image",
      "i.id",
      "c.id as category_id",
      "c.name as category_name",
      "c.image as category_image"
    )
    .innerJoin({ oi: "order_item" }, "od.id", "oi.order_id")
    .innerJoin({ i: "item" }, "oi.item_id", "i.id")
    .leftJoin({ ci: "category_item" }, "i.id", "ci.item_id")
    .leftJoin({ c: "category" }, "ci.category_id", "c.id")
    .where("od.single_supplier", supplier.name)
    .andWhereNot((builder) => {
      builder
        .whereILike("status", "%canceled%")
        .orWhereILike("status", "%cancelled%");
    })
    .modify(applyAdditionalFilters, filters)
    .groupBy("i.id", "i.name", "i.image", "c.id", "c.name", "c.image")
    .sum({ quantity: "oi.quantity" })
    .orderBy("quantity", "desc");

  if (filters?.category) {
    query = query.andWhere("c.id", filters.category);
  }

  const countQuery = query.clone().clearSelect().clearGroup().clearOrder();
  countQuery.countDistinct("i.id as count");
  const totalCountResult = await countQuery;
  const totalCount = parseInt(totalCountResult[0]?.count as string) || 0;

  const bestSellersResult = await query.limit(effectiveLimit).offset(offset);

  const bestSellers = bestSellersResult.map((item) => ({
    name: item.name,
    image: item.image,
    quantity: parseInt(item.quantity),
    category: item.category_id
      ? {
          id: item.category_id.toString(),
          name: item.category_name,
          image: item.category_image,
          supplier_id: supplier_id,
        }
      : null,
  }));

  return { bestSellers, totalCount };
}

  const applyAdditionalFilters = (queryBuilder, filters) => {
    const { duration, dateRange, routeIds, driver, serviceType } =
      filters || {};

    if (duration) {
      queryBuilder.whereRaw(
        `od.date_submitted > current_date - interval '${duration}' day`
      );
    }

    if (dateRange && dateRange.length === 2) {
      queryBuilder.whereBetween("od.date_submitted", [
        dateRange[0],
        dateRange[1],
      ]);
    }

    if ((routeIds && routeIds.length) || driver || serviceType) {
      queryBuilder
        .innerJoin("attain_user", "attain_user.id", "od.user_id")
        .where((builder) => {
          if (driver) {
            // First check if there's a custom_driver on the order
            builder.where((subBuilder) => {
              subBuilder.whereRaw(`od.config->>'custom_driver' = ?`, driver);
            });
            // For orders with no custom_driver, check route_ids
            builder.orWhere((subBuilder) => {
              subBuilder
                .whereRaw(`od.config->>'custom_driver' IS NULL`)
                .andWhere((routeBuilder) => {
                  if (routeIds && routeIds.length) {
                    routeBuilder.where(
                      knex.raw(
                        `?::text[] && string_to_array(attain_user.route_id, ',')`,
                        [routeIds]
                      )
                    );
                  }
                });
            });
          } else if (routeIds && routeIds.length) {
            // If using route instead of driver, check routes
            // First check if there's a custom_route on the order
            builder.where((subBuilder) => {
              subBuilder.whereRaw(
                `od.config->>'custom_route' = ANY(?::text[])`,
                [routeIds]
              );
            });
            // For orders with no custom_route, check route_ids
            builder.orWhere((subBuilder) => {
              subBuilder
                .whereRaw(`od.config->>'custom_route' IS NULL`)
                .andWhere((routeBuilder) => {
                  if (routeIds && routeIds.length) {
                    routeBuilder.where(
                      knex.raw(
                        `?::text[] && string_to_array(attain_user.route_id, ',')`,
                        [routeIds]
                      )
                    );
                  }
                });
            });
          }
        })
        .where((builder) => {
          if (serviceType) {
            builder.whereRaw(
              "attain_user.config->>'service_type' = ?",
              serviceType
            );
          }
        });
    }
  };

export default getBestSellers;
