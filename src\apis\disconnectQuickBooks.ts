import { Request, Response } from "express";
import RestEndpoint from "./_restEndpoint";

import { ApolloServer } from "@apollo/server";
import Expo from "expo-server-sdk";
import SimpleCrypto from "simple-crypto-js";
import knex from "../../knex/knex";
export default class DisconnectQuickBooks extends RestEndpoint {
  private simpleCryptoClient: SimpleCrypto;
  constructor(
    apolloServerInit: ApolloServer,
    expoClient: Expo,
    simpleCrypto: SimpleCrypto
  ) {
    super(apolloServerInit, expoClient);
    this.simpleCryptoClient = simpleCrypto;
  }
  public async handler(req: Request, res: Response) {
    const redirectUri = await this.worker(req.query, req.params);
    if (!redirectUri)
      res.status(500).send("Error disconnecting QuickBooks connection.");
    else res.redirect(redirectUri);
  }

  protected async worker(queryParams, urlParams) {
    // console.log("QB Auth url params:", urlParams);
    // console.log("QB Auth query params:", queryParams);
    try {
      const [supplierName, supplierId, origin] = (
        this.simpleCryptoClient.decrypt(queryParams.state) as string
      ).split("/#");
      // console.log(supplierName, supplierId, origin);

      await knex("supplier")
        .update({
          qb_realm_id: null,
          qb_access_token: null,
          qb_refresh_token: null,
        })
        .where({ id: supplierId, name: supplierName });
      return `${origin}/catalog`;
    } catch (error) {
      console.error(`Error disconnecting QB:`, error);
    }
  }
}
