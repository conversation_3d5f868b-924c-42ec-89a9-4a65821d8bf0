import { Token } from "intuit-oauth-ts/src/access-token/Token";
import knex from "../../../knex/knex";
import { env } from "../../config/environment";
import { Item } from "../../generated/graphql";
import QuickBooksClient from "../../lib/quickBooks/client";
import {
  AccountRef,
  pullItemInfo,
  pullItemsInfo,
  pushItemsInfo,
} from "../../lib/quickBooks/items";
import { updateOrAddItems } from "../integrationService/algoliaOperations";
import { getSupplierConfig } from "../supplierService";

export const sync = async (
  client: QuickBooksClient,
  supplierId: string,
  supplierName: string,
  skipPull = true,
  skipPush = true,
  includeArchivedItems = false
) => {
  const itemSets = await fetchItemSets(
    client,
    supplierName,
    includeArchivedItems
  );
  console.log("Item Sets:", JSON.stringify(itemSets));

  const supplierConfig = await getSupplierConfig(supplierId);
  if (supplierConfig.skip_qb_sync) return;

  if (!skipPull) {
    await pullFromQB(itemSets);
  }
  if (!skipPush) {
    await pushToQB(client, itemSets);
  }
};

export const pullFromQB = async (itemSets: ItemSets) => {
  try {
    console.log("Inserting/updating the following items in the database:");
    console.log(JSON.stringify(itemSets.commonNewerQbItems));
    console.log(JSON.stringify(itemSets.qbOnlyItems));
    await knex.transaction(async (trx) => {
      if (itemSets.commonNewerQbItems.length > 0) {
        const updatedItems = await trx("item")
          .insert(itemSets.commonNewerQbItems)
          .onConflict("id")
          .merge()
          .returning("*")
          .transacting(trx);
        console.log(
          "[Pull from QB] Updated items:",
          JSON.stringify(updatedItems)
        );
        if (env.production) {
          await updateOrAddItems(updatedItems);
        }
      }
      if (itemSets.qbOnlyItems.length > 0) {
        const newItems = await trx("item")
          .insert(itemSets.qbOnlyItems)
          .returning("*")
          .transacting(trx);
        console.log("[Pull from QB] New items:", newItems);
        if (env.production) {
          await updateOrAddItems(newItems);
        }
      }
    });
  } catch (error) {
    console.error(error);
    const errorMessage = `Couldn't pull from QB: ${error}`;
    console.error(errorMessage);
    throw new Error(errorMessage);
  }
};

export const pushToQB = async (
  client: QuickBooksClient,
  itemSets: ItemSets
) => {
  try {
    if (!itemSets.qbItems.length) {
      throw new Error("No items in QB to mirror AccountRefs");
    }
    console.log("Inserting/updating the following items in QB:");
    console.log(JSON.stringify(itemSets.commonNewerDbItems));
    console.log(JSON.stringify(itemSets.dbOnlyItems));

    const itemsToPush = itemSets.commonNewerDbItems.concat(
      itemSets.dbOnlyItems
    );
    if (!itemsToPush.length) return;
    // Uncomment the following lines when ready to push to QB.
    // const pushedQbItems = await pushItemsInfo(
    //   client,
    //   itemsToPush,
    //   itemSets.qbItems[0]["incomeAccountRef"] as AccountRef,
    //   itemSets.qbItems[0]["expenseAccountRef"] as AccountRef,
    //   itemSets.qbItems[0]["assetAccountRef"] as AccountRef
    // );

    // const updateDBItems = pushedQbItems.map((item) => {
    //   const foundDbItem = itemSets.dbItems.find((i) => i.name === item.Name);
    //   return {
    //     id: foundDbItem.id,
    //     qb_id: item.Id,
    //     qb_sync_token: item.SyncToken,
    //     updated_at: new Date(item["MetaData"]["LastUpdatedTime"]),
    //   };
    // });

    // await knex("item").insert(updateDBItems).onConflict("id").merge();
  } catch (error) {
    console.error(error);
    const errorMessage = `Couldn't push to QB: ${error}`;
    console.error(errorMessage);
    throw new Error(errorMessage);
  }
  return;
};

const fetchItemSets = async (
  client: QuickBooksClient,
  supplierName: string,
  includeArchivedItems = false
): Promise<ItemSets> => {
  const token = (await client.refreshAccessToken()) as Token;
  if (token.access_token && token.refresh_token) {
    await knex("supplier")
      .update({
        qb_access_token: token.access_token,
        qb_refresh_token: token.refresh_token,
      })
      .where({ name: supplierName });
  } else {
    throw "Couldn't refresh QuickBooks access token";
  }
  const qbItems = (
    (await pullItemsInfo(client)) as (Item & Record<string, unknown>)[]
  ).map((item) => ({
    ...item,
    supplier: supplierName,
  }));
  const dbItemsQuery = knex<Item>("item")
    .select("*")
    .distinctOn("name")
    .where({ supplier: supplierName })
    .orderBy("name")
    .orderBy("updated_at", "desc");
  if (!includeArchivedItems) {
    dbItemsQuery.andWhere({ archived: false });
  }
  dbItemsQuery.orderBy([
    { column: "name" },
    { column: "updated_at", order: "desc" },
    { column: "id", order: "desc" },
  ]);
  const dbItems = await dbItemsQuery;
  const commonNewerQbItems = qbItems
    .filter((qbItem) =>
      dbItems.some(
        (dbItem) =>
          dbItem.name === qbItem.name && dbItem.updated_at < qbItem.updated_at
      )
    )
    .map((item) => {
      const cleanedItem = {
        ...item,
        id: dbItems.find((i) => i.name === item.name)?.id,
      };
      delete cleanedItem["incomeAccountRef"];
      delete cleanedItem["expenseAccountRef"];
      delete cleanedItem["assetAccountRef"];
      return cleanedItem;
    });
  const commonNewerDbItems = dbItems
    .filter((dbItem) =>
      qbItems.some(
        (qbItem) =>
          qbItem.name === dbItem.name && qbItem.updated_at < dbItem.updated_at
      )
    )
    .map((item) => ({
      ...item,
      qb_id: qbItems.find((i) => i.name === item.name)?.qb_id,
    }));
  const qbOnlyItems = qbItems
    .filter((qbItem) => dbItems.every((dbItem) => dbItem.name !== qbItem.name))
    .map((item) => {
      const cleanedItem = {
        ...item,
        oos: false,
        local_item: false,
        outdated: false,
        archived: false,
      };
      delete cleanedItem["incomeAccountRef"];
      delete cleanedItem["expenseAccountRef"];
      delete cleanedItem["assetAccountRef"];
      return cleanedItem;
    });
  const dbOnlyItems = dbItems
    .filter((dbItem) => qbItems.every((qbItem) => qbItem.name !== dbItem.name))
    .map((item) => {
      const cleanedItem = { ...item };
      delete cleanedItem.qb_id;
      delete cleanedItem.qb_sync_token;
      return cleanedItem;
    });

  return {
    qbItems,
    dbItems,
    commonNewerQbItems,
    commonNewerDbItems,
    qbOnlyItems,
    dbOnlyItems,
  };
};

type ItemSets = {
  qbItems: (Item & Record<string, unknown>)[];
  dbItems: Item[];
  commonNewerQbItems: Item[];
  commonNewerDbItems: Item[];
  qbOnlyItems: Item[];
  dbOnlyItems: Item[];
};

export const processQBItemUpdates = async (
  client: QuickBooksClient,
  supplierName: string,
  notification,
  includeArchivedItems = false
) => {
  try {
    const token = (await client.refreshAccessToken()) as Token;
    if (token.access_token && token.refresh_token) {
      await knex("supplier")
        .update({
          qb_access_token: token.access_token,
          qb_refresh_token: token.refresh_token,
        })
        .where({ name: supplierName });
    } else {
      throw "Couldn't refresh QuickBooks access token";
    }
    const itemIdsToCreate = notification.dataChangeEvent.entities
      .filter(
        (entity) => entity.name === "Item" && entity.operation === "Create"
      )
      .map((entity) => entity.id);
    const itemIdsToUpdate = notification.dataChangeEvent.entities
      .filter(
        (entity) => entity.name === "Item" && entity.operation === "Update"
      )
      .map((entity) => entity.id);

    const itemsToCreate: Item[] = itemIdsToCreate.length
      ? await Promise.all(itemIdsToCreate.map((id) => pullItemInfo(client, id)))
      : [];
    const itemsToUpdate: Item[] = itemIdsToUpdate.length
      ? await Promise.all(itemIdsToUpdate.map((id) => pullItemInfo(client, id)))
      : [];
    const dbCreateItemsMatchingQuery = knex("item")
      .select("id", "name", "oos")
      .whereIn(
        "name",
        itemsToCreate.map((item) => item.name)
      )
      .andWhere({ supplier: supplierName });
    if (!includeArchivedItems) {
      dbCreateItemsMatchingQuery.andWhere({ archived: false });
    }
    const dbCreateItemsMatching: Item[] = await dbCreateItemsMatchingQuery;

    itemsToCreate.forEach((item) => {
      const matchedDBItem = dbCreateItemsMatching.find(
        (di) => di.name === item.name
      );
      if (matchedDBItem && matchedDBItem.id) {
        item.id = matchedDBItem.id;
        item.oos = matchedDBItem.oos;
      } else {
        item.oos = false;
      }
      item.local_item = false;
      item.outdated = false;
      item.archived = false;
      item.supplier = supplierName;
      delete item["incomeAccountRef"];
      delete item["expenseAccountRef"];
      delete item["assetAccountRef"];
    });
    const dbUpdateItemsMatching: Item[] = await knex("item")
      .select("id", "qb_id")
      .whereIn("qb_id", itemIdsToUpdate)
      .andWhere({ supplier: supplierName });

    itemsToUpdate.forEach((item) => {
      item.id = dbUpdateItemsMatching.find(
        (dbItem) => dbItem.qb_id === item.qb_id
      ).id;
      if (item.name.indexOf("deleted") !== -1) {
        item.archived = true;
        delete item.name;
      } else {
        item.archived = false;
      }
      delete item["incomeAccountRef"];
      delete item["expenseAccountRef"];
      delete item["assetAccountRef"];
    });

    await knex.transaction(async (trx) => {
      if (itemsToCreate.length) {
        await trx("item").insert(itemsToCreate).onConflict("id").merge();
      }
      if (itemsToUpdate.length) {
        await trx("item").insert(itemsToUpdate).onConflict("id").merge();
      }
    });
  } catch (error) {
    const errorMessage = `Couldn't process QB item updates: ${error}`;
    console.error(errorMessage);
    throw new Error(errorMessage);
  }
};

export const processDBItemUpdates = async (items: Item[]) => {
  try {
    if (!items.length) return;
    const itemsBySupplier = {};
    items.forEach((item) => {
      if (!itemsBySupplier[item.supplier]) {
        itemsBySupplier[item.supplier] = [];
      }
      itemsBySupplier[item.supplier].push(item);
    });
    return (
      await Promise.all(
        Object.keys(itemsBySupplier).map(async (supplierName) => {
          const supplier = await knex("supplier")
            .select("qb_realm_id", "qb_access_token", "qb_refresh_token")
            .where({ name: supplierName })
            .first();
          if (!supplier || !supplier.qb_realm_id) return;
          const client = new QuickBooksClient(
            supplier.qb_access_token,
            supplier.qb_realm_id,
            supplier.qb_refresh_token
          );
          const token = (await client.refreshAccessToken()) as Token;
          if (token.access_token && token.refresh_token) {
            await knex("supplier")
              .update({
                qb_access_token: token.access_token,
                qb_refresh_token: token.refresh_token,
              })
              .where({ name: supplierName });
          } else {
            throw "Couldn't refresh QuickBooks access token";
          }
          const mirrorItem = (await pullItemsInfo(client, { limit: 1 }))[0];
          return await pushItemsInfo(
            client,
            itemsBySupplier[supplierName],
            mirrorItem["incomeAccountRef"],
            mirrorItem["expenseAccountRef"],
            mirrorItem["assetAccountRef"]
          );
        })
      )
    ).flat();
  } catch (error) {
    const errorMessage = `Couldn't process DB item updates: ${error}`;
    console.error(errorMessage);
    throw new Error(errorMessage);
  }
};
