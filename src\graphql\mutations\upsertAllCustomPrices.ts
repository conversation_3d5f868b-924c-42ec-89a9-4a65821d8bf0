import { MutationUpsertAllCustomPricesArgs } from "../../generated/graphql";
import { updateAllUserCustomItemPrice } from "../../services/userService/userService";

const upsertAllCustomPrices = async (
  _,
  args: MutationUpsertAllCustomPricesArgs
) => {
  const { supplierId, userId, itemId, price, overrideExistingPrices } = args;
  if (!supplierId || !itemId || !price) {
    throw new Error("Unable to update custom prices with empty values");
  }

  try {
    await updateAllUserCustomItemPrice(
      supplierId,
      itemId,
      price,
      overrideExistingPrices || false
    );
  } catch (e) {
    throw new Error("Failed to update custom prices");
  }
  return true;
};

export default upsertAllCustomPrices;
