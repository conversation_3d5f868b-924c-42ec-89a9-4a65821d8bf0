"use strict";

exports.up = function (db) {
  return db
    .createTable("activity_log", {
      id: { type: "int", primaryKey: true, autoIncrement: true },
      log_number: { type: "int", notNull: true },
      supplier_id: {
        type: "int",
        foreignKey: {
          name: "activity_log_supplier_id_fk",
          table: "supplier",
          rules: {
            onDelete: "CASCADE",
            onUpdate: "RESTRICT",
          },
          mapping: "id",
        },
      },
      user_id: {
        type: "int",
        foreignKey: {
          name: "activity_log_user_id_fk",
          table: "attain_user",
          rules: {
            onDelete: "CASCADE",
            onUpdate: "RESTRICT",
          },
          mapping: "id",
        },
      },
      employee_id: {
        type: "int",
        foreignKey: {
          name: "activity_log_employee_id_fk",
          table: "employees",
          rules: {
            onDelete: "CASCADE",
            onUpdate: "RESTRICT",
          },
          mapping: "id",
        },
      },
      type: { type: "string", notNull: true },
      metadata: { type: "jsonb" },
      location: { type: "string" },
      created_at: {
        type: "timestamp",
        notNull: true,
        defaultValue: new String("CURRENT_TIMESTAMP"),
      },
    })
    .then(() => {
      // Add indexes for better query performance
      return Promise.all([
        db.addIndex("activity_log", "activity_log_supplier_id_idx", [
          "supplier_id",
        ]),
        db.addIndex("activity_log", "activity_log_user_id_idx", ["user_id"]),
        db.addIndex("activity_log", "activity_log_employee_id_idx", [
          "employee_id",
        ]),
        db.addIndex("activity_log", "activity_log_type_idx", ["type"]),
        db.addIndex("activity_log", "activity_log_created_at_idx", [
          "created_at",
        ]),
        db.addIndex("activity_log", "activity_log_log_number_idx", [
          "log_number",
        ]),
        // Add unique constraint for log_number per supplier
        db.addIndex(
          "activity_log",
          "activity_log_supplier_log_number_unique",
          ["supplier_id", "log_number"],
          true
        ),
      ]);
    });
};

exports.down = function (db) {
  return db.dropTable("activity_log");
};

exports._meta = {
  version: 1,
};
