import knex from "../../../knex/knex";
import { MutationDeleteUsersArgs } from "../../generated/graphql";

const deleteUsers = async (_, args: MutationDeleteUsersArgs) => {
  const { userIds: usersToDelete } = args;
  const timeNow = new Date();
  await knex("attain_user")
    .update({ archived: true, updated_at: timeNow })
    .where("id", "in", usersToDelete);

  return usersToDelete;
};

export default deleteUsers;
