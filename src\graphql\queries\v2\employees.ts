import { QueryResolvers } from "../../../generated/graphql";
import { getEmployees } from "../../../services/accessService/getEmployees";

const employeesV2: QueryResolvers["employeesV2"] = async (
  _,
  { employeesInput }
) => {
  const { supplierId, filters, pagination, sortBy } = employeesInput;

  // Call the getEmployees service
  const { employees, totalCount } = await getEmployees({
    filters: {
      supplierId,
      ids: filters?.ids,
      name: filters?.name,
      email: filters?.email,
      phone: filters?.phone,
      appAccess: filters?.appAccess,
      dashboardAccess: filters?.dashboardAccess,
      roleIds: filters?.roleIds,
      includeArchived: filters?.includeArchived,
      query: filters?.query,
      createdAfter: filters?.createdAfter,
      createdBefore: filters?.createdBefore,
      lastLoginAfter: filters?.lastLoginAfter,
      lastLoginBefore: filters?.lastLoginBefore,
    },
    pagination,
    sortBy,
  });

  return {
    employees,
    totalCount,
  };
};

export default employeesV2;
