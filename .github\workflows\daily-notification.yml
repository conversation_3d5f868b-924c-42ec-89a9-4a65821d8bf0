name: Daily Back In Stock Notification

on:
  schedule:
    # Runs at 7:00 AM EST (12:00 UTC) every day
    - cron: "0 12 * * *"
  workflow_dispatch: # Allows manual trigger for testing

jobs:
  send-notification:
    runs-on: ubuntu-latest
    steps:
      - name: Call notification endpoint
        run: |
          curl -X GET \
            "https://attain-server.herokuapp.com/api/dailyBackInStockNotification" \
            --max-time 30 \
            --retry 3 \
            --retry-delay 5 \
            --fail \
            --silent \
            --show-error
        continue-on-error: true

      - name: Log completion
        run: echo "Daily notification job completed at $(date)"
