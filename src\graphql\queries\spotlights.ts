import knex from "../../../knex/knex";
import { QuerySpotlightsArgs } from "../../generated/graphql";
import { getSupplierConfig } from "../../services/supplierService";

const Spotlights = async (_, args: QuerySpotlightsArgs) => {
  const { userId } = args;
  // const { offset, limit } = pagination;

  const spotlightsQuery = knex
    .select("*")
    .from("spotlight")
    .join("supplier", "spotlight.supplier_id", "supplier.id")
    .orderBy("spotlight.id", "desc")
    .where("spotlight.id", "!=", "8");

  if (userId) {
    const suppliers = await knex("supplier_times")
      .select("*")
      .where("business_id", userId);
    const supplierIds = suppliers.map((supplier) => supplier.supplier_id);

    // Get supplier configs for all user's suppliers
    const supplierConfigs = await Promise.all(
      supplierIds.map(async (supplierId) => ({
        supplierId,
        config: await getSupplierConfig(supplierId.toString()),
      }))
    );

    // Apply spotlight filtering based on configs
    let spotlightIdsToShow: number[] = [];
    for (const { config } of supplierConfigs) {
      if (config.spotlight_ids && Array.isArray(config.spotlight_ids)) {
        spotlightIdsToShow = [...spotlightIdsToShow, ...config.spotlight_ids];
      }
    }

    // Remove duplicates and apply filter if any spotlight IDs found
    if (spotlightIdsToShow.length > 0) {
      const uniqueSpotlightIds = [...new Set(spotlightIdsToShow)];
      spotlightsQuery.whereIn("spotlight.id", uniqueSpotlightIds);
    }

    if (userId === "223") {
      spotlightsQuery.whereIn("spotlight.id", [8, 1, 2, 3, 4, 5]);
    }
  }

  const spotlights = await spotlightsQuery;
  return spotlights;
};

export default Spotlights;
