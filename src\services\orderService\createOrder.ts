import knex from "../../../knex/knex";
import dayjs from "../../util/dayjsConfig";
import { Order } from "../../generated/graphql";
import { orderItemsSubtotal } from "../../util/financials";
import {
  getApplicablePromotions,
  calculatePromotionDiscount,
  recordPromotionUsage,
} from "../promotionService";
import { getSupplierConfig } from "../supplierService";
import { getItems } from "../itemService/itemService";

export async function getNextOrderNumber(
  supplierName: string
): Promise<number> {
  // Get the highest order_number for this supplier
  const result = await knex("order_detail")
    .where("single_supplier", supplierName)
    .max("order_number as max_order_number")
    .first();

  const maxOrderNumber = result?.max_order_number;

  if (!maxOrderNumber) {
    // If no orders exist for this supplier, get their highest ID
    const idResult = await knex("order_detail")
      .where("single_supplier", supplierName)
      .max("id as max_id")
      .first();

    const maxId = idResult?.max_id;
    return maxId + 1;
  }

  return maxOrderNumber + 1;
}

export const createOrder = async ({
  supplierId,
  userId,
  orderItems,
  deliveryDate,
  order_name,
  notes,
  config = {},
  date_submitted = new Date().toUTCString(),
  status = "In Transit",
}): Promise<Order> => {
  const items = await getItems({
    supplierId,
    userId,
    filters: {
      ids: orderItems.map((item) => item.id),
    },
  });

  console.log("orderItems", orderItems);
  console.log("items", items);
  const newOrderItems = items.items.map((item) => {
    const orderItem = orderItems.find(
      (orderItem) => orderItem.id.toString() === item.id.toString()
    );
    return {
      id: item.id,
      quantity: orderItem?.quantity,
      price_purchased_at: orderItem?.price_purchased_at || item.price,
      item_uom_id: orderItem?.itemUomId || null,
    };
  });

  console.log("newOrderItems", newOrderItems);
  const subtotal = orderItemsSubtotal(newOrderItems);

  if (isNaN(subtotal)) {
    throw new Error("Subtotal is NaN");
  }

  let orderDetails, orderId;

  try {
    const supplier = (
      await knex("supplier").select("name").where("id", supplierId).first()
    ).name;
    if (!supplier) throw new Error(`Supplier with ID ${supplierId} not found`);

    // Get applicable promotions
    const applicablePromotions = await getApplicablePromotions(
      supplierId,
      userId,
      orderItems.map((item) => ({
        item_id: item.id,
        quantity: item.quantity,
        price: item.price_purchased_at,
        item_uom_id: item.itemUomId,
      }))
    );

    // Calculate total discount and collect promotion data
    let totalDiscount = 0;
    const appliedPromotions = [];

    for (const promotion of applicablePromotions) {
      const { discountAmount } = await calculatePromotionDiscount(
        supplierId,
        promotion.id,
        orderItems.map((item) => ({
          item_id: item.id,
          quantity: item.quantity,
          price: item.price_purchased_at,
        })),
        subtotal
      );

      if (discountAmount > 0) {
        totalDiscount += discountAmount;
      }
      appliedPromotions.push({
        id: promotion.id,
        discountAmount,
      });
    }

    console.log("totalDiscount", totalDiscount);
    console.log("appliedPromotions", appliedPromotions);

    // Get the next order number for this supplier
    const orderNumber = await getNextOrderNumber(supplier);

    // Get user's net terms
    const user = await knex("attain_user")
      .select("net_terms_days")
      .where("id", userId)
      .first();
    const netTermsDays = user?.net_terms_days;

    await knex.transaction(async (trx) => {
      const [orderResult] = await trx("order_detail")
        .insert({
          user_id: userId,
          status: status,
          subtotal: subtotal - totalDiscount,
          date_submitted: date_submitted,
          single_supplier: supplier,
          discount: totalDiscount,
          order_name,
          notes,
          config,
          order_number: orderNumber,
          net_terms_days: netTermsDays,
        })
        .returning("*");
      orderDetails = orderResult;
      if (deliveryDate) {
        orderDetails.delivery_date = dayjs(deliveryDate)
          .utc()
          .format("YYYY-MM-DD");
      }
      orderId = orderResult.id;

      const supplierConfig = await getSupplierConfig(supplierId);

      // Insert order status
      await trx("order_status").insert({
        order_id: orderId,
        supplier_id: supplierId,
        delivery_date: deliveryDate
          ? dayjs(deliveryDate).utc().format("YYYY-MM-DD")
          : supplierConfig.auto_set_delivery_date
          ? dayjs().utc().format("YYYY-MM-DD")
          : null,
      });
      const orderItemsToInsert = newOrderItems.map((item) => ({
        order_id: orderId,
        item_id: item.id,
        quantity: item.quantity,
        price_purchased_at: item.price_purchased_at,
        item_uom_id: item.item_uom_id,
      }));
      // Bulk insert order items
      await trx("order_item").insert(orderItemsToInsert);

      // Record promotion usage
      if (appliedPromotions.length > 0) {
        await recordPromotionUsage(appliedPromotions, orderId, userId, trx);
      }
    });

    // Rename single_supplier to supplier
    const { single_supplier, ...rest } = orderDetails;
    return { ...rest, supplier: single_supplier };
  } catch (error) {
    const errorMessage = `Error creating order for user ${userId} and supplier ${supplierId}: ${error}`;
    console.error(errorMessage, error);
    throw new Error(errorMessage);
  }
};

// These newly created orders default to Delivered status
export const createOrderFromInvoice = async (
  invoiceId: string
): Promise<Order> => {
  try {
    // Get the invoice details
    const invoice = await knex("invoice")
      .select("*")
      .where("id", invoiceId)
      .first();

    if (!invoice) {
      throw new Error(`Invoice with ID ${invoiceId} not found`);
    }

    if (invoice.order_id) {
      throw new Error(`Invoice ${invoiceId} already has an order`);
    }

    // Get supplier name for order number generation
    const supplier = await knex("supplier")
      .select("name")
      .where("id", invoice.supplier_id)
      .first();

    const orderNumber = await getNextOrderNumber(supplier);

    // Get the invoice items
    const invoiceItems = await knex("invoice_item")
      .select("*")
      .where("invoice_id", invoiceId);

    if (!invoiceItems || invoiceItems.length === 0) {
      throw new Error(`No items found for invoice ${invoiceId}`);
    }

    // Map invoice items to order items format
    const orderItems = invoiceItems.map((item) => ({
      id: item.item_id,
      quantity: item.quantity,
      price_purchased_at: item.price,
      item_uom_id: item.item_uom_id,
    }));

    // Create the order
    const order = await createOrder({
      supplierId: invoice.supplier_id,
      userId: invoice.user_id,
      orderItems,
      deliveryDate: invoice.date_created
        ? dayjs(invoice.date_created).toDate()
        : undefined,
      order_name: `Order from Invoice #${invoiceId}`,
      notes: invoice.notes,
      config: invoice.config,
      date_submitted: invoice.date_created
        ? dayjs(invoice.date_created).toDate().toUTCString()
        : undefined,
      status: "Delivered",
    });

    // Update the invoice with the order ID and order_number
    await knex("invoice").where("id", invoiceId).update({
      order_id: order.id,
      order_number: orderNumber,
      updated_at: dayjs().toISOString(),
    });

    return order;
  } catch (error) {
    const errorMessage = `Error creating order from invoice ${invoiceId}: ${error}`;
    console.error(errorMessage, error);
    throw new Error(errorMessage);
  }
};
