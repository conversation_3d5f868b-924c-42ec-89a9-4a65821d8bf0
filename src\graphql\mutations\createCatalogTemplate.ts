import knex from "../../../knex/knex";
import { MutationCreateCatalogTemplateArgs } from "../../generated/graphql";
import {
  createCatalogTemplate as createCatalogTemplateService,
  CatalogTemplateConfig,
} from "../../services/catalogTemplateService";

const createCatalogTemplate = async (
  _,
  args: MutationCreateCatalogTemplateArgs
) => {
  const { supplier_id, name, config } = args.input;
  const trx = await knex.transaction();

  try {
    const result = await createCatalogTemplateService(trx, {
      supplier_id,
      name,
      config: config as CatalogTemplateConfig,
    });
    await trx.commit();
    return result;
  } catch (error) {
    await trx.rollback();
    throw error;
  }
};

export default createCatalogTemplate;
