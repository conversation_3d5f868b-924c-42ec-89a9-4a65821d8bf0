import { <PERSON><PERSON> } from "knex";
import knex from "../../../knex/knex";
import { InvoiceItem, InvoiceItemInput } from "../../generated/graphql";
import { getQbIds } from "../integrationService/quickbooksService";
const addInvoiceItems = async (
  invoiceId: string | undefined | null,
  items: InvoiceItemInput[],
  isReturning = false,
  trxProvider?: Knex.TransactionProvider | undefined | null
) => {
  const trxOrQuery = trxProvider
    ? (await trxProvider())("invoice_item")
    : knex("invoice_item");
  const qb_ids = await getQbIds(items.map((item) => item.item_id.toString()));
  const updatedItems = items.map((item) => ({
    ...item,
    qb_id: item.qb_id ?? item.item_id ? qb_ids.get(item.item_id) ?? null : null,
  }));

  const invoiceItems: InvoiceItem[] = await trxOrQuery
    .insert(
      invoiceId
        ? updatedItems.map((item) => ({
            ...item,
            invoice_id: invoiceId,
          }))
        : updatedItems
    )
    .returning(isReturning ? "*" : null);
  return invoiceItems;
};

export default addInvoiceItems;
