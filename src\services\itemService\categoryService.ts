import knex from "../../../knex/knex";
import { Category } from "../../generated/graphql";

/**
 * Get all categories for a supplier, ordered by the ordering field
 */
export const getCategories = async (
  supplierId: string,
  fillItems = false
): Promise<Category[]> => {
  const categoriesQuery = knex("category")
    .leftJoin("category_item", "category.id", "category_item.category_id")
    .groupBy("category.id")
    .where("supplier_id", supplierId);
  if (fillItems) {
    categoriesQuery
      .leftJoin("item", "category_item.item_id", "item.id")
      .select("category.*", knex.raw("json_agg(item.*) as items"));
  } else {
    categoriesQuery.select(
      "category.*",
      knex.raw(
        "json_agg(json_build_object('id', category_item.item_id)) as items"
      )
    );
  }

  const categories = await categoriesQuery;

  categories.forEach((category) => {
    if (category.items) {
      category.items = category.items.filter((item) => item.id);
    }
  });

  return categories;
};

/**
 * Update the ordering of a single category
 */
export const updateCategoryOrder = async (
  supplierId: number,
  categoryId: number,
  newOrder: number
): Promise<void> => {
  await knex("category")
    .where({ id: categoryId, supplier_id: supplierId })
    .update({ ordering: newOrder });
};

/**
 * Reorder multiple categories at once
 * @param supplierId - The ID of the supplier
 * @param categoryOrders - Array of category IDs in their new order
 */
export const reorderCategories = async (
  supplierId: string,
  categoryOrders: string[]
): Promise<void> => {
  // Start transaction to ensure all updates succeed or none do
  await knex.transaction(async (trx) => {
    const updates = categoryOrders.map((categoryId, index) => {
      return trx("category")
        .where({ id: categoryId, supplier_id: supplierId })
        .update({ ordering: (index + 1) * 10 });
    });

    await Promise.all(updates);
  });
};

/**
 * Insert a new category with proper ordering
 */
export const createCategory = async (
  supplierId: number,
  name: string,
  image?: string
): Promise<Category> => {
  // Get the highest current ordering value
  const maxOrder = await knex("category")
    .where("supplier_id", supplierId)
    .max("ordering as max")
    .first();

  // Add new category with ordering value higher than the current maximum
  const newOrder = (maxOrder?.max || 0) + 10;

  const [category] = await knex("category")
    .insert({
      name,
      supplier_id: supplierId,
      image,
      ordering: newOrder,
    })
    .returning("*");

  return category;
};
