import QuickBooks from "node-quickbooks";
import { env } from "../../config/environment";

class QuickBooksClient extends QuickBooks {
  constructor(accessToken: string, realmId: string, refreshToken: string) {
    const clientId = env.production
      ? process.env.QUICKBOOKS_CLIENT_ID
      : process.env.QUICKBOOKS_CLIENT_ID_SANDBOX;
    const clientSecret = env.production
      ? process.env.QUICKBOOKS_CLIENT_SECRET
      : process.env.QUICKBOOKS_CLIENT_SECRET_SANDBOX;
    const isSandbox = !env.production;
    super(
      clientId,
      clientSecret,
      accessToken,
      false,
      realmId,
      isSandbox,
      false,
      null,
      "2.0",
      refreshToken
    );
  }

  async refreshAccessToken() {
    return new Promise((resolve, reject) => {
      super.refreshAccessToken((error, refreshResponse) => {
        if (error || refreshResponse.error) {
          reject(refreshResponse.error || error.fault || error.Fault);
        }
        resolve(refreshResponse);
      });
    });
  }

  async getItem(id: string) {
    return new Promise((resolve, reject) => {
      super.getItem(id, (error, item) => {
        if (error) {
          reject(error.fault || error.Fault);
        }
        resolve(item);
      });
    });
  }
  async findItems(criteria?: string | unknown) {
    return new Promise((resolve, reject) => {
      criteria
        ? super.findItems(criteria, (error, itemsResponse) => {
            if (error) {
              reject(error.fault || error.Fault);
            }
            resolve(itemsResponse.QueryResponse.Item);
          })
        : super.findItems((error, itemsResponse) => {
            if (error) {
              reject(error.fault || error.Fault);
            }
            resolve(itemsResponse.QueryResponse.Item);
          });
    });
  }
  async updateItem(item: { Id: string; SyncToken: string } & unknown) {
    return new Promise((resolve, reject) => {
      super.updateItem(item, (error, updatedItem) => {
        if (error) {
          reject(error.fault || error.Fault);
        }
        resolve(updatedItem);
      });
    });
  }
  async createItem(item: { Name: string; UnitPrice: number } & unknown) {
    return new Promise((resolve, reject) => {
      super.createItem(item, (error, updatedItem) => {
        if (error) {
          reject(error.fault || error.Fault);
        }
        resolve(updatedItem);
      });
    });
  }
  async findCustomers(criteria?: string | unknown) {
    return new Promise((resolve, reject) => {
      criteria
        ? super.findCustomers(criteria, (error, customersResponse) => {
            if (error) {
              reject(error.fault || error.Fault);
            }
            resolve(customersResponse.QueryResponse.Customer);
          })
        : super.findCustomers((error, customersResponse) => {
            if (error) {
              reject(error.fault || error.Fault);
            }
            resolve(customersResponse.QueryResponse.Customer);
          });
    });
  }
  async findInvoices(criteria?: string | unknown) {
    return new Promise((resolve, reject) => {
      criteria
        ? super.findInvoices(criteria, (error, invoicesResponse) => {
            if (error) {
              reject(error.fault || error.Fault);
            }
            resolve(invoicesResponse.QueryResponse.Invoice);
          })
        : super.findInvoices((error, invoicesResponse) => {
            if (error) {
              reject(error.fault || error.Fault);
            }
            resolve(invoicesResponse.QueryResponse.Invoice);
          });
    });
  }
}

export default QuickBooksClient;
