import { queryBuilder } from "../../../../knex/mock";

const knexMock = jest.fn(() => queryBuilder);
Object.assign(knexMock, queryBuilder);

jest.mock("../../../../knex/knex", () => ({
  __esModule: true,
  default: knexMock,
}));

import {
  toggleFavoriteItem,
  addFavoriteStatusToItems,
  getFavoriteIds,
  getFavoriteItems,
} from "../index";

describe("Favorite Item Service", () => {
  // Suppress console.error output during error tests
  let consoleErrorSpy: jest.SpyInstance;

  beforeAll(() => {
    consoleErrorSpy = jest.spyOn(console, "error").mockImplementation(() => {});
  });

  afterAll(() => {
    consoleErrorSpy.mockRestore();
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("toggleFavoriteItem", () => {
    const userId = 1;
    const itemId = 101;

    it("should add an item to favorites if it is not already favorited", async () => {
      queryBuilder.first.mockResolvedValueOnce(undefined);
      (queryBuilder.insert as jest.Mock).mockResolvedValueOnce([]);

      const result = await toggleFavoriteItem(userId, itemId);

      expect(knexMock).toHaveBeenCalledWith("user_item_favorites");
      expect(queryBuilder.insert).toHaveBeenCalledWith({
        user_id: userId,
        item_id: itemId,
        employee_id: null,
      });
      expect(result).toEqual({ success: true, isFavorited: true });
    });

    it("should remove an item from favorites if it is already favorited", async () => {
      const existingFavorite = { id: 5, user_id: userId, item_id: itemId };
      queryBuilder.first.mockResolvedValueOnce(existingFavorite);

      const result = await toggleFavoriteItem(userId, itemId);

      expect(knexMock).toHaveBeenCalledWith("user_item_favorites");
      expect(queryBuilder.where).toHaveBeenCalledWith({
        id: existingFavorite.id,
      });
      expect((queryBuilder as any).delete).toHaveBeenCalled();
      expect(result).toEqual({ success: true, isFavorited: false });
    });

    it("should return a failure response on database error", async () => {
      const dbError = new Error("DB connection failed");
      queryBuilder.first.mockRejectedValueOnce(dbError);

      const result = await toggleFavoriteItem(userId, itemId);

      expect(console.error).toHaveBeenCalledWith(
        "Error toggling favorite:",
        dbError
      );
      expect(result).toEqual({
        success: false,
        isFavorited: false,
        message: "Failed to toggle favorite",
      });
    });
  });

  describe("getFavoriteItems", () => {
    it("should fetch a user's favorite items with correct query parameters", async () => {
      const userId = 1;
      const mockFavorites = [
        { id: 101, name: "Favorite Item 1", favorited_at: new Date() },
        { id: 102, name: "Favorite Item 2", favorited_at: new Date() },
      ];
      queryBuilder.offset.mockResolvedValueOnce(mockFavorites);

      const result = await getFavoriteItems(userId, undefined, 5, 10);

      expect(knexMock).toHaveBeenCalledWith("user_item_favorites");
      expect(queryBuilder.join).toHaveBeenCalledWith(
        "item",
        "user_item_favorites.item_id",
        "item.id"
      );
      expect(queryBuilder.where).toHaveBeenCalledWith(
        "user_item_favorites.user_id",
        userId
      );
      expect(queryBuilder.where).toHaveBeenCalledWith("item.archived", false);
      expect(queryBuilder.select).toHaveBeenCalledWith(
        "item.*",
        "user_item_favorites.created_at as favorited_at"
      );
      expect(queryBuilder.orderBy).toHaveBeenCalledWith(
        "user_item_favorites.created_at",
        "desc"
      );
      expect(queryBuilder.limit).toHaveBeenCalledWith(10);
      expect(queryBuilder.offset).toHaveBeenCalledWith(5);
      expect(result).toEqual(mockFavorites);
    });
  });

  describe("getFavoriteIds", () => {
    it("should return an array of favorite item IDs for a user", async () => {
      const userId = 1;
      const mockDbResponse = [
        { item_id: 101 },
        { item_id: 102 },
        { item_id: 105 },
      ];

      queryBuilder.then.mockImplementationOnce((resolve) =>
        resolve(mockDbResponse)
      );

      const result = await getFavoriteIds(userId);

      expect(knexMock).toHaveBeenCalledWith("user_item_favorites");
      expect(queryBuilder.where).toHaveBeenCalledWith("user_id", userId);
      expect(queryBuilder.select).toHaveBeenCalledWith("item_id");
      expect(result).toEqual([101, 102, 105]);
    });
  });

  describe("addFavoriteStatusToItems", () => {
    const userId = 1;
    const items = [
      { id: 101, name: "Item A" },
      { id: 102, name: "Item B" },
      { id: 103, name: "Item C" },
    ];

    it("should correctly add 'isFavorited' status to a list of items", async () => {
      const favoriteIdsFromDb = [{ item_id: 101 }, { item_id: 103 }];

      queryBuilder.then.mockImplementationOnce((resolve) =>
        resolve(favoriteIdsFromDb)
      );

      const result = await addFavoriteStatusToItems(items, userId);

      expect(result).toEqual([
        { id: 101, name: "Item A", isFavorited: true },
        { id: 102, name: "Item B", isFavorited: false },
        { id: 103, name: "Item C", isFavorited: true },
      ]);
      expect(knexMock).toHaveBeenCalledWith("user_item_favorites");
    });

    it("should return items unchanged if the items array is empty", async () => {
      const result = await addFavoriteStatusToItems([], userId);
      expect(result).toEqual([]);
      expect(knexMock).not.toHaveBeenCalled();
    });

    it("should return items unchanged if userId is not provided", async () => {
      const result = await addFavoriteStatusToItems(items, 0);
      expect(result).toEqual(items);
      expect(knexMock).not.toHaveBeenCalled();
    });
  });
});
