import knex from "../../../knex/knex";
import { QuerySuppliersArgs, Supplier } from "../../generated/graphql";

const Suppliers = async (_, args: QuerySuppliersArgs) => {
  const { ids, pagination } = args.getSuppliersInput;
  const { offset, limit } = pagination ?? { offset: 0, limit: 10 };
  let suppliers: Supplier[];
  if (!ids) {
    suppliers = await knex("supplier").select("*").limit(limit).offset(offset);
  } else {
    suppliers = await knex("supplier").select("*").whereIn("id", ids);
  }
  return suppliers;
};

export default Suppliers;
