import knex from "../../../knex/knex";
import {
  Account,
  MutationUpdateDefaultInAccountArgs,
} from "../../generated/graphql";

const updateDefaultInAccount = async (
  _,
  args: MutationUpdateDefaultInAccountArgs
) => {
  const { isDefault, accountId, userId } = args.updateDefaultInAccountInput;

  if (isDefault === true) {
    //update selected card to be default
    await knex("account")
      .update("is_default", isDefault)
      .where("user_id", userId)
      .where("id", accountId);
    //Update every other row to be false
    await knex("account")
      .update("is_default", false)
      .where("user_id", userId)
      .whereNot("id", accountId);
  }

  const res: Account[] = await knex("account")
    .select("*")
    .where("user_id", userId);
  return res;
};

export default updateDefaultInAccount;
