# Access Service

Access service is a way for us to manage authentication and authorization. We have two UI's that need managed access: the dashboard and app.
We'd like this management to be accessible in the dashboard so the suppliers can control functionality for certain users.

## Logins

The dashboard and app use different services for authentication. The dashboard uses auth0 and the app uses firebase.

## Roles

- Admin
- Sales Rep
- Driver

# Tables

Employee

- id, name, phone, email, created_at, updated_at, last_login, archived

Roles

- id, name, description, created_at, updated_at

Role Assignment

- employee_id, role_id, assigned_at
