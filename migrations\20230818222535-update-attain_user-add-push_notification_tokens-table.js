"use strict";

var dbm;
var type;
var seed;

/**
 * We receive the dbmigrate dependency from dbmigrate initially.
 * This enables us to not have to rely on NODE_PATH.
 */
exports.setup = function (options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

exports.up = function (db) {
  return db.runSql("ALTER TABLE attain_user ADD PRIMARY KEY (id);").then(() => {
    db.createTable("push_notification_tokens", {
      user_id: {
        type: "serial",
        foreignKey: {
          name: "fk_push_notification_tokens_to_attain_user",
          table: "attain_user",
          mapping: "id",
          rules: {},
        },
      },
      token: {
        type: "string",
        length: 255,
        notNull: true,
        unique: true,
      },
    });
  });
};

exports.down = function (db) {
  return db.dropTable("push_notification_tokens").then(() => {
    db.runSql("ALTER TABLE attain_user DROP CONSTRAINT attain_user_pkey;");
  });
};

exports._meta = {
  version: 1,
};
